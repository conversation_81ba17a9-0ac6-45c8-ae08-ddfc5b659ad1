import { defineConfig } from 'orval';

export default defineConfig({
  contextclue: {
    // input: 'http://localhost:8580/api/v1/openapi.json', // dev
    input: 'https://app.context-clue.com/api/v1/openapi.json', // prod
    output: {
      target: './src/api/',
      client: 'react-query',
      mode: 'tags-split',
      override: {
        mutator: {
          path: './src/config/mutator.ts',
          name: 'apiUrlMutator',
        },
      },
    },
  },
});