<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <title>Giq Studio - app for BMW</title>

    <style>
      body,
      html {
        margin: 0;
        padding: 0;
        width: 100vw;
        height: 100%;
      }
      canvas {
        display: block;
        touch-action: none;
      }
      canvas:focus {
        outline: none;
      }
      .btn {
        color: white;
        position: fixed;
        bottom: 10px;
        right: 10px;
        cursor: pointer;
      }
      #btn_sample_1 {
        right: 200px;
      }
      #btn_sample_2 {
        right: 100px;
      }
      #filter {
        bottom: 50px;
      }
    </style>

    <script type="module" src="./app.js"></script>
  </head>
  <body>
    <div class="btn" id="btn_sample_1">Sample 1</div>
    <div id="btn_sample_2" class="btn">Sample 2</div>
    <div id="btn_sample_3" class="btn">Sample 3</div>
    <div id="filter" class="btn">Filter</div>

    <script>
      /* communication with app example */

      fetch("./sample_1.json")
        .then((resp) => resp.text())
        .then((data) => {
          document
            .getElementById("btn_sample_1")
            .addEventListener("click", (e) => {
              console.log("click 1");
              window.wasm_app.new_graph_data(data);
            });
        })
        .catch((e) => {
          console.log("failed to load sample 1");
        });

      fetch("./sample_2.json")
        .then((resp) => resp.text())
        .then((data) => {
          document
            .getElementById("btn_sample_2")
            .addEventListener("click", (e) => {
              console.log("click 2");
              window.wasm_app.new_graph_data(data);
            });
        })
        .catch((e) => {
          console.log("failed to load sample 2");
        });

      fetch("./sample_3.json")
        .then((resp) => resp.text())
        .then((data) => {
          document
            .getElementById("btn_sample_3")
            .addEventListener("click", (e) => {
              console.log("click 3");
              window.wasm_app.new_graph_data(data);
            });
        })
        .catch((e) => {
          console.log("failed to load sample 3");
        });

      document
        .getElementById("filter")
        .addEventListener("click", (e) => {
          console.log("filter click");
          window.wasm_app.set_filter("entity_id", ["07c5b78c-219e-443b-8232-589c43d7019b"]);
        });

      /*setInterval(() => {
        if (window.wasm_app) {
          window.wasm_app.send_info_message(
            "Hello from JavaScript at " + new Date().toLocaleTimeString()
          );
        } else {
          console.log("fail");
        }
      }, 3000);*/
      setInterval(() => {
          let clicked = window.wasm_app.get_selected_entities();
          if (clicked.length > 0) {
            console.log("selected elements: " + clicked);
          }

          let root_element = window.wasm_app.get_root_entity();
          if (root_element) {
            console.log("root element: " + root_element);
          }
      }, 250);

      /*
        let show_values_for_entities = window.wasm_app.get_show_values_for();
        window.wasm_app.set_show_values_for(["07c5b78c-219e-443b-8232-589c43d7019b"]);

        let show_children_for_entities = window.wasm_app.get_show_children_for();
        window.wasm_app.set_show_children_for(["07c5b78c-219e-443b-8232-589c43d7019b"]);

        window.wasm_app.change_view("position_graph");
        window.wasm_app.change_view("dependency_graph");

        window.wasm_app.change_color_feature("entity_type");
      */
    </script>
  </body>
</html>
