## Run

to run webassembly app you need a webserwer, for local you can use `miniserve` (rust):

```sh
cargo install miniserve

miniserve ./webassembly/giq-studio-app-bmw --index index.html -p 8181
```

or `http.server` (phython):

```sh
cd webassembly/giq-studio-app-bmw
python3 -m http.server 8181
```

## Usage in html

## default

See `webassembly/giq-studio-app-bmw/index.html` file.

For minimal example you need `app.js` file which loads files `giq_studio_app_bmw.js` and `giq_studio_app_bmw.wasm`;

then in your html file:

```html
<script type="module" src="./app.js"></script>
```

this will create `<canvas></canvas>` with app in `<body>` tag.

## custom

### embed

The easiest way to run app in your custom html code is to use the `<embed></embed>` tag like this:

```html
<div>
  <embed
    type="text/html"
    src="webassembly/giq-studio-app-bmw/index.html"
    width="1000"
    height="1000"
  />
</div>
```

### iframe

If default app is actually hosted you can use iframe:

```html
<iframe src="http://127.0.0.1:8181/" style="width: 100%;height: 100%;"></iframe>
```

## App <-> Js communication

See `webassembly/giq-studio-app-bmw/use_example.html` file.

Once the app is loaded you can use `document.wasm_app` object to communicate with application.

### functions

#### send_info_message(String)

Print sended message in console. Can by used to test comunication.

```js
window.wasm_app.send_info_message("Hello!");
```

#### new_graph_data(String)

Force reload graph with new data. Argument should be json file content string.

```js
window.wasm_app.new_graph_data(json_data_file_content_string);
```

#### set_filter(String, Array[String])

Set filters. **Filters are resets when new data is loaded**. Use after `new_graph_data` function.

```js
 window.wasm_app.set_filter("entity_id", ["my-entity-id-1", "my-entity-id-2"]);
```

#### get_selected_entities()

Return array with string `entity_id` for clicked nodes.

```js
let clicked = window.wasm_app.get_selected_entities();
```

#### get_root_entity()

Return string `entity_id` for entity choosen as root if subgraph is selected otherwise undefined.

```js
let clicked = window.wasm_app.get_root_entity();
```

#### get_show_children_for()

Get entity_id for nodes with open children.

```js
let show_children_for_entities = window.wasm_app.get_show_children_for();
```

#### set_show_children_for(Array[String])

Set the entity for which children will be shown. This function overwrites previous settings.

```js
window.wasm_app.set_show_children_for(["my-entity-id-1", "my-entity-id-2"]);
```

#### get_show_values_for()

Get entity_id for nodes with open values.

```js
let show_values_for_entities = window.wasm_app.get_show_values_for();
```

#### set_show_values_for(Array[String])

Set the entity for which values will be shown. This function overwrites previous settings.

```js
window.wasm_app.set_show_values_for(["my-entity-id-1", "my-entity-id-2"]);
```

#### change_view(String)

Change view. Available options:
- position_graph
- dependency_graph

```js
window.wasm_app.change_view("position_graph");
```

#### change_color_feature(String)

Change feature used to color nodes.

```js
window.wasm_app.change_color_feature("entity_type");
```
