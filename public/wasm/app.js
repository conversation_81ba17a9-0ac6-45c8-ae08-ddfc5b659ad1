import init, {
    AppCommunicationResource,
    wasm_app_run,
  } from "./giq_studio_app_bmw.js";

  init()
    .then(() => {
      window.wasm_app = new AppCommunicationResource();

      try {
        wasm_app_run(window.wasm_app);
      } catch (e) {
        if (!e.message.startsWith("Using exceptions for control flow")) {
          throw e;
        }
      }
    })
    .catch((e) => {
      console.log("WASM load fail");
    });
