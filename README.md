# IMPORTANT

```bash
docker build -t <IMAGE_NAME>:<VERSION> .
docker run -p 3000:3000 <IMAGE_NAME>:<VERSION>
```

## Context Clue Application

This project uses `node v22.2.0` and `npm v10.9.0`. Remember to keep these versions up to date with the Dockerfiles!

## API Generation with Orval

This project uses [Orval](https://orval.dev/) to generate TypeScript API clients from OpenAPI specifications. The API client code is automatically generated in the `src/api/` directory.

### Regenerating the API Client

When the backend API changes, you need to regenerate the API client:

1. Ensure the backend server is running locally (default: http://localhost:8580)
2. Run the following command:

```bash
npm run generate-api
```

This command will fetch the OpenAPI specification from the backend and regenerate all API client code. The configuration for Orval is in `orval.config.ts`.

## Docker Compose

Docker Compose is required due to the git hook declared in `.husky`.

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/api-reference/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

This project uses the Next.js App Router.

## Libraries Used in This Project

- [Tailwind CSS](https://tailwindcss.com/) - For CSS utilities
- [Shadcn.ui](https://ui.shadcn.com/) - For UI components
- [NextAuth](https://next-auth.js.org/) - For OAuth + Login/Password Auth
- [TanStack Query](https://tanstack.com/query) - For client components API management
- [Date-Fns](https://date-fns.org/) - For date management
- Client/Server-Only - Guard
- [Husky](https://typicode.github.io/husky) - Git Hooks
- [Prettier](https://prettier.io/) - Code Quality

Have fun! :-)
