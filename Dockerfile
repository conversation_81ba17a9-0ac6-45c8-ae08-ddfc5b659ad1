# Stage 1: Build the Next.js application
FROM node:22-alpine AS base
WORKDIR /app
COPY package*.json ./
EXPOSE 3000
RUN npm ci

########## BUILDER
FROM base as builder
WORKDIR /app
COPY . .
RUN npm run build



########## PRODUCTION

FROM base as production
WORKDIR /app
ENV NODE_ENV=production
# RUN npm ci
# RUN addgroup -g 1001 -S nodejs
# RUN adduser -S nextjs -u 1001
# USER nextjs
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/public ./public
CMD npm start

########## DEV
# Set environment variables
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

FROM base as dev
ENV NODE_ENV=development
RUN npm install 
COPY . .
CMD npm run dev