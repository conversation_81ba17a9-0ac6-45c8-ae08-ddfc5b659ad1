{"name": "context-clue", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky", "generate-api": "rm -rf src/api/* && dotenv -e .env.local -- orval"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.3", "@react-pdf/renderer": "^4.3.0", "@sentry/nextjs": "^9", "@sentry/react": "^9.1.0", "@sentry/tracing": "^7.120.3", "@tanstack/react-query": "^5.59.20", "@types/js-cookie": "^3.0.6", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "immer": "^10.1.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.456.0", "marked": "^15.0.7", "next": "15.0.3", "next-auth": "^4.24.10", "next-themes": "^0.4.3", "react": "^18", "react-dom": "^18", "react-force-graph-2d": "^1.27.0", "react-hook-form": "^7.53.2", "react-icons": "^5.4.0", "react-toastify": "^11.0.5", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.2", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "dotenv-cli": "^8.0.0", "eslint": "^9", "eslint-config-next": "15.0.3", "husky": "^9.1.7", "orval": "^7.9.0", "postcss": "^8", "prettier": "^3.4.2", "tailwindcss": "^3.4.14", "typescript": "^5"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}