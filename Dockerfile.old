# Stage 1: Build the Next.js application
FROM node:22-alpine AS builder

# Set the working directory
WORKDIR /app

# Copy the package.json and package-lock.json for installing dependencies
COPY package*.json ./

# Install dependencies (including dev dependencies)
RUN npm install

# Copy the entire application code to the container
COPY . .

# Build the Next.js app with the standalone output
RUN npm run build

# Stage 2: Production image
FROM node:22-alpine

# Set working directory for the final image
WORKDIR /app

# Copy the built application files from the builder stage
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/public ./public

# Set environment variables
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Expose the application port
EXPOSE 3000

# Run the Next.js application
CMD ["node", "server.js"]