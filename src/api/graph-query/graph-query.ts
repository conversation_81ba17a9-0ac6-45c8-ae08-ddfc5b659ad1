/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation,
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import type {
  BodyCreateNode,
  CopyNodeToCollectionParams,
  CreateNodeParams,
  CreateRelation200,
  CreateRelationBody,
  CreateRelationParams,
  DeleteNodeParams,
  DeleteRelation200,
  DeleteRelationParams,
  GetGraphParams,
  GetGraphView200,
  GetGraphViewParams,
  GetNodeParams,
  GetObjectsGraphParams,
  GraphNodeModel,
  GraphResponse,
  HTTPValidationError,
  UpdateObjectNode200,
  UpdateObjectNodeBody,
  UpdateObjectNodeParams
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * Lists all available collections

:return: List of collection names
 * @summary List Collections
 */
export const listCollections = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<string[]>(
    {
      url: `/graph_query/list_collections`, method: 'GET', signal
    },
  );
}


export const getListCollectionsQueryKey = () => {
  return [`/graph_query/list_collections`] as const;
}


export const getListCollectionsQueryOptions = <TData = Awaited<ReturnType<typeof listCollections>>, TError = unknown>(options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listCollections>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getListCollectionsQueryKey();



  const queryFn: QueryFunction<Awaited<ReturnType<typeof listCollections>>> = ({ signal }) => listCollections(signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof listCollections>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type ListCollectionsQueryResult = NonNullable<Awaited<ReturnType<typeof listCollections>>>
export type ListCollectionsQueryError = unknown


export function useListCollections<TData = Awaited<ReturnType<typeof listCollections>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof listCollections>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof listCollections>>,
        TError,
        Awaited<ReturnType<typeof listCollections>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListCollections<TData = Awaited<ReturnType<typeof listCollections>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listCollections>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof listCollections>>,
        TError,
        Awaited<ReturnType<typeof listCollections>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListCollections<TData = Awaited<ReturnType<typeof listCollections>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listCollections>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary List Collections
 */

export function useListCollections<TData = Awaited<ReturnType<typeof listCollections>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listCollections>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getListCollectionsQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * Get objects graph from a specific collection.

:param collection: The collection to retrieve nodes from (default: None, which uses the default collection)
:return: dict with nodes and edges
 * @summary Get Objects Graph
 */
export const getObjectsGraph = (
  params?: GetObjectsGraphParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<GraphResponse>(
    {
      url: `/graph_query/get_objects_graph`, method: 'GET',
      params, signal
    },
  );
}


export const getGetObjectsGraphQueryKey = (params?: GetObjectsGraphParams,) => {
  return [`/graph_query/get_objects_graph`, ...(params ? [params] : [])] as const;
}


export const getGetObjectsGraphQueryOptions = <TData = Awaited<ReturnType<typeof getObjectsGraph>>, TError = HTTPValidationError>(params?: GetObjectsGraphParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getObjectsGraph>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetObjectsGraphQueryKey(params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof getObjectsGraph>>> = ({ signal }) => getObjectsGraph(params, signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getObjectsGraph>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type GetObjectsGraphQueryResult = NonNullable<Awaited<ReturnType<typeof getObjectsGraph>>>
export type GetObjectsGraphQueryError = HTTPValidationError


export function useGetObjectsGraph<TData = Awaited<ReturnType<typeof getObjectsGraph>>, TError = HTTPValidationError>(
  params: undefined | GetObjectsGraphParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getObjectsGraph>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getObjectsGraph>>,
        TError,
        Awaited<ReturnType<typeof getObjectsGraph>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetObjectsGraph<TData = Awaited<ReturnType<typeof getObjectsGraph>>, TError = HTTPValidationError>(
  params?: GetObjectsGraphParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getObjectsGraph>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getObjectsGraph>>,
        TError,
        Awaited<ReturnType<typeof getObjectsGraph>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetObjectsGraph<TData = Awaited<ReturnType<typeof getObjectsGraph>>, TError = HTTPValidationError>(
  params?: GetObjectsGraphParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getObjectsGraph>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Get Objects Graph
 */

export function useGetObjectsGraph<TData = Awaited<ReturnType<typeof getObjectsGraph>>, TError = HTTPValidationError>(
  params?: GetObjectsGraphParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getObjectsGraph>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getGetObjectsGraphQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * Get graph from a specific collection.

:param collection: The collection to retrieve nodes from (default: None, which uses the default collection)
:return: dict with nodes and edges
 * @summary Get Graph
 */
export const getGraph = (
  params?: GetGraphParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<GraphResponse>(
    {
      url: `/graph_query/get_graph`, method: 'GET',
      params, signal
    },
  );
}


export const getGetGraphQueryKey = (params?: GetGraphParams,) => {
  return [`/graph_query/get_graph`, ...(params ? [params] : [])] as const;
}


export const getGetGraphQueryOptions = <TData = Awaited<ReturnType<typeof getGraph>>, TError = HTTPValidationError>(params?: GetGraphParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGraph>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetGraphQueryKey(params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof getGraph>>> = ({ signal }) => getGraph(params, signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getGraph>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type GetGraphQueryResult = NonNullable<Awaited<ReturnType<typeof getGraph>>>
export type GetGraphQueryError = HTTPValidationError


export function useGetGraph<TData = Awaited<ReturnType<typeof getGraph>>, TError = HTTPValidationError>(
  params: undefined | GetGraphParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGraph>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getGraph>>,
        TError,
        Awaited<ReturnType<typeof getGraph>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetGraph<TData = Awaited<ReturnType<typeof getGraph>>, TError = HTTPValidationError>(
  params?: GetGraphParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGraph>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getGraph>>,
        TError,
        Awaited<ReturnType<typeof getGraph>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetGraph<TData = Awaited<ReturnType<typeof getGraph>>, TError = HTTPValidationError>(
  params?: GetGraphParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGraph>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Get Graph
 */

export function useGetGraph<TData = Awaited<ReturnType<typeof getGraph>>, TError = HTTPValidationError>(
  params?: GetGraphParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGraph>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getGetGraphQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * Ultra-optimized graph view with ID-keyed nodes.

:param node_types: Types of nodes to include
:param base_relation: Kept for API compatibility
:param metadata_only: Kept for API compatibility
:param collection: Collection to filter by
:param optimization_level: Level of optimization to use
:return: Dictionary with nodes keyed by ID
 * @summary Get Graph View
 */
export const getGraphView = (
  params: GetGraphViewParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<GetGraphView200>(
    {
      url: `/graph_query/get_graph_view`, method: 'GET',
      params, signal
    },
  );
}


export const getGetGraphViewQueryKey = (params: GetGraphViewParams,) => {
  return [`/graph_query/get_graph_view`, ...(params ? [params] : [])] as const;
}


export const getGetGraphViewQueryOptions = <TData = Awaited<ReturnType<typeof getGraphView>>, TError = HTTPValidationError>(params: GetGraphViewParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGraphView>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetGraphViewQueryKey(params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof getGraphView>>> = ({ signal }) => getGraphView(params, signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getGraphView>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type GetGraphViewQueryResult = NonNullable<Awaited<ReturnType<typeof getGraphView>>>
export type GetGraphViewQueryError = HTTPValidationError


export function useGetGraphView<TData = Awaited<ReturnType<typeof getGraphView>>, TError = HTTPValidationError>(
  params: GetGraphViewParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGraphView>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getGraphView>>,
        TError,
        Awaited<ReturnType<typeof getGraphView>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetGraphView<TData = Awaited<ReturnType<typeof getGraphView>>, TError = HTTPValidationError>(
  params: GetGraphViewParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGraphView>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getGraphView>>,
        TError,
        Awaited<ReturnType<typeof getGraphView>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetGraphView<TData = Awaited<ReturnType<typeof getGraphView>>, TError = HTTPValidationError>(
  params: GetGraphViewParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGraphView>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Get Graph View
 */

export function useGetGraphView<TData = Awaited<ReturnType<typeof getGraphView>>, TError = HTTPValidationError>(
  params: GetGraphViewParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getGraphView>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getGetGraphViewQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * Deletes node from a specific collection

:param node_id: ID of the node to delete
:param collection: The collection to delete the node from (default: None, which uses the default collection)
:return: Success status
 * @summary Delete Object Node
 */
export const deleteNode = (
  params: DeleteNodeParams,
) => {


  return apiUrlMutator<boolean>(
    {
      url: `/graph_query/delete_node`, method: 'DELETE',
      params
    },
  );
}



export const getDeleteNodeMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteNode>>, TError, { params: DeleteNodeParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof deleteNode>>, TError, { params: DeleteNodeParams }, TContext> => {

  const mutationKey = ['deleteNode'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteNode>>, { params: DeleteNodeParams }> = (props) => {
    const { params } = props ?? {};

    return deleteNode(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type DeleteNodeMutationResult = NonNullable<Awaited<ReturnType<typeof deleteNode>>>

export type DeleteNodeMutationError = HTTPValidationError

/**
* @summary Delete Object Node
*/
export const useDeleteNode = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteNode>>, TError, { params: DeleteNodeParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof deleteNode>>,
      TError,
      { params: DeleteNodeParams },
      TContext
    > => {

  const mutationOptions = getDeleteNodeMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Get node from a specific collection

:param node_id: ID of the node to retrieve
:param collection: The collection to retrieve the node from (default: None, which uses the default collection)
:return: Node data
* @summary Get Object Node
*/
export const getNode = (
  params: GetNodeParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/graph_query/get_node`, method: 'GET',
      params, signal
    },
  );
}


export const getGetNodeQueryKey = (params: GetNodeParams,) => {
  return [`/graph_query/get_node`, ...(params ? [params] : [])] as const;
}


export const getGetNodeQueryOptions = <TData = Awaited<ReturnType<typeof getNode>>, TError = HTTPValidationError>(params: GetNodeParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getNode>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetNodeQueryKey(params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof getNode>>> = ({ signal }) => getNode(params, signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getNode>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type GetNodeQueryResult = NonNullable<Awaited<ReturnType<typeof getNode>>>
export type GetNodeQueryError = HTTPValidationError


export function useGetNode<TData = Awaited<ReturnType<typeof getNode>>, TError = HTTPValidationError>(
  params: GetNodeParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getNode>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getNode>>,
        TError,
        Awaited<ReturnType<typeof getNode>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetNode<TData = Awaited<ReturnType<typeof getNode>>, TError = HTTPValidationError>(
  params: GetNodeParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getNode>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getNode>>,
        TError,
        Awaited<ReturnType<typeof getNode>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetNode<TData = Awaited<ReturnType<typeof getNode>>, TError = HTTPValidationError>(
  params: GetNodeParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getNode>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Get Object Node
 */

export function useGetNode<TData = Awaited<ReturnType<typeof getNode>>, TError = HTTPValidationError>(
  params: GetNodeParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getNode>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getGetNodeQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * Upserts node in a specific collection

:param node_name: node name
:param node_data: node dict
:param node_id: used by default, otherwise search in node_data
:param collection: The collection to upsert the node in (default: None, which uses the default collection)
:param upsert_if_missing: allows adding a new node
:return: False or node
 * @summary Update Object Node
 */
export const updateObjectNode = (
  updateObjectNodeBody: UpdateObjectNodeBody,
  params?: UpdateObjectNodeParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<UpdateObjectNode200>(
    {
      url: `/graph_query/update_object_node`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: updateObjectNodeBody,
      params, signal
    },
  );
}



export const getUpdateObjectNodeMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof updateObjectNode>>, TError, { data: UpdateObjectNodeBody; params?: UpdateObjectNodeParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof updateObjectNode>>, TError, { data: UpdateObjectNodeBody; params?: UpdateObjectNodeParams }, TContext> => {

  const mutationKey = ['updateObjectNode'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof updateObjectNode>>, { data: UpdateObjectNodeBody; params?: UpdateObjectNodeParams }> = (props) => {
    const { data, params } = props ?? {};

    return updateObjectNode(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type UpdateObjectNodeMutationResult = NonNullable<Awaited<ReturnType<typeof updateObjectNode>>>
export type UpdateObjectNodeMutationBody = UpdateObjectNodeBody
export type UpdateObjectNodeMutationError = HTTPValidationError

/**
* @summary Update Object Node
*/
export const useUpdateObjectNode = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof updateObjectNode>>, TError, { data: UpdateObjectNodeBody; params?: UpdateObjectNodeParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof updateObjectNode>>,
      TError,
      { data: UpdateObjectNodeBody; params?: UpdateObjectNodeParams },
      TContext
    > => {

  const mutationOptions = getUpdateObjectNodeMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Creates a new node in the graph within a specific collection

:param name: Name of the new node
:param node_type: Type of the node (default: ENTITY)
:param collection: The collection to create the node in (default: None, which uses the default collection)
:param attributes: Optional node attributes
:param data: Optional node data
:param extraction_metadata: Optional extraction metadata
:return: Created node
* @summary Create Node
*/
export const createNode = (
  bodyCreateNode: BodyCreateNode,
  params: CreateNodeParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<GraphNodeModel>(
    {
      url: `/graph_query/create_node`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyCreateNode,
      params, signal
    },
  );
}



export const getCreateNodeMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof createNode>>, TError, { data: BodyCreateNode; params: CreateNodeParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof createNode>>, TError, { data: BodyCreateNode; params: CreateNodeParams }, TContext> => {

  const mutationKey = ['createNode'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof createNode>>, { data: BodyCreateNode; params: CreateNodeParams }> = (props) => {
    const { data, params } = props ?? {};

    return createNode(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type CreateNodeMutationResult = NonNullable<Awaited<ReturnType<typeof createNode>>>
export type CreateNodeMutationBody = BodyCreateNode
export type CreateNodeMutationError = HTTPValidationError

/**
* @summary Create Node
*/
export const useCreateNode = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof createNode>>, TError, { data: BodyCreateNode; params: CreateNodeParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof createNode>>,
      TError,
      { data: BodyCreateNode; params: CreateNodeParams },
      TContext
    > => {

  const mutationOptions = getCreateNodeMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Creates a relation between two existing nodes

:param source_id: ID of the source node
:param target_id: ID of the target node
:param relation_type: Type of relation to create (default: parent_of)
:param is_bidirectional: Whether to create relation in both directions
:param relation_data: Optional data to attach to the relation
:param collection: The collection to check for nodes (default: None, which uses the default collection)
:return: Status of the operation
* @summary Create Relation
*/
export const createRelation = (
  createRelationBody: CreateRelationBody,
  params: CreateRelationParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<CreateRelation200>(
    {
      url: `/graph_query/create_relation`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: createRelationBody,
      params, signal
    },
  );
}



export const getCreateRelationMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof createRelation>>, TError, { data: CreateRelationBody; params: CreateRelationParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof createRelation>>, TError, { data: CreateRelationBody; params: CreateRelationParams }, TContext> => {

  const mutationKey = ['createRelation'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof createRelation>>, { data: CreateRelationBody; params: CreateRelationParams }> = (props) => {
    const { data, params } = props ?? {};

    return createRelation(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type CreateRelationMutationResult = NonNullable<Awaited<ReturnType<typeof createRelation>>>
export type CreateRelationMutationBody = CreateRelationBody
export type CreateRelationMutationError = HTTPValidationError

/**
* @summary Create Relation
*/
export const useCreateRelation = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof createRelation>>, TError, { data: CreateRelationBody; params: CreateRelationParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof createRelation>>,
      TError,
      { data: CreateRelationBody; params: CreateRelationParams },
      TContext
    > => {

  const mutationOptions = getCreateRelationMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Deletes a relation between two nodes

:param source_id: Source node ID
:param target_id: Target node ID
:param relation_type: Type of relation to delete
:param collection: The collection to check for nodes (default: None, which uses the default collection)
:return: Status of the operation
* @summary Delete Relation
*/
export const deleteRelation = (
  params: DeleteRelationParams,
) => {


  return apiUrlMutator<DeleteRelation200>(
    {
      url: `/graph_query/delete_relation`, method: 'DELETE',
      params
    },
  );
}



export const getDeleteRelationMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteRelation>>, TError, { params: DeleteRelationParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof deleteRelation>>, TError, { params: DeleteRelationParams }, TContext> => {

  const mutationKey = ['deleteRelation'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteRelation>>, { params: DeleteRelationParams }> = (props) => {
    const { params } = props ?? {};

    return deleteRelation(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type DeleteRelationMutationResult = NonNullable<Awaited<ReturnType<typeof deleteRelation>>>

export type DeleteRelationMutationError = HTTPValidationError

/**
* @summary Delete Relation
*/
export const useDeleteRelation = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteRelation>>, TError, { params: DeleteRelationParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof deleteRelation>>,
      TError,
      { params: DeleteRelationParams },
      TContext
    > => {

  const mutationOptions = getDeleteRelationMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Copies a node from one collection to another

:param node_id: ID of the node to copy
:param source_collection: Source collection name
:param target_collection: Target collection name
:return: The newly created node
* @summary Copy Node To Collection
*/
export const copyNodeToCollection = (
  params: CopyNodeToCollectionParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<GraphNodeModel>(
    {
      url: `/graph_query/copy_node_to_collection`, method: 'POST',
      params, signal
    },
  );
}



export const getCopyNodeToCollectionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof copyNodeToCollection>>, TError, { params: CopyNodeToCollectionParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof copyNodeToCollection>>, TError, { params: CopyNodeToCollectionParams }, TContext> => {

  const mutationKey = ['copyNodeToCollection'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof copyNodeToCollection>>, { params: CopyNodeToCollectionParams }> = (props) => {
    const { params } = props ?? {};

    return copyNodeToCollection(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type CopyNodeToCollectionMutationResult = NonNullable<Awaited<ReturnType<typeof copyNodeToCollection>>>

export type CopyNodeToCollectionMutationError = HTTPValidationError

/**
* @summary Copy Node To Collection
*/
export const useCopyNodeToCollection = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof copyNodeToCollection>>, TError, { params: CopyNodeToCollectionParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof copyNodeToCollection>>,
      TError,
      { params: CopyNodeToCollectionParams },
      TContext
    > => {

  const mutationOptions = getCopyNodeToCollectionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
