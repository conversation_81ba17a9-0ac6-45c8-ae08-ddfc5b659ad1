/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import { apiUrlMutator } from '../../config/mutator';




/**
 * @summary Ready Check
 */
export const healthCheckReady = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/health/ready`, method: 'GET', signal
    },
  );
}


export const getHealthCheckReadyQueryKey = () => {
  return [`/health/ready`] as const;
}


export const getHealthCheckReadyQueryOptions = <TData = Awaited<ReturnType<typeof healthCheckReady>>, TError = unknown>(options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof healthCheckReady>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getHealthCheckReadyQueryKey();



  const queryFn: QueryFunction<Awaited<ReturnType<typeof healthCheckReady>>> = ({ signal }) => healthCheckReady(signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof healthCheckReady>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type HealthCheckReadyQueryResult = NonNullable<Awaited<ReturnType<typeof healthCheckReady>>>
export type HealthCheckReadyQueryError = unknown


export function useHealthCheckReady<TData = Awaited<ReturnType<typeof healthCheckReady>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof healthCheckReady>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof healthCheckReady>>,
        TError,
        Awaited<ReturnType<typeof healthCheckReady>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useHealthCheckReady<TData = Awaited<ReturnType<typeof healthCheckReady>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof healthCheckReady>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof healthCheckReady>>,
        TError,
        Awaited<ReturnType<typeof healthCheckReady>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useHealthCheckReady<TData = Awaited<ReturnType<typeof healthCheckReady>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof healthCheckReady>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Ready Check
 */

export function useHealthCheckReady<TData = Awaited<ReturnType<typeof healthCheckReady>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof healthCheckReady>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getHealthCheckReadyQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * @summary Live Check
 */
export const healthCheckLive = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/health/live`, method: 'GET', signal
    },
  );
}


export const getHealthCheckLiveQueryKey = () => {
  return [`/health/live`] as const;
}


export const getHealthCheckLiveQueryOptions = <TData = Awaited<ReturnType<typeof healthCheckLive>>, TError = unknown>(options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof healthCheckLive>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getHealthCheckLiveQueryKey();



  const queryFn: QueryFunction<Awaited<ReturnType<typeof healthCheckLive>>> = ({ signal }) => healthCheckLive(signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof healthCheckLive>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type HealthCheckLiveQueryResult = NonNullable<Awaited<ReturnType<typeof healthCheckLive>>>
export type HealthCheckLiveQueryError = unknown


export function useHealthCheckLive<TData = Awaited<ReturnType<typeof healthCheckLive>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof healthCheckLive>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof healthCheckLive>>,
        TError,
        Awaited<ReturnType<typeof healthCheckLive>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useHealthCheckLive<TData = Awaited<ReturnType<typeof healthCheckLive>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof healthCheckLive>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof healthCheckLive>>,
        TError,
        Awaited<ReturnType<typeof healthCheckLive>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useHealthCheckLive<TData = Awaited<ReturnType<typeof healthCheckLive>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof healthCheckLive>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Live Check
 */

export function useHealthCheckLive<TData = Awaited<ReturnType<typeof healthCheckLive>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof healthCheckLive>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getHealthCheckLiveQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



