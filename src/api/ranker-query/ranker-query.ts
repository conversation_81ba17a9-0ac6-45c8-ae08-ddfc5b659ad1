/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation
} from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryClient,
  UseMutationOptions,
  UseMutationResult
} from '@tanstack/react-query';

import type {
  HTTPValidationError,
  RankerData,
  RankerResponse
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * :param data: {'query': 'query', 'chunks': ['chunk1', 'chunk2']}
:return: list of floats (scores)
 * @summary Run Ranker Inference
 */
export const rankText = (
  rankerData: RankerData,
  signal?: AbortSignal
) => {


  return apiUrlMutator<RankerResponse>(
    {
      url: `/ranker_query/rank_text`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: rankerData, signal
    },
  );
}



export const getRankTextMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof rankText>>, TError, { data: RankerData }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof rankText>>, TError, { data: RankerData }, TContext> => {

  const mutationKey = ['rankText'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof rankText>>, { data: RankerData }> = (props) => {
    const { data } = props ?? {};

    return rankText(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type RankTextMutationResult = NonNullable<Awaited<ReturnType<typeof rankText>>>
export type RankTextMutationBody = RankerData
export type RankTextMutationError = HTTPValidationError

/**
* @summary Run Ranker Inference
*/
export const useRankText = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof rankText>>, TError, { data: RankerData }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof rankText>>,
      TError,
      { data: RankerData },
      TContext
    > => {

  const mutationOptions = getRankTextMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
