/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation
} from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryClient,
  UseMutationOptions,
  UseMutationResult
} from '@tanstack/react-query';

import type {
  BodyGetRelevantDocuments,
  BodyGetRelevantDocumentsMultiCollection,
  GetRelevantDocumentsParams,
  HTTPValidationError
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * @summary Get Relevant Documents
 */
export const getRelevantDocuments = (
  bodyGetRelevantDocuments: BodyGetRelevantDocuments,
  params?: GetRelevantDocumentsParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/semantic_search/get_relevant_documents`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyGetRelevantDocuments,
      params, signal
    },
  );
}



export const getGetRelevantDocumentsMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof getRelevantDocuments>>, TError, { data: BodyGetRelevantDocuments; params?: GetRelevantDocumentsParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof getRelevantDocuments>>, TError, { data: BodyGetRelevantDocuments; params?: GetRelevantDocumentsParams }, TContext> => {

  const mutationKey = ['getRelevantDocuments'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof getRelevantDocuments>>, { data: BodyGetRelevantDocuments; params?: GetRelevantDocumentsParams }> = (props) => {
    const { data, params } = props ?? {};

    return getRelevantDocuments(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type GetRelevantDocumentsMutationResult = NonNullable<Awaited<ReturnType<typeof getRelevantDocuments>>>
export type GetRelevantDocumentsMutationBody = BodyGetRelevantDocuments
export type GetRelevantDocumentsMutationError = HTTPValidationError

/**
* @summary Get Relevant Documents
*/
export const useGetRelevantDocuments = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof getRelevantDocuments>>, TError, { data: BodyGetRelevantDocuments; params?: GetRelevantDocumentsParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof getRelevantDocuments>>,
      TError,
      { data: BodyGetRelevantDocuments; params?: GetRelevantDocumentsParams },
      TContext
    > => {

  const mutationOptions = getGetRelevantDocumentsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Multi Collection Get Relevant Documents
*/
export const getRelevantDocumentsMultiCollection = (
  bodyGetRelevantDocumentsMultiCollection: BodyGetRelevantDocumentsMultiCollection,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/semantic_search/multi_collection_get_relevant_documents`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyGetRelevantDocumentsMultiCollection, signal
    },
  );
}



export const getGetRelevantDocumentsMultiCollectionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof getRelevantDocumentsMultiCollection>>, TError, { data: BodyGetRelevantDocumentsMultiCollection }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof getRelevantDocumentsMultiCollection>>, TError, { data: BodyGetRelevantDocumentsMultiCollection }, TContext> => {

  const mutationKey = ['getRelevantDocumentsMultiCollection'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof getRelevantDocumentsMultiCollection>>, { data: BodyGetRelevantDocumentsMultiCollection }> = (props) => {
    const { data } = props ?? {};

    return getRelevantDocumentsMultiCollection(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type GetRelevantDocumentsMultiCollectionMutationResult = NonNullable<Awaited<ReturnType<typeof getRelevantDocumentsMultiCollection>>>
export type GetRelevantDocumentsMultiCollectionMutationBody = BodyGetRelevantDocumentsMultiCollection
export type GetRelevantDocumentsMultiCollectionMutationError = HTTPValidationError

/**
* @summary Multi Collection Get Relevant Documents
*/
export const useGetRelevantDocumentsMultiCollection = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof getRelevantDocumentsMultiCollection>>, TError, { data: BodyGetRelevantDocumentsMultiCollection }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof getRelevantDocumentsMultiCollection>>,
      TError,
      { data: BodyGetRelevantDocumentsMultiCollection },
      TContext
    > => {

  const mutationOptions = getGetRelevantDocumentsMultiCollectionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
