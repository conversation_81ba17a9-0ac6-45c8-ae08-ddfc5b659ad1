/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation,
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import type {
  BodyUploadSnapshots,
  DeleteSnapshotParams,
  HTTPValidationError,
  LoadSnapshot200,
  LoadSnapshotParams,
  MakeSnapshot200
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * Makes a database snapshot

Returns None on failure and snapshot id on success
 * @summary Make Snapshot
 */
export const makeSnapshot = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<MakeSnapshot200>(
    {
      url: `/snapshot/make`, method: 'POST', signal
    },
  );
}



export const getMakeSnapshotMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof makeSnapshot>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof makeSnapshot>>, TError, void, TContext> => {

  const mutationKey = ['makeSnapshot'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof makeSnapshot>>, void> = () => {


    return makeSnapshot()
  }




  return { mutationFn, ...mutationOptions }
}

export type MakeSnapshotMutationResult = NonNullable<Awaited<ReturnType<typeof makeSnapshot>>>

export type MakeSnapshotMutationError = unknown

/**
* @summary Make Snapshot
*/
export const useMakeSnapshot = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof makeSnapshot>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof makeSnapshot>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getMakeSnapshotMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Loads a database snapshot

Returns None on failure and snapshot id on success
* @summary Load Snapshot
*/
export const loadSnapshot = (
  params: LoadSnapshotParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<LoadSnapshot200>(
    {
      url: `/snapshot/load`, method: 'POST',
      params, signal
    },
  );
}



export const getLoadSnapshotMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof loadSnapshot>>, TError, { params: LoadSnapshotParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof loadSnapshot>>, TError, { params: LoadSnapshotParams }, TContext> => {

  const mutationKey = ['loadSnapshot'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof loadSnapshot>>, { params: LoadSnapshotParams }> = (props) => {
    const { params } = props ?? {};

    return loadSnapshot(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type LoadSnapshotMutationResult = NonNullable<Awaited<ReturnType<typeof loadSnapshot>>>

export type LoadSnapshotMutationError = HTTPValidationError

/**
* @summary Load Snapshot
*/
export const useLoadSnapshot = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof loadSnapshot>>, TError, { params: LoadSnapshotParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof loadSnapshot>>,
      TError,
      { params: LoadSnapshotParams },
      TContext
    > => {

  const mutationOptions = getLoadSnapshotMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Add snapshots (.zips) to be loaded to snapshot manager
* @summary Upload Snapshots
*/
export const uploadSnapshots = (
  bodyUploadSnapshots: BodyUploadSnapshots,
  signal?: AbortSignal
) => {

  const formData = new FormData();
  bodyUploadSnapshots.database_snapshots.forEach(value => formData.append(`database_snapshots`, value));

  return apiUrlMutator<unknown>(
    {
      url: `/snapshot/upload`, method: 'POST',
      headers: { 'Content-Type': 'multipart/form-data', },
      data: formData, signal
    },
  );
}



export const getUploadSnapshotsMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof uploadSnapshots>>, TError, { data: BodyUploadSnapshots }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof uploadSnapshots>>, TError, { data: BodyUploadSnapshots }, TContext> => {

  const mutationKey = ['uploadSnapshots'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof uploadSnapshots>>, { data: BodyUploadSnapshots }> = (props) => {
    const { data } = props ?? {};

    return uploadSnapshots(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type UploadSnapshotsMutationResult = NonNullable<Awaited<ReturnType<typeof uploadSnapshots>>>
export type UploadSnapshotsMutationBody = BodyUploadSnapshots
export type UploadSnapshotsMutationError = HTTPValidationError

/**
* @summary Upload Snapshots
*/
export const useUploadSnapshots = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof uploadSnapshots>>, TError, { data: BodyUploadSnapshots }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof uploadSnapshots>>,
      TError,
      { data: BodyUploadSnapshots },
      TContext
    > => {

  const mutationOptions = getUploadSnapshotsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* List all available snapshots
* @summary List Snapshots
*/
export const listSnapshots = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/snapshot/`, method: 'GET', signal
    },
  );
}


export const getListSnapshotsQueryKey = () => {
  return [`/snapshot/`] as const;
}


export const getListSnapshotsQueryOptions = <TData = Awaited<ReturnType<typeof listSnapshots>>, TError = unknown>(options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listSnapshots>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getListSnapshotsQueryKey();



  const queryFn: QueryFunction<Awaited<ReturnType<typeof listSnapshots>>> = ({ signal }) => listSnapshots(signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof listSnapshots>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type ListSnapshotsQueryResult = NonNullable<Awaited<ReturnType<typeof listSnapshots>>>
export type ListSnapshotsQueryError = unknown


export function useListSnapshots<TData = Awaited<ReturnType<typeof listSnapshots>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof listSnapshots>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof listSnapshots>>,
        TError,
        Awaited<ReturnType<typeof listSnapshots>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListSnapshots<TData = Awaited<ReturnType<typeof listSnapshots>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listSnapshots>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof listSnapshots>>,
        TError,
        Awaited<ReturnType<typeof listSnapshots>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListSnapshots<TData = Awaited<ReturnType<typeof listSnapshots>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listSnapshots>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary List Snapshots
 */

export function useListSnapshots<TData = Awaited<ReturnType<typeof listSnapshots>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listSnapshots>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getListSnapshotsQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * Delete a snapshot
 * @summary Delete Snapshot
 */
export const deleteSnapshot = (
  params: DeleteSnapshotParams,
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/snapshot/delete`, method: 'DELETE',
      params
    },
  );
}



export const getDeleteSnapshotMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteSnapshot>>, TError, { params: DeleteSnapshotParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof deleteSnapshot>>, TError, { params: DeleteSnapshotParams }, TContext> => {

  const mutationKey = ['deleteSnapshot'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteSnapshot>>, { params: DeleteSnapshotParams }> = (props) => {
    const { params } = props ?? {};

    return deleteSnapshot(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type DeleteSnapshotMutationResult = NonNullable<Awaited<ReturnType<typeof deleteSnapshot>>>

export type DeleteSnapshotMutationError = HTTPValidationError

/**
* @summary Delete Snapshot
*/
export const useDeleteSnapshot = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteSnapshot>>, TError, { params: DeleteSnapshotParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof deleteSnapshot>>,
      TError,
      { params: DeleteSnapshotParams },
      TContext
    > => {

  const mutationOptions = getDeleteSnapshotMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Delete all snapshots
* @summary Delete All Snapshots
*/
export const deleteAllSnapshots = (

) => {


  return apiUrlMutator<unknown>(
    {
      url: `/snapshot/delete_all`, method: 'DELETE'
    },
  );
}



export const getDeleteAllSnapshotsMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteAllSnapshots>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof deleteAllSnapshots>>, TError, void, TContext> => {

  const mutationKey = ['deleteAllSnapshots'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteAllSnapshots>>, void> = () => {


    return deleteAllSnapshots()
  }




  return { mutationFn, ...mutationOptions }
}

export type DeleteAllSnapshotsMutationResult = NonNullable<Awaited<ReturnType<typeof deleteAllSnapshots>>>

export type DeleteAllSnapshotsMutationError = unknown

/**
* @summary Delete All Snapshots
*/
export const useDeleteAllSnapshots = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteAllSnapshots>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof deleteAllSnapshots>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getDeleteAllSnapshotsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
