/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import { apiUrlMutator } from '../../config/mutator';




/**
 * @summary Read Root
 */
export const readRootGet = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/`, method: 'GET', signal
    },
  );
}


export const getReadRootGetQueryKey = () => {
  return [`/`] as const;
}


export const getReadRootGetQueryOptions = <TData = Awaited<ReturnType<typeof readRootGet>>, TError = unknown>(options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof readRootGet>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getReadRootGetQueryKey();



  const queryFn: QueryFunction<Awaited<ReturnType<typeof readRootGet>>> = ({ signal }) => readRootGet(signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof readRootGet>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type ReadRootGetQueryResult = NonNullable<Awaited<ReturnType<typeof readRootGet>>>
export type ReadRootGetQueryError = unknown


export function useReadRootGet<TData = Awaited<ReturnType<typeof readRootGet>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof readRootGet>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof readRootGet>>,
        TError,
        Awaited<ReturnType<typeof readRootGet>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useReadRootGet<TData = Awaited<ReturnType<typeof readRootGet>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof readRootGet>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof readRootGet>>,
        TError,
        Awaited<ReturnType<typeof readRootGet>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useReadRootGet<TData = Awaited<ReturnType<typeof readRootGet>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof readRootGet>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Read Root
 */

export function useReadRootGet<TData = Awaited<ReturnType<typeof readRootGet>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof readRootGet>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getReadRootGetQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



