/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation
} from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryClient,
  UseMutationOptions,
  UseMutationResult
} from '@tanstack/react-query';

import type {
  BodyUploadAndProcess,
  BodyUploadDocuments,
  BodyUploadSqlTable,
  HTTPValidationError,
  UploadAndProcessParams,
  UploadDocumentsParams,
  UploadSqlTableParams
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * Upload files and optionally associate them with a collection.

Args:
    files: List of files to upload
    collection_id: Optional collection ID to associate documents with
    contextclue: ContextClue instance

Returns:
    Upload response with status for each file
 * @summary Upload
 */
export const uploadDocuments = (
  bodyUploadDocuments: BodyUploadDocuments,
  params?: UploadDocumentsParams,
  signal?: AbortSignal
) => {

  const formData = new FormData();
  bodyUploadDocuments.files.forEach(value => formData.append(`files`, value));

  return apiUrlMutator<unknown>(
    {
      url: `/upload`, method: 'POST',
      headers: { 'Content-Type': 'multipart/form-data', },
      data: formData,
      params, signal
    },
  );
}



export const getUploadDocumentsMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof uploadDocuments>>, TError, { data: BodyUploadDocuments; params?: UploadDocumentsParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof uploadDocuments>>, TError, { data: BodyUploadDocuments; params?: UploadDocumentsParams }, TContext> => {

  const mutationKey = ['uploadDocuments'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof uploadDocuments>>, { data: BodyUploadDocuments; params?: UploadDocumentsParams }> = (props) => {
    const { data, params } = props ?? {};

    return uploadDocuments(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type UploadDocumentsMutationResult = NonNullable<Awaited<ReturnType<typeof uploadDocuments>>>
export type UploadDocumentsMutationBody = BodyUploadDocuments
export type UploadDocumentsMutationError = HTTPValidationError

/**
* @summary Upload
*/
export const useUploadDocuments = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof uploadDocuments>>, TError, { data: BodyUploadDocuments; params?: UploadDocumentsParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof uploadDocuments>>,
      TError,
      { data: BodyUploadDocuments; params?: UploadDocumentsParams },
      TContext
    > => {

  const mutationOptions = getUploadDocumentsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Upload Sql Table
*/
export const uploadSqlTable = (
  bodyUploadSqlTable: BodyUploadSqlTable,
  params: UploadSqlTableParams,
  signal?: AbortSignal
) => {

  const formData = new FormData();
  formData.append(`csv_file`, bodyUploadSqlTable.csv_file)

  return apiUrlMutator<boolean>(
    {
      url: `/upload/sql_table`, method: 'POST',
      headers: { 'Content-Type': 'multipart/form-data', },
      data: formData,
      params, signal
    },
  );
}



export const getUploadSqlTableMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof uploadSqlTable>>, TError, { data: BodyUploadSqlTable; params: UploadSqlTableParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof uploadSqlTable>>, TError, { data: BodyUploadSqlTable; params: UploadSqlTableParams }, TContext> => {

  const mutationKey = ['uploadSqlTable'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof uploadSqlTable>>, { data: BodyUploadSqlTable; params: UploadSqlTableParams }> = (props) => {
    const { data, params } = props ?? {};

    return uploadSqlTable(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type UploadSqlTableMutationResult = NonNullable<Awaited<ReturnType<typeof uploadSqlTable>>>
export type UploadSqlTableMutationBody = BodyUploadSqlTable
export type UploadSqlTableMutationError = HTTPValidationError

/**
* @summary Upload Sql Table
*/
export const useUploadSqlTable = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof uploadSqlTable>>, TError, { data: BodyUploadSqlTable; params: UploadSqlTableParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof uploadSqlTable>>,
      TError,
      { data: BodyUploadSqlTable; params: UploadSqlTableParams },
      TContext
    > => {

  const mutationOptions = getUploadSqlTableMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Upload and process the uploaded documents.

When collection is not present then it is created.

Args:
background_tasks: FastAPI background tasks
files: List of files to upload and process
processor_config: Configuration for document processing
collection_id: Collection ID to associate documents with
add_keywords_to_chunks: Whether to add keywords to chunks
anonymize: Whether to anonymize the content
contextclue: ContextClue instance
* @summary Upload And Process
*/
export const uploadAndProcess = (
  bodyUploadAndProcess: BodyUploadAndProcess,
  params?: UploadAndProcessParams,
  signal?: AbortSignal
) => {

  const formData = new FormData();
  bodyUploadAndProcess.files.forEach(value => formData.append(`files`, value));
  if (bodyUploadAndProcess.processor_config !== undefined && bodyUploadAndProcess.processor_config !== null) {
    formData.append(`processor_config`, bodyUploadAndProcess.processor_config)
  }

  return apiUrlMutator<unknown>(
    {
      url: `/upload/upload_and_process`, method: 'POST',
      headers: { 'Content-Type': 'multipart/form-data', },
      data: formData,
      params, signal
    },
  );
}



export const getUploadAndProcessMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof uploadAndProcess>>, TError, { data: BodyUploadAndProcess; params?: UploadAndProcessParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof uploadAndProcess>>, TError, { data: BodyUploadAndProcess; params?: UploadAndProcessParams }, TContext> => {

  const mutationKey = ['uploadAndProcess'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof uploadAndProcess>>, { data: BodyUploadAndProcess; params?: UploadAndProcessParams }> = (props) => {
    const { data, params } = props ?? {};

    return uploadAndProcess(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type UploadAndProcessMutationResult = NonNullable<Awaited<ReturnType<typeof uploadAndProcess>>>
export type UploadAndProcessMutationBody = BodyUploadAndProcess
export type UploadAndProcessMutationError = HTTPValidationError

/**
* @summary Upload And Process
*/
export const useUploadAndProcess = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof uploadAndProcess>>, TError, { data: BodyUploadAndProcess; params?: UploadAndProcessParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof uploadAndProcess>>,
      TError,
      { data: BodyUploadAndProcess; params?: UploadAndProcessParams },
      TContext
    > => {

  const mutationOptions = getUploadAndProcessMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
