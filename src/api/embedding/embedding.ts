/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation,
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import type {
  AddTopologyToCollectionParams,
  AddVectorCollectionParams,
  AddVectorsParams,
  BodyCalculateEmbeddings,
  BodyCalculateForCollection,
  CollectionEntry,
  CollectionModel,
  CopyEmbeddingsParams,
  EmbeddingManagerConfigInput,
  GetAllChunksFromCollectionParams,
  GetChunksForDocumentParams,
  GetFilledTopologyGraph200,
  GetFilledTopologyGraphParams,
  GetKeywordsForChunks200,
  GetKeywordsForChunksParams,
  HTTPValidationError,
  ListVectorsParams,
  NodeListInput,
  ResetCollectionParams,
  UpdateCollectionMetadataToExtractBody,
  UpdateCollectionMetadataToExtractParams
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * @summary List Vector Collections
 */
export const listVectorCollections = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/embedding/vector_collections`, method: 'GET', signal
    },
  );
}


export const getListVectorCollectionsQueryKey = () => {
  return [`/embedding/vector_collections`] as const;
}


export const getListVectorCollectionsQueryOptions = <TData = Awaited<ReturnType<typeof listVectorCollections>>, TError = unknown>(options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listVectorCollections>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getListVectorCollectionsQueryKey();



  const queryFn: QueryFunction<Awaited<ReturnType<typeof listVectorCollections>>> = ({ signal }) => listVectorCollections(signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof listVectorCollections>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type ListVectorCollectionsQueryResult = NonNullable<Awaited<ReturnType<typeof listVectorCollections>>>
export type ListVectorCollectionsQueryError = unknown


export function useListVectorCollections<TData = Awaited<ReturnType<typeof listVectorCollections>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof listVectorCollections>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof listVectorCollections>>,
        TError,
        Awaited<ReturnType<typeof listVectorCollections>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListVectorCollections<TData = Awaited<ReturnType<typeof listVectorCollections>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listVectorCollections>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof listVectorCollections>>,
        TError,
        Awaited<ReturnType<typeof listVectorCollections>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListVectorCollections<TData = Awaited<ReturnType<typeof listVectorCollections>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listVectorCollections>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary List Vector Collections
 */

export function useListVectorCollections<TData = Awaited<ReturnType<typeof listVectorCollections>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listVectorCollections>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getListVectorCollectionsQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * @summary List All Vectors
 */
export const listVectors = (
  params?: ListVectorsParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/embedding/vectors`, method: 'GET',
      params, signal
    },
  );
}


export const getListVectorsQueryKey = (params?: ListVectorsParams,) => {
  return [`/embedding/vectors`, ...(params ? [params] : [])] as const;
}


export const getListVectorsQueryOptions = <TData = Awaited<ReturnType<typeof listVectors>>, TError = HTTPValidationError>(params?: ListVectorsParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listVectors>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getListVectorsQueryKey(params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof listVectors>>> = ({ signal }) => listVectors(params, signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof listVectors>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type ListVectorsQueryResult = NonNullable<Awaited<ReturnType<typeof listVectors>>>
export type ListVectorsQueryError = HTTPValidationError


export function useListVectors<TData = Awaited<ReturnType<typeof listVectors>>, TError = HTTPValidationError>(
  params: undefined | ListVectorsParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof listVectors>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof listVectors>>,
        TError,
        Awaited<ReturnType<typeof listVectors>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListVectors<TData = Awaited<ReturnType<typeof listVectors>>, TError = HTTPValidationError>(
  params?: ListVectorsParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listVectors>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof listVectors>>,
        TError,
        Awaited<ReturnType<typeof listVectors>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListVectors<TData = Awaited<ReturnType<typeof listVectors>>, TError = HTTPValidationError>(
  params?: ListVectorsParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listVectors>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary List All Vectors
 */

export function useListVectors<TData = Awaited<ReturnType<typeof listVectors>>, TError = HTTPValidationError>(
  params?: ListVectorsParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listVectors>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getListVectorsQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * @summary Delete Collection
 */
export const deleteVectorCollection = (
  collectionId: string,
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/embedding/collection/${collectionId}`, method: 'DELETE'
    },
  );
}



export const getDeleteVectorCollectionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteVectorCollection>>, TError, { collectionId: string }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof deleteVectorCollection>>, TError, { collectionId: string }, TContext> => {

  const mutationKey = ['deleteVectorCollection'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteVectorCollection>>, { collectionId: string }> = (props) => {
    const { collectionId } = props ?? {};

    return deleteVectorCollection(collectionId,)
  }




  return { mutationFn, ...mutationOptions }
}

export type DeleteVectorCollectionMutationResult = NonNullable<Awaited<ReturnType<typeof deleteVectorCollection>>>

export type DeleteVectorCollectionMutationError = HTTPValidationError

/**
* @summary Delete Collection
*/
export const useDeleteVectorCollection = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteVectorCollection>>, TError, { collectionId: string }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof deleteVectorCollection>>,
      TError,
      { collectionId: string },
      TContext
    > => {

  const mutationOptions = getDeleteVectorCollectionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Add Collection
*/
export const addVectorCollection = (
  embeddingManagerConfigInput: EmbeddingManagerConfigInput,
  params?: AddVectorCollectionParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/embedding/add_collection`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: embeddingManagerConfigInput,
      params, signal
    },
  );
}



export const getAddVectorCollectionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof addVectorCollection>>, TError, { data: EmbeddingManagerConfigInput; params?: AddVectorCollectionParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof addVectorCollection>>, TError, { data: EmbeddingManagerConfigInput; params?: AddVectorCollectionParams }, TContext> => {

  const mutationKey = ['addVectorCollection'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof addVectorCollection>>, { data: EmbeddingManagerConfigInput; params?: AddVectorCollectionParams }> = (props) => {
    const { data, params } = props ?? {};

    return addVectorCollection(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AddVectorCollectionMutationResult = NonNullable<Awaited<ReturnType<typeof addVectorCollection>>>
export type AddVectorCollectionMutationBody = EmbeddingManagerConfigInput
export type AddVectorCollectionMutationError = HTTPValidationError

/**
* @summary Add Collection
*/
export const useAddVectorCollection = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof addVectorCollection>>, TError, { data: EmbeddingManagerConfigInput; params?: AddVectorCollectionParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof addVectorCollection>>,
      TError,
      { data: EmbeddingManagerConfigInput; params?: AddVectorCollectionParams },
      TContext
    > => {

  const mutationOptions = getAddVectorCollectionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Add Entries
*/
export const addVectors = (
  collectionEntry: CollectionEntry[],
  params: AddVectorsParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/embedding/add_entries`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: collectionEntry,
      params, signal
    },
  );
}



export const getAddVectorsMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof addVectors>>, TError, { data: CollectionEntry[]; params: AddVectorsParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof addVectors>>, TError, { data: CollectionEntry[]; params: AddVectorsParams }, TContext> => {

  const mutationKey = ['addVectors'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof addVectors>>, { data: CollectionEntry[]; params: AddVectorsParams }> = (props) => {
    const { data, params } = props ?? {};

    return addVectors(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AddVectorsMutationResult = NonNullable<Awaited<ReturnType<typeof addVectors>>>
export type AddVectorsMutationBody = CollectionEntry[]
export type AddVectorsMutationError = HTTPValidationError

/**
* @summary Add Entries
*/
export const useAddVectors = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof addVectors>>, TError, { data: CollectionEntry[]; params: AddVectorsParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof addVectors>>,
      TError,
      { data: CollectionEntry[]; params: AddVectorsParams },
      TContext
    > => {

  const mutationOptions = getAddVectorsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Calculate For Collection
*/
export const calculateForCollection = (
  bodyCalculateForCollection: BodyCalculateForCollection,
  signal?: AbortSignal
) => {


  return apiUrlMutator<number[][]>(
    {
      url: `/embedding/calculate_for_collection`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyCalculateForCollection, signal
    },
  );
}



export const getCalculateForCollectionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof calculateForCollection>>, TError, { data: BodyCalculateForCollection }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof calculateForCollection>>, TError, { data: BodyCalculateForCollection }, TContext> => {

  const mutationKey = ['calculateForCollection'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof calculateForCollection>>, { data: BodyCalculateForCollection }> = (props) => {
    const { data } = props ?? {};

    return calculateForCollection(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type CalculateForCollectionMutationResult = NonNullable<Awaited<ReturnType<typeof calculateForCollection>>>
export type CalculateForCollectionMutationBody = BodyCalculateForCollection
export type CalculateForCollectionMutationError = HTTPValidationError

/**
* @summary Calculate For Collection
*/
export const useCalculateForCollection = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof calculateForCollection>>, TError, { data: BodyCalculateForCollection }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof calculateForCollection>>,
      TError,
      { data: BodyCalculateForCollection },
      TContext
    > => {

  const mutationOptions = getCalculateForCollectionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Calculate
*/
export const calculateEmbeddings = (
  bodyCalculateEmbeddings: BodyCalculateEmbeddings,
  signal?: AbortSignal
) => {


  return apiUrlMutator<number[][]>(
    {
      url: `/embedding/calculate`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyCalculateEmbeddings, signal
    },
  );
}



export const getCalculateEmbeddingsMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof calculateEmbeddings>>, TError, { data: BodyCalculateEmbeddings }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof calculateEmbeddings>>, TError, { data: BodyCalculateEmbeddings }, TContext> => {

  const mutationKey = ['calculateEmbeddings'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof calculateEmbeddings>>, { data: BodyCalculateEmbeddings }> = (props) => {
    const { data } = props ?? {};

    return calculateEmbeddings(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type CalculateEmbeddingsMutationResult = NonNullable<Awaited<ReturnType<typeof calculateEmbeddings>>>
export type CalculateEmbeddingsMutationBody = BodyCalculateEmbeddings
export type CalculateEmbeddingsMutationError = HTTPValidationError

/**
* @summary Calculate
*/
export const useCalculateEmbeddings = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof calculateEmbeddings>>, TError, { data: BodyCalculateEmbeddings }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof calculateEmbeddings>>,
      TError,
      { data: BodyCalculateEmbeddings },
      TContext
    > => {

  const mutationOptions = getCalculateEmbeddingsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Get all chunks for a given document represented by `document_id`. When `collection_id` is    None then the chunks are taken from every collection that has a given document.

:param document_id: ID of a document from which chunks are taken.
:param collection_id: ID of a collection from which chunks are taken.
:param contextclue: ContextClue instance.
:return: A list of collection entries.
* @summary Get Chunks
*/
export const getChunksForDocument = (
  params: GetChunksForDocumentParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<CollectionEntry[]>(
    {
      url: `/embedding/chunks_for_document`, method: 'GET',
      params, signal
    },
  );
}


export const getGetChunksForDocumentQueryKey = (params: GetChunksForDocumentParams,) => {
  return [`/embedding/chunks_for_document`, ...(params ? [params] : [])] as const;
}


export const getGetChunksForDocumentQueryOptions = <TData = Awaited<ReturnType<typeof getChunksForDocument>>, TError = HTTPValidationError>(params: GetChunksForDocumentParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getChunksForDocument>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetChunksForDocumentQueryKey(params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof getChunksForDocument>>> = ({ signal }) => getChunksForDocument(params, signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getChunksForDocument>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type GetChunksForDocumentQueryResult = NonNullable<Awaited<ReturnType<typeof getChunksForDocument>>>
export type GetChunksForDocumentQueryError = HTTPValidationError


export function useGetChunksForDocument<TData = Awaited<ReturnType<typeof getChunksForDocument>>, TError = HTTPValidationError>(
  params: GetChunksForDocumentParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getChunksForDocument>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getChunksForDocument>>,
        TError,
        Awaited<ReturnType<typeof getChunksForDocument>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetChunksForDocument<TData = Awaited<ReturnType<typeof getChunksForDocument>>, TError = HTTPValidationError>(
  params: GetChunksForDocumentParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getChunksForDocument>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getChunksForDocument>>,
        TError,
        Awaited<ReturnType<typeof getChunksForDocument>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetChunksForDocument<TData = Awaited<ReturnType<typeof getChunksForDocument>>, TError = HTTPValidationError>(
  params: GetChunksForDocumentParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getChunksForDocument>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Get Chunks
 */

export function useGetChunksForDocument<TData = Awaited<ReturnType<typeof getChunksForDocument>>, TError = HTTPValidationError>(
  params: GetChunksForDocumentParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getChunksForDocument>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getGetChunksForDocumentQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * Get all chunks from a given collection.

:param collection_id: An id of a collection from which chunks are taken.
:param contextclue: ContextClue instance.
:return: A list of collection entries.
 * @summary Get All Chunks From Collection
 */
export const getAllChunksFromCollection = (
  params: GetAllChunksFromCollectionParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<CollectionEntry[]>(
    {
      url: `/embedding/get_all_chunks_from_collection`, method: 'GET',
      params, signal
    },
  );
}


export const getGetAllChunksFromCollectionQueryKey = (params: GetAllChunksFromCollectionParams,) => {
  return [`/embedding/get_all_chunks_from_collection`, ...(params ? [params] : [])] as const;
}


export const getGetAllChunksFromCollectionQueryOptions = <TData = Awaited<ReturnType<typeof getAllChunksFromCollection>>, TError = HTTPValidationError>(params: GetAllChunksFromCollectionParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllChunksFromCollection>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAllChunksFromCollectionQueryKey(params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAllChunksFromCollection>>> = ({ signal }) => getAllChunksFromCollection(params, signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getAllChunksFromCollection>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type GetAllChunksFromCollectionQueryResult = NonNullable<Awaited<ReturnType<typeof getAllChunksFromCollection>>>
export type GetAllChunksFromCollectionQueryError = HTTPValidationError


export function useGetAllChunksFromCollection<TData = Awaited<ReturnType<typeof getAllChunksFromCollection>>, TError = HTTPValidationError>(
  params: GetAllChunksFromCollectionParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllChunksFromCollection>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getAllChunksFromCollection>>,
        TError,
        Awaited<ReturnType<typeof getAllChunksFromCollection>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetAllChunksFromCollection<TData = Awaited<ReturnType<typeof getAllChunksFromCollection>>, TError = HTTPValidationError>(
  params: GetAllChunksFromCollectionParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllChunksFromCollection>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getAllChunksFromCollection>>,
        TError,
        Awaited<ReturnType<typeof getAllChunksFromCollection>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetAllChunksFromCollection<TData = Awaited<ReturnType<typeof getAllChunksFromCollection>>, TError = HTTPValidationError>(
  params: GetAllChunksFromCollectionParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllChunksFromCollection>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Get All Chunks From Collection
 */

export function useGetAllChunksFromCollection<TData = Awaited<ReturnType<typeof getAllChunksFromCollection>>, TError = HTTPValidationError>(
  params: GetAllChunksFromCollectionParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getAllChunksFromCollection>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getGetAllChunksFromCollectionQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * Get the mapping of chunk_id and keywords that are present in a given chunk of text.

:param document_id: An ID of a document.
:param keywords: A list of keywords that are used for matching.
:param collection_id: ID of a collection from which to take the keywords.
:param contextclue: ContextClue instance.
:return: A mapping of a form of chunk_id: list[keywords present in a text chunk]
 * @summary Get Keywords For Chunks
 */
export const getKeywordsForChunks = (
  getKeywordsForChunksBody: string[],
  params: GetKeywordsForChunksParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<GetKeywordsForChunks200>(
    {
      url: `/embedding/get_keywords_for_chunks`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: getKeywordsForChunksBody,
      params, signal
    },
  );
}



export const getGetKeywordsForChunksMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof getKeywordsForChunks>>, TError, { data: string[]; params: GetKeywordsForChunksParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof getKeywordsForChunks>>, TError, { data: string[]; params: GetKeywordsForChunksParams }, TContext> => {

  const mutationKey = ['getKeywordsForChunks'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof getKeywordsForChunks>>, { data: string[]; params: GetKeywordsForChunksParams }> = (props) => {
    const { data, params } = props ?? {};

    return getKeywordsForChunks(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type GetKeywordsForChunksMutationResult = NonNullable<Awaited<ReturnType<typeof getKeywordsForChunks>>>
export type GetKeywordsForChunksMutationBody = string[]
export type GetKeywordsForChunksMutationError = HTTPValidationError

/**
* @summary Get Keywords For Chunks
*/
export const useGetKeywordsForChunks = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof getKeywordsForChunks>>, TError, { data: string[]; params: GetKeywordsForChunksParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof getKeywordsForChunks>>,
      TError,
      { data: string[]; params: GetKeywordsForChunksParams },
      TContext
    > => {

  const mutationOptions = getGetKeywordsForChunksMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Add a graph topology to a collection.

:param collection_id: An id of a collection, to which a topology is added.
:param node_list: A list of Nodes that represent a graph.
:param contextclue: ContextClue instance.
:return: A CollectionModel that represent the information about collection.
* @summary Add Topology To Collection
*/
export const addTopologyToCollection = (
  nodeListInput: NodeListInput,
  params: AddTopologyToCollectionParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<CollectionModel>(
    {
      url: `/embedding/add_topology_to_collection`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: nodeListInput,
      params, signal
    },
  );
}



export const getAddTopologyToCollectionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof addTopologyToCollection>>, TError, { data: NodeListInput; params: AddTopologyToCollectionParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof addTopologyToCollection>>, TError, { data: NodeListInput; params: AddTopologyToCollectionParams }, TContext> => {

  const mutationKey = ['addTopologyToCollection'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof addTopologyToCollection>>, { data: NodeListInput; params: AddTopologyToCollectionParams }> = (props) => {
    const { data, params } = props ?? {};

    return addTopologyToCollection(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AddTopologyToCollectionMutationResult = NonNullable<Awaited<ReturnType<typeof addTopologyToCollection>>>
export type AddTopologyToCollectionMutationBody = NodeListInput
export type AddTopologyToCollectionMutationError = HTTPValidationError

/**
* @summary Add Topology To Collection
*/
export const useAddTopologyToCollection = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof addTopologyToCollection>>, TError, { data: NodeListInput; params: AddTopologyToCollectionParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof addTopologyToCollection>>,
      TError,
      { data: NodeListInput; params: AddTopologyToCollectionParams },
      TContext
    > => {

  const mutationOptions = getAddTopologyToCollectionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Get a filled topology graph ready for visualization.

:param collection_id: An id of a collection which is used to created a graph for    visualiztaion.
:param contextclue: ContextClue instance.
:return: A dictionary representing a graph ready for visualization.
* @summary Get Filled Topology Graph
*/
export const getFilledTopologyGraph = (
  params: GetFilledTopologyGraphParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<GetFilledTopologyGraph200>(
    {
      url: `/embedding/get_filled_topology_graph`, method: 'POST',
      params, signal
    },
  );
}



export const getGetFilledTopologyGraphMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof getFilledTopologyGraph>>, TError, { params: GetFilledTopologyGraphParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof getFilledTopologyGraph>>, TError, { params: GetFilledTopologyGraphParams }, TContext> => {

  const mutationKey = ['getFilledTopologyGraph'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof getFilledTopologyGraph>>, { params: GetFilledTopologyGraphParams }> = (props) => {
    const { params } = props ?? {};

    return getFilledTopologyGraph(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type GetFilledTopologyGraphMutationResult = NonNullable<Awaited<ReturnType<typeof getFilledTopologyGraph>>>

export type GetFilledTopologyGraphMutationError = HTTPValidationError

/**
* @summary Get Filled Topology Graph
*/
export const useGetFilledTopologyGraph = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof getFilledTopologyGraph>>, TError, { params: GetFilledTopologyGraphParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof getFilledTopologyGraph>>,
      TError,
      { params: GetFilledTopologyGraphParams },
      TContext
    > => {

  const mutationOptions = getGetFilledTopologyGraphMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Copies embeddings of source collection to target collection. If document_id is `None` then
all the embeddings are copied from one collection to another.
* @summary Copy Embeddings
*/
export const copyEmbeddings = (
  params: CopyEmbeddingsParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<boolean>(
    {
      url: `/embedding/copy_embeddings`, method: 'POST',
      params, signal
    },
  );
}



export const getCopyEmbeddingsMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof copyEmbeddings>>, TError, { params: CopyEmbeddingsParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof copyEmbeddings>>, TError, { params: CopyEmbeddingsParams }, TContext> => {

  const mutationKey = ['copyEmbeddings'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof copyEmbeddings>>, { params: CopyEmbeddingsParams }> = (props) => {
    const { params } = props ?? {};

    return copyEmbeddings(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type CopyEmbeddingsMutationResult = NonNullable<Awaited<ReturnType<typeof copyEmbeddings>>>

export type CopyEmbeddingsMutationError = HTTPValidationError

/**
* @summary Copy Embeddings
*/
export const useCopyEmbeddings = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof copyEmbeddings>>, TError, { params: CopyEmbeddingsParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof copyEmbeddings>>,
      TError,
      { params: CopyEmbeddingsParams },
      TContext
    > => {

  const mutationOptions = getCopyEmbeddingsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Reset collection by removing all the embeddings and information from documents
* @summary Reset Collection
*/
export const resetCollection = (
  params: ResetCollectionParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<boolean>(
    {
      url: `/embedding/reset_collection`, method: 'POST',
      params, signal
    },
  );
}



export const getResetCollectionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof resetCollection>>, TError, { params: ResetCollectionParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof resetCollection>>, TError, { params: ResetCollectionParams }, TContext> => {

  const mutationKey = ['resetCollection'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof resetCollection>>, { params: ResetCollectionParams }> = (props) => {
    const { params } = props ?? {};

    return resetCollection(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ResetCollectionMutationResult = NonNullable<Awaited<ReturnType<typeof resetCollection>>>

export type ResetCollectionMutationError = HTTPValidationError

/**
* @summary Reset Collection
*/
export const useResetCollection = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof resetCollection>>, TError, { params: ResetCollectionParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof resetCollection>>,
      TError,
      { params: ResetCollectionParams },
      TContext
    > => {

  const mutationOptions = getResetCollectionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Update metadata to extract for a given collection.

Args:
collection_id: An id of a collection.
metadata_to_extract: A dictionary with metadata to extract.
Returns:
True if the metadata was updated successfully, False otherwise.
* @summary Update Collection Metadata To Extract
*/
export const updateCollectionMetadataToExtract = (
  updateCollectionMetadataToExtractBody: UpdateCollectionMetadataToExtractBody,
  params: UpdateCollectionMetadataToExtractParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<boolean>(
    {
      url: `/embedding/update_collection_metadata_to_extract`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: updateCollectionMetadataToExtractBody,
      params, signal
    },
  );
}



export const getUpdateCollectionMetadataToExtractMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof updateCollectionMetadataToExtract>>, TError, { data: UpdateCollectionMetadataToExtractBody; params: UpdateCollectionMetadataToExtractParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof updateCollectionMetadataToExtract>>, TError, { data: UpdateCollectionMetadataToExtractBody; params: UpdateCollectionMetadataToExtractParams }, TContext> => {

  const mutationKey = ['updateCollectionMetadataToExtract'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof updateCollectionMetadataToExtract>>, { data: UpdateCollectionMetadataToExtractBody; params: UpdateCollectionMetadataToExtractParams }> = (props) => {
    const { data, params } = props ?? {};

    return updateCollectionMetadataToExtract(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type UpdateCollectionMetadataToExtractMutationResult = NonNullable<Awaited<ReturnType<typeof updateCollectionMetadataToExtract>>>
export type UpdateCollectionMetadataToExtractMutationBody = UpdateCollectionMetadataToExtractBody
export type UpdateCollectionMetadataToExtractMutationError = HTTPValidationError

/**
* @summary Update Collection Metadata To Extract
*/
export const useUpdateCollectionMetadataToExtract = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof updateCollectionMetadataToExtract>>, TError, { data: UpdateCollectionMetadataToExtractBody; params: UpdateCollectionMetadataToExtractParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof updateCollectionMetadataToExtract>>,
      TError,
      { data: UpdateCollectionMetadataToExtractBody; params: UpdateCollectionMetadataToExtractParams },
      TContext
    > => {

  const mutationOptions = getUpdateCollectionMetadataToExtractMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
