/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation,
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import type {
  GetActions200,
  HTTPValidationError,
  RateActionParams
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * List all actions available in the action database
 * @summary List All Actions
 */
export const getActions = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<GetActions200>(
    {
      url: `/actions`, method: 'GET', signal
    },
  );
}


export const getGetActionsQueryKey = () => {
  return [`/actions`] as const;
}


export const getGetActionsQueryOptions = <TData = Awaited<ReturnType<typeof getActions>>, TError = unknown>(options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getActions>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetActionsQueryKey();



  const queryFn: QueryFunction<Awaited<ReturnType<typeof getActions>>> = ({ signal }) => getActions(signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getActions>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type GetActionsQueryResult = NonNullable<Awaited<ReturnType<typeof getActions>>>
export type GetActionsQueryError = unknown


export function useGetActions<TData = Awaited<ReturnType<typeof getActions>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getActions>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getActions>>,
        TError,
        Awaited<ReturnType<typeof getActions>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetActions<TData = Awaited<ReturnType<typeof getActions>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getActions>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getActions>>,
        TError,
        Awaited<ReturnType<typeof getActions>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetActions<TData = Awaited<ReturnType<typeof getActions>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getActions>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary List All Actions
 */

export function useGetActions<TData = Awaited<ReturnType<typeof getActions>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getActions>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getGetActionsQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * Reset action database
 * @summary Reset Action Database
 */
export const deleteAction = (

) => {


  return apiUrlMutator<number>(
    {
      url: `/actions`, method: 'DELETE'
    },
  );
}



export const getDeleteActionMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteAction>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof deleteAction>>, TError, void, TContext> => {

  const mutationKey = ['deleteAction'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteAction>>, void> = () => {


    return deleteAction()
  }




  return { mutationFn, ...mutationOptions }
}

export type DeleteActionMutationResult = NonNullable<Awaited<ReturnType<typeof deleteAction>>>

export type DeleteActionMutationError = unknown

/**
* @summary Reset Action Database
*/
export const useDeleteAction = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteAction>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof deleteAction>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getDeleteActionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Rate action
* @summary Rate Action
*/
export const rateAction = (
  params: RateActionParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<boolean>(
    {
      url: `/actions/rate_action`, method: 'POST',
      params, signal
    },
  );
}



export const getRateActionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof rateAction>>, TError, { params: RateActionParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof rateAction>>, TError, { params: RateActionParams }, TContext> => {

  const mutationKey = ['rateAction'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof rateAction>>, { params: RateActionParams }> = (props) => {
    const { params } = props ?? {};

    return rateAction(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type RateActionMutationResult = NonNullable<Awaited<ReturnType<typeof rateAction>>>

export type RateActionMutationError = HTTPValidationError

/**
* @summary Rate Action
*/
export const useRateAction = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof rateAction>>, TError, { params: RateActionParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof rateAction>>,
      TError,
      { params: RateActionParams },
      TContext
    > => {

  const mutationOptions = getRateActionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
