/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation,
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import type {
  AsyncProcessingTestsBody,
  AsyncProcessingTestsParams,
  BodyAnonymizeText,
  BodyDocumentExtractionAndSplitting,
  BodyExtractKeywords,
  BodyProcess,
  BodySplitDocument,
  CopyCollection200,
  CopyCollectionParams,
  CreateCollection201,
  CreateCollectionParams,
  DeleteCollection200,
  DocumentExtractionAndSplitting200,
  ExtractDocument200,
  ExtractDocumentBody,
  ExtractDocumentParams,
  ExtractEntitiesGraphFromDocuments200,
  ExtractEntitiesGraphSender1200,
  ExtractEntitiesGraphSender2200,
  ExtractEntitiesGraphSenderEnrichmentFuzzy200,
  ExtractEntitiesGraphSenderEnrichmentMatching200,
  ExtractEntitiesGraphSenderEnrichmentModels200,
  ExtractEntitiesGraphSenderFileNodes200,
  ExtractEntitiesGraphSenderIdentifiers200,
  ExtractEntitiesGraphSenderInstances200,
  ExtractEntitiesGraphSenderJoinIdentifiers200,
  ExtractEntitiesGraphSenderModelFileNodes200,
  ExtractEntitiesGraphSenderTables200,
  ExtractGraphFromDocument200,
  ExtractGraphFromDocumentParams,
  GetProcessingStatus200,
  GetProcessingStatusParams,
  HTTPValidationError,
  ProcessDocumentsBody,
  ProcessDocumentsParams,
  ProcessDocumentsSync200,
  ProcessDocumentsSyncBody,
  ProcessDocumentsSyncParams,
  ProcessParams,
  ProcessStatusChangeParams,
  ProcessStep202,
  ProcessStepParams,
  ResetProcessing200,
  ResetProcessingParams,
  SplitDocument200,
  StopProcessing200,
  StopProcessingParams
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * @summary Process Documents
 */
export const processDocuments = (
  processDocumentsBody: ProcessDocumentsBody,
  params?: ProcessDocumentsParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/process_documents`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: processDocumentsBody,
      params, signal
    },
  );
}



export const getProcessDocumentsMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof processDocuments>>, TError, { data: ProcessDocumentsBody; params?: ProcessDocumentsParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof processDocuments>>, TError, { data: ProcessDocumentsBody; params?: ProcessDocumentsParams }, TContext> => {

  const mutationKey = ['processDocuments'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof processDocuments>>, { data: ProcessDocumentsBody; params?: ProcessDocumentsParams }> = (props) => {
    const { data, params } = props ?? {};

    return processDocuments(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ProcessDocumentsMutationResult = NonNullable<Awaited<ReturnType<typeof processDocuments>>>
export type ProcessDocumentsMutationBody = ProcessDocumentsBody
export type ProcessDocumentsMutationError = HTTPValidationError

/**
* @summary Process Documents
*/
export const useProcessDocuments = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof processDocuments>>, TError, { data: ProcessDocumentsBody; params?: ProcessDocumentsParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof processDocuments>>,
      TError,
      { data: ProcessDocumentsBody; params?: ProcessDocumentsParams },
      TContext
    > => {

  const mutationOptions = getProcessDocumentsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* A testing endpoint which does what /process_documents endpoint but in the foreground
* @summary Process Documents Sync
*/
export const processDocumentsSync = (
  processDocumentsSyncBody: ProcessDocumentsSyncBody,
  params: ProcessDocumentsSyncParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<ProcessDocumentsSync200>(
    {
      url: `/process_documents_sync`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: processDocumentsSyncBody,
      params, signal
    },
  );
}



export const getProcessDocumentsSyncMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof processDocumentsSync>>, TError, { data: ProcessDocumentsSyncBody; params: ProcessDocumentsSyncParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof processDocumentsSync>>, TError, { data: ProcessDocumentsSyncBody; params: ProcessDocumentsSyncParams }, TContext> => {

  const mutationKey = ['processDocumentsSync'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof processDocumentsSync>>, { data: ProcessDocumentsSyncBody; params: ProcessDocumentsSyncParams }> = (props) => {
    const { data, params } = props ?? {};

    return processDocumentsSync(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ProcessDocumentsSyncMutationResult = NonNullable<Awaited<ReturnType<typeof processDocumentsSync>>>
export type ProcessDocumentsSyncMutationBody = ProcessDocumentsSyncBody
export type ProcessDocumentsSyncMutationError = HTTPValidationError

/**
* @summary Process Documents Sync
*/
export const useProcessDocumentsSync = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof processDocumentsSync>>, TError, { data: ProcessDocumentsSyncBody; params: ProcessDocumentsSyncParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof processDocumentsSync>>,
      TError,
      { data: ProcessDocumentsSyncBody; params: ProcessDocumentsSyncParams },
      TContext
    > => {

  const mutationOptions = getProcessDocumentsSyncMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Stop Processing
*/
export const stopProcessing = (
  params?: StopProcessingParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<StopProcessing200>(
    {
      url: `/stop_processing`, method: 'POST',
      params, signal
    },
  );
}



export const getStopProcessingMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof stopProcessing>>, TError, { params?: StopProcessingParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof stopProcessing>>, TError, { params?: StopProcessingParams }, TContext> => {

  const mutationKey = ['stopProcessing'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof stopProcessing>>, { params?: StopProcessingParams }> = (props) => {
    const { params } = props ?? {};

    return stopProcessing(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type StopProcessingMutationResult = NonNullable<Awaited<ReturnType<typeof stopProcessing>>>

export type StopProcessingMutationError = HTTPValidationError

/**
* @summary Stop Processing
*/
export const useStopProcessing = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof stopProcessing>>, TError, { params?: StopProcessingParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof stopProcessing>>,
      TError,
      { params?: StopProcessingParams },
      TContext
    > => {

  const mutationOptions = getStopProcessingMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Anonymize Text
*/
export const anonymizeText = (
  bodyAnonymizeText: BodyAnonymizeText,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/anonymize_text`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyAnonymizeText, signal
    },
  );
}



export const getAnonymizeTextMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof anonymizeText>>, TError, { data: BodyAnonymizeText }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof anonymizeText>>, TError, { data: BodyAnonymizeText }, TContext> => {

  const mutationKey = ['anonymizeText'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof anonymizeText>>, { data: BodyAnonymizeText }> = (props) => {
    const { data } = props ?? {};

    return anonymizeText(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AnonymizeTextMutationResult = NonNullable<Awaited<ReturnType<typeof anonymizeText>>>
export type AnonymizeTextMutationBody = BodyAnonymizeText
export type AnonymizeTextMutationError = HTTPValidationError

/**
* @summary Anonymize Text
*/
export const useAnonymizeText = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof anonymizeText>>, TError, { data: BodyAnonymizeText }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof anonymizeText>>,
      TError,
      { data: BodyAnonymizeText },
      TContext
    > => {

  const mutationOptions = getAnonymizeTextMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Process
*/
export const process = (
  bodyProcess: BodyProcess,
  params: ProcessParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/process`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyProcess,
      params, signal
    },
  );
}



export const getProcessMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof process>>, TError, { data: BodyProcess; params: ProcessParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof process>>, TError, { data: BodyProcess; params: ProcessParams }, TContext> => {

  const mutationKey = ['process'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof process>>, { data: BodyProcess; params: ProcessParams }> = (props) => {
    const { data, params } = props ?? {};

    return process(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ProcessMutationResult = NonNullable<Awaited<ReturnType<typeof process>>>
export type ProcessMutationBody = BodyProcess
export type ProcessMutationError = HTTPValidationError

/**
* @summary Process
*/
export const useProcess = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof process>>, TError, { data: BodyProcess; params: ProcessParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof process>>,
      TError,
      { data: BodyProcess; params: ProcessParams },
      TContext
    > => {

  const mutationOptions = getProcessMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Process Status Change
*/
export const processStatusChange = (
  params: ProcessStatusChangeParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/process_status_change`, method: 'POST',
      params, signal
    },
  );
}



export const getProcessStatusChangeMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof processStatusChange>>, TError, { params: ProcessStatusChangeParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof processStatusChange>>, TError, { params: ProcessStatusChangeParams }, TContext> => {

  const mutationKey = ['processStatusChange'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof processStatusChange>>, { params: ProcessStatusChangeParams }> = (props) => {
    const { params } = props ?? {};

    return processStatusChange(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ProcessStatusChangeMutationResult = NonNullable<Awaited<ReturnType<typeof processStatusChange>>>

export type ProcessStatusChangeMutationError = HTTPValidationError

/**
* @summary Process Status Change
*/
export const useProcessStatusChange = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof processStatusChange>>, TError, { params: ProcessStatusChangeParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof processStatusChange>>,
      TError,
      { params: ProcessStatusChangeParams },
      TContext
    > => {

  const mutationOptions = getProcessStatusChangeMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Document Extraction And Splitting
*/
export const documentExtractionAndSplitting = (
  bodyDocumentExtractionAndSplitting: BodyDocumentExtractionAndSplitting,
  signal?: AbortSignal
) => {


  return apiUrlMutator<DocumentExtractionAndSplitting200>(
    {
      url: `/document_extraction_and_splitting`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyDocumentExtractionAndSplitting, signal
    },
  );
}



export const getDocumentExtractionAndSplittingMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof documentExtractionAndSplitting>>, TError, { data: BodyDocumentExtractionAndSplitting }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof documentExtractionAndSplitting>>, TError, { data: BodyDocumentExtractionAndSplitting }, TContext> => {

  const mutationKey = ['documentExtractionAndSplitting'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof documentExtractionAndSplitting>>, { data: BodyDocumentExtractionAndSplitting }> = (props) => {
    const { data } = props ?? {};

    return documentExtractionAndSplitting(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type DocumentExtractionAndSplittingMutationResult = NonNullable<Awaited<ReturnType<typeof documentExtractionAndSplitting>>>
export type DocumentExtractionAndSplittingMutationBody = BodyDocumentExtractionAndSplitting
export type DocumentExtractionAndSplittingMutationError = HTTPValidationError

/**
* @summary Document Extraction And Splitting
*/
export const useDocumentExtractionAndSplitting = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof documentExtractionAndSplitting>>, TError, { data: BodyDocumentExtractionAndSplitting }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof documentExtractionAndSplitting>>,
      TError,
      { data: BodyDocumentExtractionAndSplitting },
      TContext
    > => {

  const mutationOptions = getDocumentExtractionAndSplittingMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract document chunks from document using its id, where extraction is configured by
`extraction_config`.
* @summary Extract
*/
export const extractDocument = (
  extractDocumentBody: ExtractDocumentBody,
  params: ExtractDocumentParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractDocument200>(
    {
      url: `/extract`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: extractDocumentBody,
      params, signal
    },
  );
}



export const getExtractDocumentMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractDocument>>, TError, { data: ExtractDocumentBody; params: ExtractDocumentParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractDocument>>, TError, { data: ExtractDocumentBody; params: ExtractDocumentParams }, TContext> => {

  const mutationKey = ['extractDocument'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractDocument>>, { data: ExtractDocumentBody; params: ExtractDocumentParams }> = (props) => {
    const { data, params } = props ?? {};

    return extractDocument(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractDocumentMutationResult = NonNullable<Awaited<ReturnType<typeof extractDocument>>>
export type ExtractDocumentMutationBody = ExtractDocumentBody
export type ExtractDocumentMutationError = HTTPValidationError

/**
* @summary Extract
*/
export const useExtractDocument = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractDocument>>, TError, { data: ExtractDocumentBody; params: ExtractDocumentParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractDocument>>,
      TError,
      { data: ExtractDocumentBody; params: ExtractDocumentParams },
      TContext
    > => {

  const mutationOptions = getExtractDocumentMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Split `document_chunks` using `splitter_config`
* @summary Split
*/
export const splitDocument = (
  bodySplitDocument: BodySplitDocument,
  signal?: AbortSignal
) => {


  return apiUrlMutator<SplitDocument200>(
    {
      url: `/split`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodySplitDocument, signal
    },
  );
}



export const getSplitDocumentMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof splitDocument>>, TError, { data: BodySplitDocument }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof splitDocument>>, TError, { data: BodySplitDocument }, TContext> => {

  const mutationKey = ['splitDocument'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof splitDocument>>, { data: BodySplitDocument }> = (props) => {
    const { data } = props ?? {};

    return splitDocument(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type SplitDocumentMutationResult = NonNullable<Awaited<ReturnType<typeof splitDocument>>>
export type SplitDocumentMutationBody = BodySplitDocument
export type SplitDocumentMutationError = HTTPValidationError

/**
* @summary Split
*/
export const useSplitDocument = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof splitDocument>>, TError, { data: BodySplitDocument }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof splitDocument>>,
      TError,
      { data: BodySplitDocument },
      TContext
    > => {

  const mutationOptions = getSplitDocumentMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract keywords from text
* @summary Extract Keywords
*/
export const extractKeywords = (
  bodyExtractKeywords: BodyExtractKeywords,
  signal?: AbortSignal
) => {


  return apiUrlMutator<string[][]>(
    {
      url: `/extract_keywords`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyExtractKeywords, signal
    },
  );
}



export const getExtractKeywordsMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractKeywords>>, TError, { data: BodyExtractKeywords }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractKeywords>>, TError, { data: BodyExtractKeywords }, TContext> => {

  const mutationKey = ['extractKeywords'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractKeywords>>, { data: BodyExtractKeywords }> = (props) => {
    const { data } = props ?? {};

    return extractKeywords(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractKeywordsMutationResult = NonNullable<Awaited<ReturnType<typeof extractKeywords>>>
export type ExtractKeywordsMutationBody = BodyExtractKeywords
export type ExtractKeywordsMutationError = HTTPValidationError

/**
* @summary Extract Keywords
*/
export const useExtractKeywords = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractKeywords>>, TError, { data: BodyExtractKeywords }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractKeywords>>,
      TError,
      { data: BodyExtractKeywords },
      TContext
    > => {

  const mutationOptions = getExtractKeywordsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract a graph of dependencies present in the document.

:param document_id: An ID of a document.
:param sub_node: A name of a sub_node, which if present chooses only those graph paths that    contain it.
:param contextclue: ContextClue instance.
:return: A graph representation taken from a given file.
* @summary Extract Graph
*/
export const extractGraphFromDocument = (
  params: ExtractGraphFromDocumentParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractGraphFromDocument200>(
    {
      url: `/extract_graph_from_document`, method: 'POST',
      params, signal
    },
  );
}



export const getExtractGraphFromDocumentMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractGraphFromDocument>>, TError, { params: ExtractGraphFromDocumentParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractGraphFromDocument>>, TError, { params: ExtractGraphFromDocumentParams }, TContext> => {

  const mutationKey = ['extractGraphFromDocument'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractGraphFromDocument>>, { params: ExtractGraphFromDocumentParams }> = (props) => {
    const { params } = props ?? {};

    return extractGraphFromDocument(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractGraphFromDocumentMutationResult = NonNullable<Awaited<ReturnType<typeof extractGraphFromDocument>>>

export type ExtractGraphFromDocumentMutationError = HTTPValidationError

/**
* @summary Extract Graph
*/
export const useExtractGraphFromDocument = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractGraphFromDocument>>, TError, { params: ExtractGraphFromDocumentParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractGraphFromDocument>>,
      TError,
      { params: ExtractGraphFromDocumentParams },
      TContext
    > => {

  const mutationOptions = getExtractGraphFromDocumentMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Async Processing Tests
*/
export const asyncProcessingTests = (
  asyncProcessingTestsBody: AsyncProcessingTestsBody,
  params?: AsyncProcessingTestsParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/async_processing_tests`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: asyncProcessingTestsBody,
      params, signal
    },
  );
}



export const getAsyncProcessingTestsMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof asyncProcessingTests>>, TError, { data: AsyncProcessingTestsBody; params?: AsyncProcessingTestsParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof asyncProcessingTests>>, TError, { data: AsyncProcessingTestsBody; params?: AsyncProcessingTestsParams }, TContext> => {

  const mutationKey = ['asyncProcessingTests'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof asyncProcessingTests>>, { data: AsyncProcessingTestsBody; params?: AsyncProcessingTestsParams }> = (props) => {
    const { data, params } = props ?? {};

    return asyncProcessingTests(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AsyncProcessingTestsMutationResult = NonNullable<Awaited<ReturnType<typeof asyncProcessingTests>>>
export type AsyncProcessingTestsMutationBody = AsyncProcessingTestsBody
export type AsyncProcessingTestsMutationError = HTTPValidationError

/**
* @summary Async Processing Tests
*/
export const useAsyncProcessingTests = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof asyncProcessingTests>>, TError, { data: AsyncProcessingTestsBody; params?: AsyncProcessingTestsParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof asyncProcessingTests>>,
      TError,
      { data: AsyncProcessingTestsBody; params?: AsyncProcessingTestsParams },
      TContext
    > => {

  const mutationOptions = getAsyncProcessingTestsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract a graph of entities present in the documents.

:param contextclue: ContextClue instance.
:return: A graph representation.
* @summary Extract Entities Graph Route
*/
export const extractEntitiesGraphFromDocuments = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractEntitiesGraphFromDocuments200>(
    {
      url: `/extract_entities_graph_from_documents`, method: 'POST', signal
    },
  );
}



export const getExtractEntitiesGraphFromDocumentsMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphFromDocuments>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphFromDocuments>>, TError, void, TContext> => {

  const mutationKey = ['extractEntitiesGraphFromDocuments'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractEntitiesGraphFromDocuments>>, void> = () => {


    return extractEntitiesGraphFromDocuments()
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractEntitiesGraphFromDocumentsMutationResult = NonNullable<Awaited<ReturnType<typeof extractEntitiesGraphFromDocuments>>>

export type ExtractEntitiesGraphFromDocumentsMutationError = unknown

/**
* @summary Extract Entities Graph Route
*/
export const useExtractEntitiesGraphFromDocuments = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphFromDocuments>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractEntitiesGraphFromDocuments>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getExtractEntitiesGraphFromDocumentsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract a graph of entities present in the documents for sender1.

:param contextclue: ContextClue instance.
:return: A graph representation for sender1.
* @summary Extract Entities Graph Sender1 Route
*/
export const extractEntitiesGraphSender1 = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractEntitiesGraphSender1200>(
    {
      url: `/extract_entities_graph_sender1`, method: 'POST', signal
    },
  );
}



export const getExtractEntitiesGraphSender1MutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSender1>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSender1>>, TError, void, TContext> => {

  const mutationKey = ['extractEntitiesGraphSender1'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractEntitiesGraphSender1>>, void> = () => {


    return extractEntitiesGraphSender1()
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractEntitiesGraphSender1MutationResult = NonNullable<Awaited<ReturnType<typeof extractEntitiesGraphSender1>>>

export type ExtractEntitiesGraphSender1MutationError = unknown

/**
* @summary Extract Entities Graph Sender1 Route
*/
export const useExtractEntitiesGraphSender1 = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSender1>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractEntitiesGraphSender1>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getExtractEntitiesGraphSender1MutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract a graph of entities present in the documents for sender2.

:param contextclue: ContextClue instance.
:return: A graph representation for sender2.
* @summary Extract Entities Graph Sender2 Route
*/
export const extractEntitiesGraphSender2 = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractEntitiesGraphSender2200>(
    {
      url: `/extract_entities_graph_sender2`, method: 'POST', signal
    },
  );
}



export const getExtractEntitiesGraphSender2MutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSender2>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSender2>>, TError, void, TContext> => {

  const mutationKey = ['extractEntitiesGraphSender2'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractEntitiesGraphSender2>>, void> = () => {


    return extractEntitiesGraphSender2()
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractEntitiesGraphSender2MutationResult = NonNullable<Awaited<ReturnType<typeof extractEntitiesGraphSender2>>>

export type ExtractEntitiesGraphSender2MutationError = unknown

/**
* @summary Extract Entities Graph Sender2 Route
*/
export const useExtractEntitiesGraphSender2 = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSender2>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractEntitiesGraphSender2>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getExtractEntitiesGraphSender2MutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract a graph of entities - file nodes step.

:param contextclue: ContextClue instance.
:return: A graph representation for file nodes.
* @summary Extract Entities Graph Sender File Nodes Route
*/
export const extractEntitiesGraphSenderFileNodes = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractEntitiesGraphSenderFileNodes200>(
    {
      url: `/extract_entities_graph_sender_file_nodes`, method: 'POST', signal
    },
  );
}



export const getExtractEntitiesGraphSenderFileNodesMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderFileNodes>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderFileNodes>>, TError, void, TContext> => {

  const mutationKey = ['extractEntitiesGraphSenderFileNodes'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractEntitiesGraphSenderFileNodes>>, void> = () => {


    return extractEntitiesGraphSenderFileNodes()
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractEntitiesGraphSenderFileNodesMutationResult = NonNullable<Awaited<ReturnType<typeof extractEntitiesGraphSenderFileNodes>>>

export type ExtractEntitiesGraphSenderFileNodesMutationError = unknown

/**
* @summary Extract Entities Graph Sender File Nodes Route
*/
export const useExtractEntitiesGraphSenderFileNodes = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderFileNodes>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractEntitiesGraphSenderFileNodes>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getExtractEntitiesGraphSenderFileNodesMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract a graph of entities - model file nodes step.

:param contextclue: ContextClue instance.
:return: A graph representation for model file nodes.
* @summary Extract Entities Graph Sender Model File Nodes Route
*/
export const extractEntitiesGraphSenderModelFileNodes = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractEntitiesGraphSenderModelFileNodes200>(
    {
      url: `/extract_entities_graph_sender_model_file_nodes`, method: 'POST', signal
    },
  );
}



export const getExtractEntitiesGraphSenderModelFileNodesMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderModelFileNodes>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderModelFileNodes>>, TError, void, TContext> => {

  const mutationKey = ['extractEntitiesGraphSenderModelFileNodes'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractEntitiesGraphSenderModelFileNodes>>, void> = () => {


    return extractEntitiesGraphSenderModelFileNodes()
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractEntitiesGraphSenderModelFileNodesMutationResult = NonNullable<Awaited<ReturnType<typeof extractEntitiesGraphSenderModelFileNodes>>>

export type ExtractEntitiesGraphSenderModelFileNodesMutationError = unknown

/**
* @summary Extract Entities Graph Sender Model File Nodes Route
*/
export const useExtractEntitiesGraphSenderModelFileNodes = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderModelFileNodes>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractEntitiesGraphSenderModelFileNodes>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getExtractEntitiesGraphSenderModelFileNodesMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract a graph of entities - tables step.

:param contextclue: ContextClue instance.
:return: A graph representation for tables.
* @summary Extract Entities Graph Sender Tables Route
*/
export const extractEntitiesGraphSenderTables = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractEntitiesGraphSenderTables200>(
    {
      url: `/extract_entities_graph_sender_tables`, method: 'POST', signal
    },
  );
}



export const getExtractEntitiesGraphSenderTablesMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderTables>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderTables>>, TError, void, TContext> => {

  const mutationKey = ['extractEntitiesGraphSenderTables'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractEntitiesGraphSenderTables>>, void> = () => {


    return extractEntitiesGraphSenderTables()
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractEntitiesGraphSenderTablesMutationResult = NonNullable<Awaited<ReturnType<typeof extractEntitiesGraphSenderTables>>>

export type ExtractEntitiesGraphSenderTablesMutationError = unknown

/**
* @summary Extract Entities Graph Sender Tables Route
*/
export const useExtractEntitiesGraphSenderTables = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderTables>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractEntitiesGraphSenderTables>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getExtractEntitiesGraphSenderTablesMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract a graph of entities - identifiers step.

:param contextclue: ContextClue instance.
:return: A graph representation for identifiers.
* @summary Extract Entities Graph Sender Identifiers Route
*/
export const extractEntitiesGraphSenderIdentifiers = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractEntitiesGraphSenderIdentifiers200>(
    {
      url: `/extract_entities_graph_sender_identifiers`, method: 'POST', signal
    },
  );
}



export const getExtractEntitiesGraphSenderIdentifiersMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderIdentifiers>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderIdentifiers>>, TError, void, TContext> => {

  const mutationKey = ['extractEntitiesGraphSenderIdentifiers'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractEntitiesGraphSenderIdentifiers>>, void> = () => {


    return extractEntitiesGraphSenderIdentifiers()
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractEntitiesGraphSenderIdentifiersMutationResult = NonNullable<Awaited<ReturnType<typeof extractEntitiesGraphSenderIdentifiers>>>

export type ExtractEntitiesGraphSenderIdentifiersMutationError = unknown

/**
* @summary Extract Entities Graph Sender Identifiers Route
*/
export const useExtractEntitiesGraphSenderIdentifiers = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderIdentifiers>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractEntitiesGraphSenderIdentifiers>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getExtractEntitiesGraphSenderIdentifiersMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract a graph of entities - instances step.

:param contextclue: ContextClue instance.
:return: A graph representation for instances.
* @summary Extract Entities Graph Sender Instances Route
*/
export const extractEntitiesGraphSenderInstances = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractEntitiesGraphSenderInstances200>(
    {
      url: `/extract_entities_graph_sender_instances`, method: 'POST', signal
    },
  );
}



export const getExtractEntitiesGraphSenderInstancesMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderInstances>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderInstances>>, TError, void, TContext> => {

  const mutationKey = ['extractEntitiesGraphSenderInstances'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractEntitiesGraphSenderInstances>>, void> = () => {


    return extractEntitiesGraphSenderInstances()
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractEntitiesGraphSenderInstancesMutationResult = NonNullable<Awaited<ReturnType<typeof extractEntitiesGraphSenderInstances>>>

export type ExtractEntitiesGraphSenderInstancesMutationError = unknown

/**
* @summary Extract Entities Graph Sender Instances Route
*/
export const useExtractEntitiesGraphSenderInstances = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderInstances>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractEntitiesGraphSenderInstances>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getExtractEntitiesGraphSenderInstancesMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract a graph of entities - enrichment matching step.

:param contextclue: ContextClue instance.
:return: A graph representation for enrichment matching.
* @summary Extract Entities Graph Sender Enrichment Matching Route
*/
export const extractEntitiesGraphSenderEnrichmentMatching = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractEntitiesGraphSenderEnrichmentMatching200>(
    {
      url: `/extract_entities_graph_sender_enrichment_matching`, method: 'POST', signal
    },
  );
}



export const getExtractEntitiesGraphSenderEnrichmentMatchingMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentMatching>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentMatching>>, TError, void, TContext> => {

  const mutationKey = ['extractEntitiesGraphSenderEnrichmentMatching'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentMatching>>, void> = () => {


    return extractEntitiesGraphSenderEnrichmentMatching()
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractEntitiesGraphSenderEnrichmentMatchingMutationResult = NonNullable<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentMatching>>>

export type ExtractEntitiesGraphSenderEnrichmentMatchingMutationError = unknown

/**
* @summary Extract Entities Graph Sender Enrichment Matching Route
*/
export const useExtractEntitiesGraphSenderEnrichmentMatching = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentMatching>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentMatching>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getExtractEntitiesGraphSenderEnrichmentMatchingMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract a graph of entities - enrichment models step.

:param contextclue: ContextClue instance.
:return: A graph representation for enrichment models.
* @summary Extract Entities Graph Sender Enrichment Models Route
*/
export const extractEntitiesGraphSenderEnrichmentModels = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractEntitiesGraphSenderEnrichmentModels200>(
    {
      url: `/extract_entities_graph_sender_enrichment_models`, method: 'POST', signal
    },
  );
}



export const getExtractEntitiesGraphSenderEnrichmentModelsMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentModels>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentModels>>, TError, void, TContext> => {

  const mutationKey = ['extractEntitiesGraphSenderEnrichmentModels'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentModels>>, void> = () => {


    return extractEntitiesGraphSenderEnrichmentModels()
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractEntitiesGraphSenderEnrichmentModelsMutationResult = NonNullable<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentModels>>>

export type ExtractEntitiesGraphSenderEnrichmentModelsMutationError = unknown

/**
* @summary Extract Entities Graph Sender Enrichment Models Route
*/
export const useExtractEntitiesGraphSenderEnrichmentModels = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentModels>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentModels>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getExtractEntitiesGraphSenderEnrichmentModelsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract a graph of entities - enrichment fuzzy step.

:param contextclue: ContextClue instance.
:return: A graph representation for enrichment fuzzy.
* @summary Extract Entities Graph Sender Enrichment Fuzzy Route
*/
export const extractEntitiesGraphSenderEnrichmentFuzzy = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractEntitiesGraphSenderEnrichmentFuzzy200>(
    {
      url: `/extract_entities_graph_sender_enrichment_fuzzy`, method: 'POST', signal
    },
  );
}



export const getExtractEntitiesGraphSenderEnrichmentFuzzyMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentFuzzy>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentFuzzy>>, TError, void, TContext> => {

  const mutationKey = ['extractEntitiesGraphSenderEnrichmentFuzzy'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentFuzzy>>, void> = () => {


    return extractEntitiesGraphSenderEnrichmentFuzzy()
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractEntitiesGraphSenderEnrichmentFuzzyMutationResult = NonNullable<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentFuzzy>>>

export type ExtractEntitiesGraphSenderEnrichmentFuzzyMutationError = unknown

/**
* @summary Extract Entities Graph Sender Enrichment Fuzzy Route
*/
export const useExtractEntitiesGraphSenderEnrichmentFuzzy = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentFuzzy>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractEntitiesGraphSenderEnrichmentFuzzy>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getExtractEntitiesGraphSenderEnrichmentFuzzyMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract a graph of entities - join identifiers step.

:param contextclue: ContextClue instance.
:return: A graph representation for join identifiers.
* @summary Extract Entities Graph Sender Join Identifiers Route
*/
export const extractEntitiesGraphSenderJoinIdentifiers = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractEntitiesGraphSenderJoinIdentifiers200>(
    {
      url: `/extract_entities_graph_sender_join_identifiers`, method: 'POST', signal
    },
  );
}



export const getExtractEntitiesGraphSenderJoinIdentifiersMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderJoinIdentifiers>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderJoinIdentifiers>>, TError, void, TContext> => {

  const mutationKey = ['extractEntitiesGraphSenderJoinIdentifiers'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractEntitiesGraphSenderJoinIdentifiers>>, void> = () => {


    return extractEntitiesGraphSenderJoinIdentifiers()
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractEntitiesGraphSenderJoinIdentifiersMutationResult = NonNullable<Awaited<ReturnType<typeof extractEntitiesGraphSenderJoinIdentifiers>>>

export type ExtractEntitiesGraphSenderJoinIdentifiersMutationError = unknown

/**
* @summary Extract Entities Graph Sender Join Identifiers Route
*/
export const useExtractEntitiesGraphSenderJoinIdentifiers = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractEntitiesGraphSenderJoinIdentifiers>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractEntitiesGraphSenderJoinIdentifiers>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getExtractEntitiesGraphSenderJoinIdentifiersMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Process a single step of the graph extraction in the background
* @summary Process Step
*/
export const processStep = (
  params: ProcessStepParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<ProcessStep202>(
    {
      url: `/process_step`, method: 'POST',
      params, signal
    },
  );
}



export const getProcessStepMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof processStep>>, TError, { params: ProcessStepParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof processStep>>, TError, { params: ProcessStepParams }, TContext> => {

  const mutationKey = ['processStep'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof processStep>>, { params: ProcessStepParams }> = (props) => {
    const { params } = props ?? {};

    return processStep(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ProcessStepMutationResult = NonNullable<Awaited<ReturnType<typeof processStep>>>

export type ProcessStepMutationError = HTTPValidationError

/**
* @summary Process Step
*/
export const useProcessStep = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof processStep>>, TError, { params: ProcessStepParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof processStep>>,
      TError,
      { params: ProcessStepParams },
      TContext
    > => {

  const mutationOptions = getProcessStepMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Get current processing status from the database with detailed information
* @summary Get Processing Status
*/
export const getProcessingStatus = (
  params?: GetProcessingStatusParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<GetProcessingStatus200>(
    {
      url: `/processing_status`, method: 'GET',
      params, signal
    },
  );
}


export const getGetProcessingStatusQueryKey = (params?: GetProcessingStatusParams,) => {
  return [`/processing_status`, ...(params ? [params] : [])] as const;
}


export const getGetProcessingStatusQueryOptions = <TData = Awaited<ReturnType<typeof getProcessingStatus>>, TError = HTTPValidationError>(params?: GetProcessingStatusParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProcessingStatus>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetProcessingStatusQueryKey(params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof getProcessingStatus>>> = ({ signal }) => getProcessingStatus(params, signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getProcessingStatus>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type GetProcessingStatusQueryResult = NonNullable<Awaited<ReturnType<typeof getProcessingStatus>>>
export type GetProcessingStatusQueryError = HTTPValidationError


export function useGetProcessingStatus<TData = Awaited<ReturnType<typeof getProcessingStatus>>, TError = HTTPValidationError>(
  params: undefined | GetProcessingStatusParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProcessingStatus>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getProcessingStatus>>,
        TError,
        Awaited<ReturnType<typeof getProcessingStatus>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetProcessingStatus<TData = Awaited<ReturnType<typeof getProcessingStatus>>, TError = HTTPValidationError>(
  params?: GetProcessingStatusParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProcessingStatus>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getProcessingStatus>>,
        TError,
        Awaited<ReturnType<typeof getProcessingStatus>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetProcessingStatus<TData = Awaited<ReturnType<typeof getProcessingStatus>>, TError = HTTPValidationError>(
  params?: GetProcessingStatusParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProcessingStatus>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Get Processing Status
 */

export function useGetProcessingStatus<TData = Awaited<ReturnType<typeof getProcessingStatus>>, TError = HTTPValidationError>(
  params?: GetProcessingStatusParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getProcessingStatus>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getGetProcessingStatusQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * Reset processing to no graph state (step 0)
 * @summary Reset Processing
 */
export const resetProcessing = (
  params?: ResetProcessingParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<ResetProcessing200>(
    {
      url: `/reset_processing`, method: 'POST',
      params, signal
    },
  );
}



export const getResetProcessingMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof resetProcessing>>, TError, { params?: ResetProcessingParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof resetProcessing>>, TError, { params?: ResetProcessingParams }, TContext> => {

  const mutationKey = ['resetProcessing'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof resetProcessing>>, { params?: ResetProcessingParams }> = (props) => {
    const { params } = props ?? {};

    return resetProcessing(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ResetProcessingMutationResult = NonNullable<Awaited<ReturnType<typeof resetProcessing>>>

export type ResetProcessingMutationError = HTTPValidationError

/**
* @summary Reset Processing
*/
export const useResetProcessing = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof resetProcessing>>, TError, { params?: ResetProcessingParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof resetProcessing>>,
      TError,
      { params?: ResetProcessingParams },
      TContext
    > => {

  const mutationOptions = getResetProcessingMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Create a new collection
* @summary Create Collection
*/
export const createCollection = (
  params: CreateCollectionParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<CreateCollection201>(
    {
      url: `/collections`, method: 'POST',
      params, signal
    },
  );
}



export const getCreateCollectionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof createCollection>>, TError, { params: CreateCollectionParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof createCollection>>, TError, { params: CreateCollectionParams }, TContext> => {

  const mutationKey = ['createCollection'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof createCollection>>, { params: CreateCollectionParams }> = (props) => {
    const { params } = props ?? {};

    return createCollection(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type CreateCollectionMutationResult = NonNullable<Awaited<ReturnType<typeof createCollection>>>

export type CreateCollectionMutationError = HTTPValidationError

/**
* @summary Create Collection
*/
export const useCreateCollection = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof createCollection>>, TError, { params: CreateCollectionParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof createCollection>>,
      TError,
      { params: CreateCollectionParams },
      TContext
    > => {

  const mutationOptions = getCreateCollectionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Delete a collection and all its graphs
* @summary Delete Collection
*/
export const deleteCollection = (
  name: string,
) => {


  return apiUrlMutator<DeleteCollection200>(
    {
      url: `/collections/${name}`, method: 'DELETE'
    },
  );
}



export const getDeleteCollectionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteCollection>>, TError, { name: string }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof deleteCollection>>, TError, { name: string }, TContext> => {

  const mutationKey = ['deleteCollection'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteCollection>>, { name: string }> = (props) => {
    const { name } = props ?? {};

    return deleteCollection(name,)
  }




  return { mutationFn, ...mutationOptions }
}

export type DeleteCollectionMutationResult = NonNullable<Awaited<ReturnType<typeof deleteCollection>>>

export type DeleteCollectionMutationError = HTTPValidationError

/**
* @summary Delete Collection
*/
export const useDeleteCollection = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteCollection>>, TError, { name: string }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof deleteCollection>>,
      TError,
      { name: string },
      TContext
    > => {

  const mutationOptions = getDeleteCollectionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Copy a collection's graph to a new collection
* @summary Copy Collection
*/
export const copyCollection = (
  params: CopyCollectionParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<CopyCollection200>(
    {
      url: `/copy_collection`, method: 'POST',
      params, signal
    },
  );
}



export const getCopyCollectionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof copyCollection>>, TError, { params: CopyCollectionParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof copyCollection>>, TError, { params: CopyCollectionParams }, TContext> => {

  const mutationKey = ['copyCollection'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof copyCollection>>, { params: CopyCollectionParams }> = (props) => {
    const { params } = props ?? {};

    return copyCollection(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type CopyCollectionMutationResult = NonNullable<Awaited<ReturnType<typeof copyCollection>>>

export type CopyCollectionMutationError = HTTPValidationError

/**
* @summary Copy Collection
*/
export const useCopyCollection = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof copyCollection>>, TError, { params: CopyCollectionParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof copyCollection>>,
      TError,
      { params: CopyCollectionParams },
      TContext
    > => {

  const mutationOptions = getCopyCollectionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
