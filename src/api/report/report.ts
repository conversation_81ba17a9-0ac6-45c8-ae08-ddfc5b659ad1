/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation
} from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryClient,
  UseMutationOptions,
  UseMutationResult
} from '@tanstack/react-query';

import type {
  AnalysePdfImagesReportAnalysePdfImagesPostParams,
  AnalysePdfImagesResponse,
  AnswerMultipleQuestionsAndGenerateReportResponse,
  BodyAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost,
  BodyFillPptxTemplateReportFillPptxTemplatePost,
  BodyReadTemplateReportReadTemplatePost,
  ExtractImagesFromPdfReportExtractImagesFromPdfPostParams,
  ExtractImagesFromPdfResponse,
  HTTPValidationError,
  ReadTemplateResponse
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * Parse .docx template file and return prompts for QA:
List lines starting with `Q:` or containing `{`
 * @summary Read Template
 */
export const readTemplateReportReadTemplatePost = (
  bodyReadTemplateReportReadTemplatePost: BodyReadTemplateReportReadTemplatePost,
  signal?: AbortSignal
) => {

  const formData = new FormData();
  formData.append(`file`, bodyReadTemplateReportReadTemplatePost.file)

  return apiUrlMutator<ReadTemplateResponse>(
    {
      url: `/report/read_template`, method: 'POST',
      headers: { 'Content-Type': 'multipart/form-data', },
      data: formData, signal
    },
  );
}



export const getReadTemplateReportReadTemplatePostMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof readTemplateReportReadTemplatePost>>, TError, { data: BodyReadTemplateReportReadTemplatePost }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof readTemplateReportReadTemplatePost>>, TError, { data: BodyReadTemplateReportReadTemplatePost }, TContext> => {

  const mutationKey = ['readTemplateReportReadTemplatePost'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof readTemplateReportReadTemplatePost>>, { data: BodyReadTemplateReportReadTemplatePost }> = (props) => {
    const { data } = props ?? {};

    return readTemplateReportReadTemplatePost(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ReadTemplateReportReadTemplatePostMutationResult = NonNullable<Awaited<ReturnType<typeof readTemplateReportReadTemplatePost>>>
export type ReadTemplateReportReadTemplatePostMutationBody = BodyReadTemplateReportReadTemplatePost
export type ReadTemplateReportReadTemplatePostMutationError = HTTPValidationError

/**
* @summary Read Template
*/
export const useReadTemplateReportReadTemplatePost = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof readTemplateReportReadTemplatePost>>, TError, { data: BodyReadTemplateReportReadTemplatePost }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof readTemplateReportReadTemplatePost>>,
      TError,
      { data: BodyReadTemplateReportReadTemplatePost },
      TContext
    > => {

  const mutationOptions = getReadTemplateReportReadTemplatePostMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Answer Multiple Questions
*/
export const answerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost = (
  bodyAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost: BodyAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost,
  signal?: AbortSignal
) => {

  const formData = new FormData();
  formData.append(`general_config`, JSON.stringify(bodyAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost.general_config));
  formData.append(`report_template`, bodyAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost.report_template)

  return apiUrlMutator<AnswerMultipleQuestionsAndGenerateReportResponse>(
    {
      url: `/report/answer_multiple_questions_and_generate_report`, method: 'POST',
      headers: { 'Content-Type': 'multipart/form-data', },
      data: formData, signal
    },
  );
}



export const getAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPostMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof answerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost>>, TError, { data: BodyAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof answerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost>>, TError, { data: BodyAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost }, TContext> => {

  const mutationKey = ['answerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof answerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost>>, { data: BodyAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost }> = (props) => {
    const { data } = props ?? {};

    return answerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPostMutationResult = NonNullable<Awaited<ReturnType<typeof answerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost>>>
export type AnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPostMutationBody = BodyAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost
export type AnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPostMutationError = HTTPValidationError

/**
* @summary Answer Multiple Questions
*/
export const useAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof answerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost>>, TError, { data: BodyAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof answerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost>>,
      TError,
      { data: BodyAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost },
      TContext
    > => {

  const mutationOptions = getAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPostMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Fill Pptx Template
*/
export const fillPptxTemplateReportFillPptxTemplatePost = (
  bodyFillPptxTemplateReportFillPptxTemplatePost: BodyFillPptxTemplateReportFillPptxTemplatePost,
  signal?: AbortSignal
) => {

  const formData = new FormData();
  formData.append(`general_config`, JSON.stringify(bodyFillPptxTemplateReportFillPptxTemplatePost.general_config));
  formData.append(`report_template`, bodyFillPptxTemplateReportFillPptxTemplatePost.report_template)

  return apiUrlMutator<AnswerMultipleQuestionsAndGenerateReportResponse>(
    {
      url: `/report/fill_pptx_template`, method: 'POST',
      headers: { 'Content-Type': 'multipart/form-data', },
      data: formData, signal
    },
  );
}



export const getFillPptxTemplateReportFillPptxTemplatePostMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof fillPptxTemplateReportFillPptxTemplatePost>>, TError, { data: BodyFillPptxTemplateReportFillPptxTemplatePost }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof fillPptxTemplateReportFillPptxTemplatePost>>, TError, { data: BodyFillPptxTemplateReportFillPptxTemplatePost }, TContext> => {

  const mutationKey = ['fillPptxTemplateReportFillPptxTemplatePost'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof fillPptxTemplateReportFillPptxTemplatePost>>, { data: BodyFillPptxTemplateReportFillPptxTemplatePost }> = (props) => {
    const { data } = props ?? {};

    return fillPptxTemplateReportFillPptxTemplatePost(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type FillPptxTemplateReportFillPptxTemplatePostMutationResult = NonNullable<Awaited<ReturnType<typeof fillPptxTemplateReportFillPptxTemplatePost>>>
export type FillPptxTemplateReportFillPptxTemplatePostMutationBody = BodyFillPptxTemplateReportFillPptxTemplatePost
export type FillPptxTemplateReportFillPptxTemplatePostMutationError = HTTPValidationError

/**
* @summary Fill Pptx Template
*/
export const useFillPptxTemplateReportFillPptxTemplatePost = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof fillPptxTemplateReportFillPptxTemplatePost>>, TError, { data: BodyFillPptxTemplateReportFillPptxTemplatePost }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof fillPptxTemplateReportFillPptxTemplatePost>>,
      TError,
      { data: BodyFillPptxTemplateReportFillPptxTemplatePost },
      TContext
    > => {

  const mutationOptions = getFillPptxTemplateReportFillPptxTemplatePostMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Analyse Pdf Images
*/
export const analysePdfImagesReportAnalysePdfImagesPost = (
  params: AnalysePdfImagesReportAnalysePdfImagesPostParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<AnalysePdfImagesResponse>(
    {
      url: `/report/analyse_pdf_images`, method: 'POST',
      params, signal
    },
  );
}



export const getAnalysePdfImagesReportAnalysePdfImagesPostMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof analysePdfImagesReportAnalysePdfImagesPost>>, TError, { params: AnalysePdfImagesReportAnalysePdfImagesPostParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof analysePdfImagesReportAnalysePdfImagesPost>>, TError, { params: AnalysePdfImagesReportAnalysePdfImagesPostParams }, TContext> => {

  const mutationKey = ['analysePdfImagesReportAnalysePdfImagesPost'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof analysePdfImagesReportAnalysePdfImagesPost>>, { params: AnalysePdfImagesReportAnalysePdfImagesPostParams }> = (props) => {
    const { params } = props ?? {};

    return analysePdfImagesReportAnalysePdfImagesPost(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AnalysePdfImagesReportAnalysePdfImagesPostMutationResult = NonNullable<Awaited<ReturnType<typeof analysePdfImagesReportAnalysePdfImagesPost>>>

export type AnalysePdfImagesReportAnalysePdfImagesPostMutationError = HTTPValidationError

/**
* @summary Analyse Pdf Images
*/
export const useAnalysePdfImagesReportAnalysePdfImagesPost = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof analysePdfImagesReportAnalysePdfImagesPost>>, TError, { params: AnalysePdfImagesReportAnalysePdfImagesPostParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof analysePdfImagesReportAnalysePdfImagesPost>>,
      TError,
      { params: AnalysePdfImagesReportAnalysePdfImagesPostParams },
      TContext
    > => {

  const mutationOptions = getAnalysePdfImagesReportAnalysePdfImagesPostMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Extract Images From Pdf
*/
export const extractImagesFromPdfReportExtractImagesFromPdfPost = (
  params: ExtractImagesFromPdfReportExtractImagesFromPdfPostParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractImagesFromPdfResponse>(
    {
      url: `/report/extract_images_from_pdf`, method: 'POST',
      params, signal
    },
  );
}



export const getExtractImagesFromPdfReportExtractImagesFromPdfPostMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractImagesFromPdfReportExtractImagesFromPdfPost>>, TError, { params: ExtractImagesFromPdfReportExtractImagesFromPdfPostParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractImagesFromPdfReportExtractImagesFromPdfPost>>, TError, { params: ExtractImagesFromPdfReportExtractImagesFromPdfPostParams }, TContext> => {

  const mutationKey = ['extractImagesFromPdfReportExtractImagesFromPdfPost'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractImagesFromPdfReportExtractImagesFromPdfPost>>, { params: ExtractImagesFromPdfReportExtractImagesFromPdfPostParams }> = (props) => {
    const { params } = props ?? {};

    return extractImagesFromPdfReportExtractImagesFromPdfPost(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractImagesFromPdfReportExtractImagesFromPdfPostMutationResult = NonNullable<Awaited<ReturnType<typeof extractImagesFromPdfReportExtractImagesFromPdfPost>>>

export type ExtractImagesFromPdfReportExtractImagesFromPdfPostMutationError = HTTPValidationError

/**
* @summary Extract Images From Pdf
*/
export const useExtractImagesFromPdfReportExtractImagesFromPdfPost = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractImagesFromPdfReportExtractImagesFromPdfPost>>, TError, { params: ExtractImagesFromPdfReportExtractImagesFromPdfPostParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractImagesFromPdfReportExtractImagesFromPdfPost>>,
      TError,
      { params: ExtractImagesFromPdfReportExtractImagesFromPdfPostParams },
      TContext
    > => {

  const mutationOptions = getExtractImagesFromPdfReportExtractImagesFromPdfPostMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Generate Simple Pptx
*/
export const generateSimplePptxReportGenerateSimplePptxPost = (
  generateSimplePptxReportGenerateSimplePptxPostBody: string,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/report/generate_simple_pptx`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: generateSimplePptxReportGenerateSimplePptxPostBody, signal
    },
  );
}



export const getGenerateSimplePptxReportGenerateSimplePptxPostMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof generateSimplePptxReportGenerateSimplePptxPost>>, TError, { data: string }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof generateSimplePptxReportGenerateSimplePptxPost>>, TError, { data: string }, TContext> => {

  const mutationKey = ['generateSimplePptxReportGenerateSimplePptxPost'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof generateSimplePptxReportGenerateSimplePptxPost>>, { data: string }> = (props) => {
    const { data } = props ?? {};

    return generateSimplePptxReportGenerateSimplePptxPost(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type GenerateSimplePptxReportGenerateSimplePptxPostMutationResult = NonNullable<Awaited<ReturnType<typeof generateSimplePptxReportGenerateSimplePptxPost>>>
export type GenerateSimplePptxReportGenerateSimplePptxPostMutationBody = string
export type GenerateSimplePptxReportGenerateSimplePptxPostMutationError = HTTPValidationError

/**
* @summary Generate Simple Pptx
*/
export const useGenerateSimplePptxReportGenerateSimplePptxPost = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof generateSimplePptxReportGenerateSimplePptxPost>>, TError, { data: string }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof generateSimplePptxReportGenerateSimplePptxPost>>,
      TError,
      { data: string },
      TContext
    > => {

  const mutationOptions = getGenerateSimplePptxReportGenerateSimplePptxPostMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Generate Simple Markdown
*/
export const generateSimpleMarkdownReportGenerateSimpleMarkdownPost = (
  generateSimpleMarkdownReportGenerateSimpleMarkdownPostBody: string,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/report/generate_simple_markdown`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: generateSimpleMarkdownReportGenerateSimpleMarkdownPostBody, signal
    },
  );
}



export const getGenerateSimpleMarkdownReportGenerateSimpleMarkdownPostMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof generateSimpleMarkdownReportGenerateSimpleMarkdownPost>>, TError, { data: string }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof generateSimpleMarkdownReportGenerateSimpleMarkdownPost>>, TError, { data: string }, TContext> => {

  const mutationKey = ['generateSimpleMarkdownReportGenerateSimpleMarkdownPost'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof generateSimpleMarkdownReportGenerateSimpleMarkdownPost>>, { data: string }> = (props) => {
    const { data } = props ?? {};

    return generateSimpleMarkdownReportGenerateSimpleMarkdownPost(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type GenerateSimpleMarkdownReportGenerateSimpleMarkdownPostMutationResult = NonNullable<Awaited<ReturnType<typeof generateSimpleMarkdownReportGenerateSimpleMarkdownPost>>>
export type GenerateSimpleMarkdownReportGenerateSimpleMarkdownPostMutationBody = string
export type GenerateSimpleMarkdownReportGenerateSimpleMarkdownPostMutationError = HTTPValidationError

/**
* @summary Generate Simple Markdown
*/
export const useGenerateSimpleMarkdownReportGenerateSimpleMarkdownPost = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof generateSimpleMarkdownReportGenerateSimpleMarkdownPost>>, TError, { data: string }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof generateSimpleMarkdownReportGenerateSimpleMarkdownPost>>,
      TError,
      { data: string },
      TContext
    > => {

  const mutationOptions = getGenerateSimpleMarkdownReportGenerateSimpleMarkdownPostMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
