/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation
} from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryClient,
  UseMutationOptions,
  UseMutationResult
} from '@tanstack/react-query';

import type {
  BodySummarizeCollection,
  BodySummarizeDocument,
  BodySummarizeText,
  BodySummarizeTextList,
  BodySummarizeTopic,
  HTTPValidationError,
  SummarizeCollectionParams
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * @summary Summarize Document
 */
export const summarizeDocument = (
  bodySummarizeDocument: BodySummarizeDocument,
  signal?: AbortSignal
) => {

  const formUrlEncoded = new URLSearchParams();
  formUrlEncoded.append(`document_id`, bodySummarizeDocument.document_id)
  if (bodySummarizeDocument.override_summary !== undefined) {
    formUrlEncoded.append(`override_summary`, bodySummarizeDocument.override_summary.toString())
  }
  if (bodySummarizeDocument.llm !== undefined) {
    formUrlEncoded.append(`llm`, bodySummarizeDocument.llm)
  }
  if (bodySummarizeDocument.include_intermediate_steps !== undefined) {
    formUrlEncoded.append(`include_intermediate_steps`, bodySummarizeDocument.include_intermediate_steps.toString())
  }

  return apiUrlMutator<unknown>(
    {
      url: `/summarize/document`, method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded', },
      data: formUrlEncoded, signal
    },
  );
}



export const getSummarizeDocumentMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof summarizeDocument>>, TError, { data: BodySummarizeDocument }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof summarizeDocument>>, TError, { data: BodySummarizeDocument }, TContext> => {

  const mutationKey = ['summarizeDocument'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof summarizeDocument>>, { data: BodySummarizeDocument }> = (props) => {
    const { data } = props ?? {};

    return summarizeDocument(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type SummarizeDocumentMutationResult = NonNullable<Awaited<ReturnType<typeof summarizeDocument>>>
export type SummarizeDocumentMutationBody = BodySummarizeDocument
export type SummarizeDocumentMutationError = HTTPValidationError

/**
* @summary Summarize Document
*/
export const useSummarizeDocument = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof summarizeDocument>>, TError, { data: BodySummarizeDocument }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof summarizeDocument>>,
      TError,
      { data: BodySummarizeDocument },
      TContext
    > => {

  const mutationOptions = getSummarizeDocumentMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Summarize Topic
*/
export const summarizeTopic = (
  bodySummarizeTopic: BodySummarizeTopic,
  signal?: AbortSignal
) => {

  const formUrlEncoded = new URLSearchParams();
  formUrlEncoded.append(`query`, bodySummarizeTopic.query)
  formUrlEncoded.append(`collection_id`, bodySummarizeTopic.collection_id)
  formUrlEncoded.append(`top_k`, bodySummarizeTopic.top_k.toString())
  if (bodySummarizeTopic.llm !== undefined) {
    formUrlEncoded.append(`llm`, bodySummarizeTopic.llm)
  }
  if (bodySummarizeTopic.similarity_thr !== undefined) {
    formUrlEncoded.append(`similarity_thr`, bodySummarizeTopic.similarity_thr.toString())
  }
  if (bodySummarizeTopic.include_intermediate_steps !== undefined) {
    formUrlEncoded.append(`include_intermediate_steps`, bodySummarizeTopic.include_intermediate_steps.toString())
  }

  return apiUrlMutator<unknown>(
    {
      url: `/summarize/topic`, method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded', },
      data: formUrlEncoded, signal
    },
  );
}



export const getSummarizeTopicMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof summarizeTopic>>, TError, { data: BodySummarizeTopic }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof summarizeTopic>>, TError, { data: BodySummarizeTopic }, TContext> => {

  const mutationKey = ['summarizeTopic'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof summarizeTopic>>, { data: BodySummarizeTopic }> = (props) => {
    const { data } = props ?? {};

    return summarizeTopic(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type SummarizeTopicMutationResult = NonNullable<Awaited<ReturnType<typeof summarizeTopic>>>
export type SummarizeTopicMutationBody = BodySummarizeTopic
export type SummarizeTopicMutationError = HTTPValidationError

/**
* @summary Summarize Topic
*/
export const useSummarizeTopic = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof summarizeTopic>>, TError, { data: BodySummarizeTopic }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof summarizeTopic>>,
      TError,
      { data: BodySummarizeTopic },
      TContext
    > => {

  const mutationOptions = getSummarizeTopicMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Summarize Text
*/
export const summarizeText = (
  bodySummarizeText: BodySummarizeText,
  signal?: AbortSignal
) => {

  const formUrlEncoded = new URLSearchParams();
  formUrlEncoded.append(`text`, bodySummarizeText.text)
  if (bodySummarizeText.llm !== undefined) {
    formUrlEncoded.append(`llm`, bodySummarizeText.llm)
  }
  if (bodySummarizeText.include_intermediate_steps !== undefined) {
    formUrlEncoded.append(`include_intermediate_steps`, bodySummarizeText.include_intermediate_steps.toString())
  }

  return apiUrlMutator<unknown>(
    {
      url: `/summarize/text`, method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded', },
      data: formUrlEncoded, signal
    },
  );
}



export const getSummarizeTextMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof summarizeText>>, TError, { data: BodySummarizeText }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof summarizeText>>, TError, { data: BodySummarizeText }, TContext> => {

  const mutationKey = ['summarizeText'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof summarizeText>>, { data: BodySummarizeText }> = (props) => {
    const { data } = props ?? {};

    return summarizeText(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type SummarizeTextMutationResult = NonNullable<Awaited<ReturnType<typeof summarizeText>>>
export type SummarizeTextMutationBody = BodySummarizeText
export type SummarizeTextMutationError = HTTPValidationError

/**
* @summary Summarize Text
*/
export const useSummarizeText = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof summarizeText>>, TError, { data: BodySummarizeText }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof summarizeText>>,
      TError,
      { data: BodySummarizeText },
      TContext
    > => {

  const mutationOptions = getSummarizeTextMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Summarize
*/
export const summarizeTextList = (
  bodySummarizeTextList: BodySummarizeTextList,
  signal?: AbortSignal
) => {

  const formUrlEncoded = new URLSearchParams();
  bodySummarizeTextList.text_list.forEach(value => formUrlEncoded.append(`text_list`, value));
  if (bodySummarizeTextList.llm !== undefined) {
    formUrlEncoded.append(`llm`, bodySummarizeTextList.llm)
  }
  if (bodySummarizeTextList.include_intermediate_steps !== undefined) {
    formUrlEncoded.append(`include_intermediate_steps`, bodySummarizeTextList.include_intermediate_steps.toString())
  }

  return apiUrlMutator<unknown>(
    {
      url: `/summarize/text_list`, method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded', },
      data: formUrlEncoded, signal
    },
  );
}



export const getSummarizeTextListMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof summarizeTextList>>, TError, { data: BodySummarizeTextList }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof summarizeTextList>>, TError, { data: BodySummarizeTextList }, TContext> => {

  const mutationKey = ['summarizeTextList'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof summarizeTextList>>, { data: BodySummarizeTextList }> = (props) => {
    const { data } = props ?? {};

    return summarizeTextList(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type SummarizeTextListMutationResult = NonNullable<Awaited<ReturnType<typeof summarizeTextList>>>
export type SummarizeTextListMutationBody = BodySummarizeTextList
export type SummarizeTextListMutationError = HTTPValidationError

/**
* @summary Summarize
*/
export const useSummarizeTextList = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof summarizeTextList>>, TError, { data: BodySummarizeTextList }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof summarizeTextList>>,
      TError,
      { data: BodySummarizeTextList },
      TContext
    > => {

  const mutationOptions = getSummarizeTextListMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Summarize Collection
*/
export const summarizeCollection = (
  bodySummarizeCollection: BodySummarizeCollection,
  params: SummarizeCollectionParams,
  signal?: AbortSignal
) => {

  const formUrlEncoded = new URLSearchParams();
  formUrlEncoded.append(`structure_depth`, bodySummarizeCollection.structure_depth.toString())
  if (bodySummarizeCollection.llm !== undefined) {
    formUrlEncoded.append(`llm`, bodySummarizeCollection.llm)
  }
  if (bodySummarizeCollection.override_summary !== undefined) {
    formUrlEncoded.append(`override_summary`, bodySummarizeCollection.override_summary.toString())
  }

  return apiUrlMutator<unknown>(
    {
      url: `/summarize/summarize_collection`, method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded', },
      data: formUrlEncoded,
      params, signal
    },
  );
}



export const getSummarizeCollectionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof summarizeCollection>>, TError, { data: BodySummarizeCollection; params: SummarizeCollectionParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof summarizeCollection>>, TError, { data: BodySummarizeCollection; params: SummarizeCollectionParams }, TContext> => {

  const mutationKey = ['summarizeCollection'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof summarizeCollection>>, { data: BodySummarizeCollection; params: SummarizeCollectionParams }> = (props) => {
    const { data, params } = props ?? {};

    return summarizeCollection(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type SummarizeCollectionMutationResult = NonNullable<Awaited<ReturnType<typeof summarizeCollection>>>
export type SummarizeCollectionMutationBody = BodySummarizeCollection
export type SummarizeCollectionMutationError = HTTPValidationError

/**
* @summary Summarize Collection
*/
export const useSummarizeCollection = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof summarizeCollection>>, TError, { data: BodySummarizeCollection; params: SummarizeCollectionParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof summarizeCollection>>,
      TError,
      { data: BodySummarizeCollection; params: SummarizeCollectionParams },
      TContext
    > => {

  const mutationOptions = getSummarizeCollectionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
