/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation,
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import type {
  AskQuestionAsync200,
  AskQuestionAsyncOld200,
  AskQuestionAsyncOldParams,
  AskQuestionAsyncParams,
  AskQuestionOldParams,
  AskQuestionParams,
  BodyAnalyseImage,
  BodyAskDocuments,
  BodyAskQuestion,
  BodyAskQuestionAsync,
  BodyAskQuestionAsyncOld,
  BodyAskQuestionOld,
  BodyPromptLLM,
  BodyPromptLLMAsync,
  BodyQueryTable,
  GenerateQuestionsAboutSourceBody,
  GenerateQuestionsAboutSourceParams,
  HTTPValidationError,
  PromptLLM200,
  PromptLLMAsync200,
  QueryDatabaseParams,
  QueryTableParams,
  QueryTableResponse
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * @summary Ask
 */
export const askQuestion = (
  bodyAskQuestion: BodyAskQuestion,
  params: AskQuestionParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/qa/ask`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyAskQuestion,
      params, signal
    },
  );
}



export const getAskQuestionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof askQuestion>>, TError, { data: BodyAskQuestion; params: AskQuestionParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof askQuestion>>, TError, { data: BodyAskQuestion; params: AskQuestionParams }, TContext> => {

  const mutationKey = ['askQuestion'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof askQuestion>>, { data: BodyAskQuestion; params: AskQuestionParams }> = (props) => {
    const { data, params } = props ?? {};

    return askQuestion(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AskQuestionMutationResult = NonNullable<Awaited<ReturnType<typeof askQuestion>>>
export type AskQuestionMutationBody = BodyAskQuestion
export type AskQuestionMutationError = HTTPValidationError

/**
* @summary Ask
*/
export const useAskQuestion = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof askQuestion>>, TError, { data: BodyAskQuestion; params: AskQuestionParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof askQuestion>>,
      TError,
      { data: BodyAskQuestion; params: AskQuestionParams },
      TContext
    > => {

  const mutationOptions = getAskQuestionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Ask Old
*/
export const askQuestionOld = (
  bodyAskQuestionOld: BodyAskQuestionOld,
  params: AskQuestionOldParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/qa/ask_old`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyAskQuestionOld,
      params, signal
    },
  );
}



export const getAskQuestionOldMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof askQuestionOld>>, TError, { data: BodyAskQuestionOld; params: AskQuestionOldParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof askQuestionOld>>, TError, { data: BodyAskQuestionOld; params: AskQuestionOldParams }, TContext> => {

  const mutationKey = ['askQuestionOld'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof askQuestionOld>>, { data: BodyAskQuestionOld; params: AskQuestionOldParams }> = (props) => {
    const { data, params } = props ?? {};

    return askQuestionOld(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AskQuestionOldMutationResult = NonNullable<Awaited<ReturnType<typeof askQuestionOld>>>
export type AskQuestionOldMutationBody = BodyAskQuestionOld
export type AskQuestionOldMutationError = HTTPValidationError

/**
* @summary Ask Old
*/
export const useAskQuestionOld = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof askQuestionOld>>, TError, { data: BodyAskQuestionOld; params: AskQuestionOldParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof askQuestionOld>>,
      TError,
      { data: BodyAskQuestionOld; params: AskQuestionOldParams },
      TContext
    > => {

  const mutationOptions = getAskQuestionOldMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Ask Documents
*/
export const askDocuments = (
  bodyAskDocuments: BodyAskDocuments,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/qa/ask_documents`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyAskDocuments, signal
    },
  );
}



export const getAskDocumentsMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof askDocuments>>, TError, { data: BodyAskDocuments }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof askDocuments>>, TError, { data: BodyAskDocuments }, TContext> => {

  const mutationKey = ['askDocuments'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof askDocuments>>, { data: BodyAskDocuments }> = (props) => {
    const { data } = props ?? {};

    return askDocuments(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AskDocumentsMutationResult = NonNullable<Awaited<ReturnType<typeof askDocuments>>>
export type AskDocumentsMutationBody = BodyAskDocuments
export type AskDocumentsMutationError = HTTPValidationError

/**
* @summary Ask Documents
*/
export const useAskDocuments = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof askDocuments>>, TError, { data: BodyAskDocuments }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof askDocuments>>,
      TError,
      { data: BodyAskDocuments },
      TContext
    > => {

  const mutationOptions = getAskDocumentsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Ask Async
*/
export const askQuestionAsync = (
  bodyAskQuestionAsync: BodyAskQuestionAsync,
  params: AskQuestionAsyncParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<AskQuestionAsync200>(
    {
      url: `/qa/ask_async`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyAskQuestionAsync,
      params, signal
    },
  );
}



export const getAskQuestionAsyncMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof askQuestionAsync>>, TError, { data: BodyAskQuestionAsync; params: AskQuestionAsyncParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof askQuestionAsync>>, TError, { data: BodyAskQuestionAsync; params: AskQuestionAsyncParams }, TContext> => {

  const mutationKey = ['askQuestionAsync'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof askQuestionAsync>>, { data: BodyAskQuestionAsync; params: AskQuestionAsyncParams }> = (props) => {
    const { data, params } = props ?? {};

    return askQuestionAsync(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AskQuestionAsyncMutationResult = NonNullable<Awaited<ReturnType<typeof askQuestionAsync>>>
export type AskQuestionAsyncMutationBody = BodyAskQuestionAsync
export type AskQuestionAsyncMutationError = HTTPValidationError

/**
* @summary Ask Async
*/
export const useAskQuestionAsync = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof askQuestionAsync>>, TError, { data: BodyAskQuestionAsync; params: AskQuestionAsyncParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof askQuestionAsync>>,
      TError,
      { data: BodyAskQuestionAsync; params: AskQuestionAsyncParams },
      TContext
    > => {

  const mutationOptions = getAskQuestionAsyncMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Ask Async Old
*/
export const askQuestionAsyncOld = (
  bodyAskQuestionAsyncOld: BodyAskQuestionAsyncOld,
  params: AskQuestionAsyncOldParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<AskQuestionAsyncOld200>(
    {
      url: `/qa/ask_async_old`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyAskQuestionAsyncOld,
      params, signal
    },
  );
}



export const getAskQuestionAsyncOldMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof askQuestionAsyncOld>>, TError, { data: BodyAskQuestionAsyncOld; params: AskQuestionAsyncOldParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof askQuestionAsyncOld>>, TError, { data: BodyAskQuestionAsyncOld; params: AskQuestionAsyncOldParams }, TContext> => {

  const mutationKey = ['askQuestionAsyncOld'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof askQuestionAsyncOld>>, { data: BodyAskQuestionAsyncOld; params: AskQuestionAsyncOldParams }> = (props) => {
    const { data, params } = props ?? {};

    return askQuestionAsyncOld(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AskQuestionAsyncOldMutationResult = NonNullable<Awaited<ReturnType<typeof askQuestionAsyncOld>>>
export type AskQuestionAsyncOldMutationBody = BodyAskQuestionAsyncOld
export type AskQuestionAsyncOldMutationError = HTTPValidationError

/**
* @summary Ask Async Old
*/
export const useAskQuestionAsyncOld = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof askQuestionAsyncOld>>, TError, { data: BodyAskQuestionAsyncOld; params: AskQuestionAsyncOldParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof askQuestionAsyncOld>>,
      TError,
      { data: BodyAskQuestionAsyncOld; params: AskQuestionAsyncOldParams },
      TContext
    > => {

  const mutationOptions = getAskQuestionAsyncOldMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Query Table
*/
export const queryTable = (
  bodyQueryTable: BodyQueryTable,
  params: QueryTableParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<QueryTableResponse>(
    {
      url: `/qa/query_table`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyQueryTable,
      params, signal
    },
  );
}



export const getQueryTableMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof queryTable>>, TError, { data: BodyQueryTable; params: QueryTableParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof queryTable>>, TError, { data: BodyQueryTable; params: QueryTableParams }, TContext> => {

  const mutationKey = ['queryTable'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof queryTable>>, { data: BodyQueryTable; params: QueryTableParams }> = (props) => {
    const { data, params } = props ?? {};

    return queryTable(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type QueryTableMutationResult = NonNullable<Awaited<ReturnType<typeof queryTable>>>
export type QueryTableMutationBody = BodyQueryTable
export type QueryTableMutationError = HTTPValidationError

/**
* @summary Query Table
*/
export const useQueryTable = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof queryTable>>, TError, { data: BodyQueryTable; params: QueryTableParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof queryTable>>,
      TError,
      { data: BodyQueryTable; params: QueryTableParams },
      TContext
    > => {

  const mutationOptions = getQueryTableMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Analyse Image
*/
export const analyseImage = (
  bodyAnalyseImage: BodyAnalyseImage,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/qa/analyse_image`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyAnalyseImage, signal
    },
  );
}



export const getAnalyseImageMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof analyseImage>>, TError, { data: BodyAnalyseImage }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof analyseImage>>, TError, { data: BodyAnalyseImage }, TContext> => {

  const mutationKey = ['analyseImage'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof analyseImage>>, { data: BodyAnalyseImage }> = (props) => {
    const { data } = props ?? {};

    return analyseImage(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AnalyseImageMutationResult = NonNullable<Awaited<ReturnType<typeof analyseImage>>>
export type AnalyseImageMutationBody = BodyAnalyseImage
export type AnalyseImageMutationError = HTTPValidationError

/**
* @summary Analyse Image
*/
export const useAnalyseImage = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof analyseImage>>, TError, { data: BodyAnalyseImage }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof analyseImage>>,
      TError,
      { data: BodyAnalyseImage },
      TContext
    > => {

  const mutationOptions = getAnalyseImageMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Prompt an llm, when `collection_entries` is present then it uses it as a context.

`collection_entries` parameter is useful when we have a list of collection entries that are
not a direct result of semantic search, which make the standard `/ask` endpoint not possible
to use.

:param prompt: A message to llm.
:param collection_entires: A list of collection entries which serve as a context to llm.
:param llm_config: An llm configuration.
:return: A dictionary representing the result of llm query in the form `answer: llm_output`.
* @summary Prompt Llm
*/
export const promptLLM = (
  bodyPromptLLM: BodyPromptLLM,
  signal?: AbortSignal
) => {


  return apiUrlMutator<PromptLLM200>(
    {
      url: `/qa/prompt_llm`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyPromptLLM, signal
    },
  );
}



export const getPromptLLMMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof promptLLM>>, TError, { data: BodyPromptLLM }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof promptLLM>>, TError, { data: BodyPromptLLM }, TContext> => {

  const mutationKey = ['promptLLM'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof promptLLM>>, { data: BodyPromptLLM }> = (props) => {
    const { data } = props ?? {};

    return promptLLM(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type PromptLLMMutationResult = NonNullable<Awaited<ReturnType<typeof promptLLM>>>
export type PromptLLMMutationBody = BodyPromptLLM
export type PromptLLMMutationError = HTTPValidationError

/**
* @summary Prompt Llm
*/
export const usePromptLLM = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof promptLLM>>, TError, { data: BodyPromptLLM }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof promptLLM>>,
      TError,
      { data: BodyPromptLLM },
      TContext
    > => {

  const mutationOptions = getPromptLLMMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Prompt an llm, when `collection_entries` is present then it uses it as a context.

`collection_entries` parameter is useful when we have a list of collection entries that are
not a direct result of semantic search, which make the standard `/ask` endpoint not possible
to use.

:param prompt: A message to llm.
:param collection_entires: A list of collection entries which serve as a context to llm.
:param llm_config: An llm configuration.
:return: A dictionary representing the result of llm query in the form `answer: llm_output`.
* @summary Prompt Llm Async
*/
export const promptLLMAsync = (
  bodyPromptLLMAsync: BodyPromptLLMAsync,
  signal?: AbortSignal
) => {


  return apiUrlMutator<PromptLLMAsync200>(
    {
      url: `/qa/prompt_llm_async`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyPromptLLMAsync, signal
    },
  );
}



export const getPromptLLMAsyncMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof promptLLMAsync>>, TError, { data: BodyPromptLLMAsync }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof promptLLMAsync>>, TError, { data: BodyPromptLLMAsync }, TContext> => {

  const mutationKey = ['promptLLMAsync'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof promptLLMAsync>>, { data: BodyPromptLLMAsync }> = (props) => {
    const { data } = props ?? {};

    return promptLLMAsync(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type PromptLLMAsyncMutationResult = NonNullable<Awaited<ReturnType<typeof promptLLMAsync>>>
export type PromptLLMAsyncMutationBody = BodyPromptLLMAsync
export type PromptLLMAsyncMutationError = HTTPValidationError

/**
* @summary Prompt Llm Async
*/
export const usePromptLLMAsync = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof promptLLMAsync>>, TError, { data: BodyPromptLLMAsync }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof promptLLMAsync>>,
      TError,
      { data: BodyPromptLLMAsync },
      TContext
    > => {

  const mutationOptions = getPromptLLMAsyncMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Query Database
*/
export const queryDatabase = (
  params: QueryDatabaseParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/qa/query_database`, method: 'POST',
      params, signal
    },
  );
}



export const getQueryDatabaseMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof queryDatabase>>, TError, { params: QueryDatabaseParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof queryDatabase>>, TError, { params: QueryDatabaseParams }, TContext> => {

  const mutationKey = ['queryDatabase'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof queryDatabase>>, { params: QueryDatabaseParams }> = (props) => {
    const { params } = props ?? {};

    return queryDatabase(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type QueryDatabaseMutationResult = NonNullable<Awaited<ReturnType<typeof queryDatabase>>>

export type QueryDatabaseMutationError = HTTPValidationError

/**
* @summary Query Database
*/
export const useQueryDatabase = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof queryDatabase>>, TError, { params: QueryDatabaseParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof queryDatabase>>,
      TError,
      { params: QueryDatabaseParams },
      TContext
    > => {

  const mutationOptions = getQueryDatabaseMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Get Database Tables
*/
export const getDatabaseTables = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/qa/get_database_tables`, method: 'GET', signal
    },
  );
}


export const getGetDatabaseTablesQueryKey = () => {
  return [`/qa/get_database_tables`] as const;
}


export const getGetDatabaseTablesQueryOptions = <TData = Awaited<ReturnType<typeof getDatabaseTables>>, TError = unknown>(options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDatabaseTables>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetDatabaseTablesQueryKey();



  const queryFn: QueryFunction<Awaited<ReturnType<typeof getDatabaseTables>>> = ({ signal }) => getDatabaseTables(signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getDatabaseTables>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type GetDatabaseTablesQueryResult = NonNullable<Awaited<ReturnType<typeof getDatabaseTables>>>
export type GetDatabaseTablesQueryError = unknown


export function useGetDatabaseTables<TData = Awaited<ReturnType<typeof getDatabaseTables>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDatabaseTables>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getDatabaseTables>>,
        TError,
        Awaited<ReturnType<typeof getDatabaseTables>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetDatabaseTables<TData = Awaited<ReturnType<typeof getDatabaseTables>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDatabaseTables>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getDatabaseTables>>,
        TError,
        Awaited<ReturnType<typeof getDatabaseTables>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetDatabaseTables<TData = Awaited<ReturnType<typeof getDatabaseTables>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDatabaseTables>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Get Database Tables
 */

export function useGetDatabaseTables<TData = Awaited<ReturnType<typeof getDatabaseTables>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDatabaseTables>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getGetDatabaseTablesQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * Generate questions and answers based on the provided sources, either a single document or a whole collection.
Returns a dict of {Q: A}
 * @summary Generate Questions About Source
 */
export const generateQuestionsAboutSource = (
  generateQuestionsAboutSourceBody: GenerateQuestionsAboutSourceBody,
  params?: GenerateQuestionsAboutSourceParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/qa/generate_questions_about_source`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: generateQuestionsAboutSourceBody,
      params, signal
    },
  );
}



export const getGenerateQuestionsAboutSourceMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof generateQuestionsAboutSource>>, TError, { data: GenerateQuestionsAboutSourceBody; params?: GenerateQuestionsAboutSourceParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof generateQuestionsAboutSource>>, TError, { data: GenerateQuestionsAboutSourceBody; params?: GenerateQuestionsAboutSourceParams }, TContext> => {

  const mutationKey = ['generateQuestionsAboutSource'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof generateQuestionsAboutSource>>, { data: GenerateQuestionsAboutSourceBody; params?: GenerateQuestionsAboutSourceParams }> = (props) => {
    const { data, params } = props ?? {};

    return generateQuestionsAboutSource(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type GenerateQuestionsAboutSourceMutationResult = NonNullable<Awaited<ReturnType<typeof generateQuestionsAboutSource>>>
export type GenerateQuestionsAboutSourceMutationBody = GenerateQuestionsAboutSourceBody
export type GenerateQuestionsAboutSourceMutationError = HTTPValidationError

/**
* @summary Generate Questions About Source
*/
export const useGenerateQuestionsAboutSource = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof generateQuestionsAboutSource>>, TError, { data: GenerateQuestionsAboutSourceBody; params?: GenerateQuestionsAboutSourceParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof generateQuestionsAboutSource>>,
      TError,
      { data: GenerateQuestionsAboutSourceBody; params?: GenerateQuestionsAboutSourceParams },
      TContext
    > => {

  const mutationOptions = getGenerateQuestionsAboutSourceMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
