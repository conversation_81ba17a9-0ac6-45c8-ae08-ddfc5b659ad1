/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
/**
 * Date and time of the creation of the action entry
 */
export type ActionModelCreatedAt = string | null;

/**
 * Type of functionality that is logged
 */
export type ActionModelMethodName = string | null;

export type ActionModelMethodInputAnyOf = { [key: string]: unknown };

/**
 * Input of an action that will allow to make it reproducible
 */
export type ActionModelMethodInput = ActionModelMethodInputAnyOf | null;

export type ActionModelSetupSettingsAnyOf = { [key: string]: unknown };

/**
 * Settings that were used to take action e.g. some configs
 */
export type ActionModelSetupSettings = ActionModelSetupSettingsAnyOf | null;

export type ActionModelMethodOutputAnyOf = { [key: string]: unknown };

/**
 * Output of an action
 */
export type ActionModelMethodOutput = ActionModelMethodOutputAnyOf | null;

/**
 * Duration of a method call
 */
export type ActionModelMethodDuration = number | null;

/**
 * Model of an Action
 */
export interface ActionModel {
  /** Database entry id */
  id?: string;
  /** Date and time of the creation of the action entry */
  created_at?: ActionModelCreatedAt;
  /** Date and time of the last update of the action entry */
  updated_at?: string;
  /** Type of functionality that is logged */
  method_name?: ActionModelMethodName;
  /** Input of an action that will allow to make it reproducible */
  method_input?: ActionModelMethodInput;
  /** Settings that were used to take action e.g. some configs */
  setup_settings?: ActionModelSetupSettings;
  /** Output of an action */
  method_output?: ActionModelMethodOutput;
  /** Duration of a method call */
  method_duration?: ActionModelMethodDuration;
  /** Positive rating of a given action */
  rating_positive?: number;
  /** Negative rating of a given action */
  rating_negative?: number;
  /** How many times a given action was played out */
  rating_counter?: number;
}

export type ActivationFunctionEnum = typeof ActivationFunctionEnum[keyof typeof ActivationFunctionEnum];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ActivationFunctionEnum = {
  Sigmoid: 'Sigmoid',
  NoActivation: 'NoActivation',
} as const;

export interface AnalysePdfImagesResponse {
  /** List of texts that represents the analysis of the images contained within pdf file */
  image_analysis_texts: string[];
}

export interface AnonymizerConfig {
  /** Choose whether use anonymization or not */
  anonymize?: boolean;
  /** NER model that will be used to extract entities that will be anonymized */
  ner_model?: NerModelEnum;
  /** Tags that will be anonymized */
  anonymize_tags?: string[];
  /** Choose a method of substitution of entities */
  substitution_type?: SubstitutionTypeEnum;
  [key: string]: unknown;
 }

export interface AnswerMultipleQuestionsAndGenerateReportResponse {
  /** Download link for the generated report */
  download_link: string;
}

/**
 * Available LLM models
 */
export type AvailableModels = typeof AvailableModels[keyof typeof AvailableModels];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const AvailableModels = {
  'gpt-35-turbo': 'gpt-3.5-turbo',
  'gpt-4o': 'gpt-4o',
  'gpt-4o-mini': 'gpt-4o-mini',
  'anthropicclaude-3-5-sonnet-20240620-v1:0': 'anthropic.claude-3-5-sonnet-20240620-v1:0',
  'meta-llama/Llama-31-8B-Instruct': 'meta-llama/Llama-3.1-8B-Instruct',
  'hugging-quants/Meta-Llama-31-70B-Instruct-AWQ-INT4': 'hugging-quants/Meta-Llama-3.1-70B-Instruct-AWQ-INT4',
  echo: 'echo',
} as const;

/**
 * Base class for extractor configs.
 */
export interface BaseExtractorConfig {
  /** Extractor hierarchy to use for information extraction of different documents */
  extractor_hierarchy?: ExtractorModelEnum[];
  /** Should the extractor apply Optical Character Recognition */
  apply_ocr?: boolean;
  /** Should the extractor add document title to chunks */
  add_document_title_to_chunks?: boolean;
  /** Should the extractor extract annotations */
  extract_annotations?: boolean;
  /** Should the extractor extract links */
  extract_links?: boolean;
  /**
   * The level of depth that the extractor should use
   * @minimum 1
   */
  structure_depth?: number;
  [key: string]: unknown;
 }

export interface BaseSplitterConfig {
  /** Type of splitter used for spliting extracted text chunks */
  splitter_model?: SplitterModelsEnum;
  /** Add document title to each chunk */
  add_document_title_to_chunks?: boolean;
  [key: string]: unknown;
 }

export interface BaseTopicModelingConfig {
  /** A name of a model used for topic modeling */
  modeling_model?: TopicModelingModels;
  [key: string]: unknown;
 }

export interface BodyAddCollectionKnowledgeBaseCollectionsCreatePost {
  embedding_manager_config: EmbeddingManagerConfigInput;
  processing_type?: ProcessingType;
}

export type BodyAnalyseImageAdditionalImageBase64 = string | null;

export type BodyAnalyseImagePrompt = string | null;

export interface BodyAnalyseImage {
  image_base64: string;
  additional_image_base64?: BodyAnalyseImageAdditionalImageBase64;
  prompt?: BodyAnalyseImagePrompt;
}

export type BodyAnonymizeTextAnonymizerConfig = AnonymizerConfig | null;

export interface BodyAnonymizeText {
  anonymizer_config?: BodyAnonymizeTextAnonymizerConfig;
  text: string;
}

export interface BodyAnswerMultipleQuestionsReportAnswerMultipleQuestionsAndGenerateReportPost {
  general_config: GeneralConfig;
  report_template: Blob;
}

export type BodyAskDocumentsLlmConfig = LLMInferenceConfig | null;

export interface BodyAskDocuments {
  document_ids: string[];
  query: string;
  llm_config?: BodyAskDocumentsLlmConfig;
}

export type BodyAskQuestionRagConfig = RagConfig | null;

export type BodyAskQuestionContext = LlmContext | null;

export type BodyAskQuestionDocumentIds = string[] | null;

export interface BodyAskQuestion {
  rag_config?: BodyAskQuestionRagConfig;
  query: string;
  context?: BodyAskQuestionContext;
  document_ids?: BodyAskQuestionDocumentIds;
}

export type BodyAskQuestionAsyncRagConfig = RagConfig | null;

export type BodyAskQuestionAsyncContext = LlmContext | null;

export interface BodyAskQuestionAsync {
  rag_config?: BodyAskQuestionAsyncRagConfig;
  query: string;
  context?: BodyAskQuestionAsyncContext;
}

export type BodyAskQuestionAsyncOldRagConfigAnyOf = { [key: string]: unknown };

export type BodyAskQuestionAsyncOldRagConfig = BodyAskQuestionAsyncOldRagConfigAnyOf | null;

export type BodyAskQuestionAsyncOldChatMessagesAnyOf = { [key: string]: unknown };

export type BodyAskQuestionAsyncOldChatMessages = BodyAskQuestionAsyncOldChatMessagesAnyOf | null;

export type BodyAskQuestionAsyncOldContext = LlmContext | null;

export interface BodyAskQuestionAsyncOld {
  rag_config?: BodyAskQuestionAsyncOldRagConfig;
  query: string;
  chat_messages?: BodyAskQuestionAsyncOldChatMessages;
  additional_context?: string;
  alpha?: number;
  context?: BodyAskQuestionAsyncOldContext;
}

export type BodyAskQuestionOldRagConfigAnyOf = { [key: string]: unknown };

export type BodyAskQuestionOldRagConfig = BodyAskQuestionOldRagConfigAnyOf | null;

export type BodyAskQuestionOldChatMessagesAnyOf = { [key: string]: unknown };

export type BodyAskQuestionOldChatMessages = BodyAskQuestionOldChatMessagesAnyOf | null;

export type BodyAskQuestionOldContext = LlmContext | null;

export interface BodyAskQuestionOld {
  rag_config?: BodyAskQuestionOldRagConfig;
  query: string;
  chat_messages?: BodyAskQuestionOldChatMessages;
  additional_context?: string;
  alpha?: number;
  context?: BodyAskQuestionOldContext;
}

export interface BodyAttachCollectionToDocument {
  collection_id: string;
  document_id: string;
}

export interface BodyCalculateEmbeddings {
  chunks: string[];
  embedder_model: string;
}

export interface BodyCalculateForCollection {
  chunks: string[];
  collection_id: string;
}

export type BodyCreateNodeAttributes = { [key: string]: unknown };

export type BodyCreateNodeData = { [key: string]: unknown };

export type BodyCreateNodeExtractionMetadata = { [key: string]: unknown };

export interface BodyCreateNode {
  attributes?: BodyCreateNodeAttributes;
  data?: BodyCreateNodeData;
  extraction_metadata?: BodyCreateNodeExtractionMetadata;
}

export interface BodyDocumentExtractionAndSplitting {
  document_ids: string[];
  processor_config: DocumentProcessorConfig;
}

export type BodyExtractKeywordsText = string | string[];

export type BodyExtractKeywordsConfig = BaseTopicModelingConfig | null;

export interface BodyExtractKeywords {
  text: BodyExtractKeywordsText;
  config?: BodyExtractKeywordsConfig;
}

export type BodyExtractMetadataLlmConfig = LLMInferenceConfig | null;

export interface BodyExtractMetadata {
  metadata_to_extract: string[];
  query: string;
  llm_config?: BodyExtractMetadataLlmConfig;
}

export interface BodyFillPptxTemplateReportFillPptxTemplatePost {
  general_config: GeneralConfig;
  report_template: Blob;
}

export type BodyGetRelevantDocumentsSearchConfig = SearchConfig | null;

export type BodyGetRelevantDocumentsDocumentIds = string[] | null;

export interface BodyGetRelevantDocuments {
  query: string;
  search_config?: BodyGetRelevantDocumentsSearchConfig;
  document_ids?: BodyGetRelevantDocumentsDocumentIds;
}

export type BodyGetRelevantDocumentsMultiCollectionSearchConfig = SearchConfig | null;

export interface BodyGetRelevantDocumentsMultiCollection {
  query: string;
  search_config?: BodyGetRelevantDocumentsMultiCollectionSearchConfig;
}

export interface BodyProcess {
  documents_ids_list: string[];
  processor_config: DocumentProcessorConfig;
}

export type BodyPromptLLMCollectionEntries = CollectionEntry[] | null;

export type BodyPromptLLMLlmConfig = LLMInferenceConfig | null;

export interface BodyPromptLLM {
  prompt: string;
  collection_entries?: BodyPromptLLMCollectionEntries;
  llm_config?: BodyPromptLLMLlmConfig;
}

export type BodyPromptLLMAsyncCollectionEntries = CollectionEntry[] | null;

export type BodyPromptLLMAsyncLlmConfig = LLMInferenceConfig | null;

export interface BodyPromptLLMAsync {
  prompt: string;
  collection_entries?: BodyPromptLLMAsyncCollectionEntries;
  llm_config?: BodyPromptLLMAsyncLlmConfig;
}

export interface BodyQueryTable {
  config?: LlmConfig;
  file_config?: TabularFileConfig;
  query: string;
}

export interface BodyReadTemplateReportReadTemplatePost {
  file: Blob;
}

export type BodySplitDocumentSplitterConfig = BaseSplitterConfig | null;

export interface BodySplitDocument {
  document_chunks: CollectionEntry[];
  splitter_config?: BodySplitDocumentSplitterConfig;
}

export interface BodySummarizeCollection {
  structure_depth: number;
  llm?: AvailableModels;
  override_summary?: boolean;
}

export interface BodySummarizeDocument {
  document_id: string;
  override_summary?: boolean;
  llm?: AvailableModels;
  include_intermediate_steps?: boolean;
}

export interface BodySummarizeText {
  text: string;
  llm?: AvailableModels;
  include_intermediate_steps?: boolean;
}

export interface BodySummarizeTextList {
  text_list: string[];
  llm?: AvailableModels;
  include_intermediate_steps?: boolean;
}

export interface BodySummarizeTopic {
  /** @minLength 1 */
  query: string;
  collection_id: string;
  /** @minimum 1 */
  top_k: number;
  llm?: AvailableModels;
  /**
   * @maximum 2
   */
  similarity_thr?: number;
  include_intermediate_steps?: boolean;
}

export type BodyUploadAndProcessProcessorConfig = DocumentProcessorConfig | null;

export interface BodyUploadAndProcess {
  files: Blob[];
  processor_config?: BodyUploadAndProcessProcessorConfig;
}

export interface BodyUploadDocuments {
  files: Blob[];
}

export interface BodyUploadSnapshots {
  database_snapshots: Blob[];
}

export interface BodyUploadSqlTable {
  csv_file: Blob;
}

export interface BodyUploadKnowledgeBaseUploadPost {
  files: Blob[];
}

/**
 * A model for chat entry
 */
export interface ChatEntry {
  /** A role of an entity that the message belongs to */
  role: ChatRoles;
  /** A message that was made for this chat entry */
  message: ChatMessage;
}

/**
 * A model for chat message
 */
export interface ChatMessage {
  /** A kind of message that is send */
  kind: MessageKind;
  /** A content of a message */
  content: string;
}

/**
 * A model for chat messages
 */
export interface ChatMessages {
  /** A list containing the messages of Chat entries */
  messages?: ChatEntry[];
}

export type ChatRoles = typeof ChatRoles[keyof typeof ChatRoles];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ChatRoles = {
  system: 'system',
  user: 'user',
  chatbot: 'chatbot',
} as const;

/**
 * Auto-incremented id
 */
export type CollectionEntryId = number | null;

/**
 * Collection of the entry
 */
export type CollectionEntryCollection = string | null;

/**
 * Embedding of a chunk
 */
export type CollectionEntryEmbedding = number[] | null;

/**
 * Name of the retriever
 */
export type CollectionEntryRetrieverNameProperty = CollectionEntryRetrieverName | null;

/**
 * Model for collection entry
 */
export interface CollectionEntry {
  /** Auto-incremented id */
  id?: CollectionEntryId;
  /** A chunk id */
  chunk_id?: string;
  /** Text chunk */
  chunk: string;
  /** Collection of the entry */
  collection?: CollectionEntryCollection;
  /** Metadata of a collection entry */
  document_metadata: CollectionEntryMetadata;
  /** Embedding of a chunk */
  embedding?: CollectionEntryEmbedding;
  /** Name of the retriever */
  retriever_name?: CollectionEntryRetrieverNameProperty;
}

/**
 * Name of a document from which a given chunk was derived
 */
export type CollectionEntryMetadataDocumentName = string | null;

/**
 * Model for collection entry's metadata
 */
export interface CollectionEntryMetadata {
  /** Id of a document from which a chunk was derived */
  document_id?: string;
  /** Name of a document from which a given chunk was derived */
  document_name?: CollectionEntryMetadataDocumentName;
  /** Page number on which a chunk is located */
  page_number?: number;
  /** Source of a given chunk */
  source?: string;
  /** A list of chunk ids */
  links?: string[];
}

/**
 * Enum for retriever names
 */
export type CollectionEntryRetrieverName = typeof CollectionEntryRetrieverName[keyof typeof CollectionEntryRetrieverName];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CollectionEntryRetrieverName = {
  duckduckgo: 'duckduckgo',
  semantic_search: 'semantic_search',
  documents_filter: 'documents_filter',
} as const;

/**
 * Database collection id
 */
export type CollectionModelId = string | null;

/**
 * ID of relevant vector collection
 */
export type CollectionModelVectorCollectionId = string | null;

/**
 * Name of the collection
 */
export type CollectionModelName = string | null;

/**
 * Date and time of the creation of the collection entry
 */
export type CollectionModelCreatedAt = string | null;

/**
 * Anonymization mapping of the collection
 */
export type CollectionModelAnonymizationMapping = { [key: string]: unknown };

/**
 * Embedding Manager Config that is used for embedding creation
 */
export type CollectionModelEmbeddingManagerConfig = EmbeddingManagerConfigOutput | null;

/**
 * Hashes for documents that are in the database
 */
export type CollectionModelDocumentProcessingHashes = { [key: string]: unknown };

/**
 * Dictionary of metadata fields to extract from the collection
 */
export type CollectionModelMetadataToExtract = {[key: string]: string | null};

/**
 * Additional collection metadata
 */
export type CollectionModelCollectionMetadata = { [key: string]: unknown };

/**
 * Model of a Collection
 */
export interface CollectionModel {
  /** Database collection id */
  id?: CollectionModelId;
  /** ID of relevant vector collection */
  vector_collection_id?: CollectionModelVectorCollectionId;
  /** Name of the collection */
  name?: CollectionModelName;
  /** Date and time of the creation of the collection entry */
  created_at?: CollectionModelCreatedAt;
  /** Date and time of the last update of the collection entry */
  updated_at?: string;
  /** Anonymization mapping of the collection */
  anonymization_mapping?: CollectionModelAnonymizationMapping;
  /** Embedding Manager Config that is used for embedding creation */
  embedding_manager_config?: CollectionModelEmbeddingManagerConfig;
  /** Hashes for documents that are in the database */
  document_processing_hashes?: CollectionModelDocumentProcessingHashes;
  /** A topology of a graph */
  graph_topology?: NodeListOutput;
  /** Dictionary of metadata fields to extract from the collection */
  metadata_to_extract?: CollectionModelMetadataToExtract;
  /** Additional collection metadata */
  collection_metadata?: CollectionModelCollectionMetadata;
  /** What kind of processing pipeline to run on given collection documents */
  processing_type?: ProcessingType;
}

export type CrossEmbeddingModelEnum = typeof CrossEmbeddingModelEnum[keyof typeof CrossEmbeddingModelEnum];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const CrossEmbeddingModelEnum = {
  'cross-encoder/ms-marco-TinyBERT-L-2-v2': 'cross-encoder/ms-marco-TinyBERT-L-2-v2',
  'cross-encoder/ms-marco-MiniLM-L-2-v2': 'cross-encoder/ms-marco-MiniLM-L-2-v2',
  'cross-encoder/ms-marco-MiniLM-L-4-v2': 'cross-encoder/ms-marco-MiniLM-L-4-v2',
  'cross-encoder/ms-marco-MiniLM-L-6-v2': 'cross-encoder/ms-marco-MiniLM-L-6-v2',
} as const;

export type DocumentCategory = typeof DocumentCategory[keyof typeof DocumentCategory];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DocumentCategory = {
  tabular: 'tabular',
  textual: 'textual',
  all: 'all',
  image: 'image',
} as const;

export interface DocumentProcessorConfig {
  /** A config used for extracting the information from documents. */
  extractor_config?: BaseExtractorConfig;
  /** A config used for splitting the extracted text. */
  splitter_config?: BaseSplitterConfig;
}

export type DocumentStatus = typeof DocumentStatus[keyof typeof DocumentStatus];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DocumentStatus = {
  init: 'init',
  uploaded: 'uploaded',
  in_process: 'in_process',
  processed: 'processed',
  failed: 'failed',
  backup: 'backup',
  in_queue: 'in_queue',
} as const;

export type DocumentType = typeof DocumentType[keyof typeof DocumentType];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const DocumentType = {
  all: 'all',
  pdf: 'pdf',
  docx: 'docx',
  jpg: 'jpg',
  png: 'png',
  xlsx: 'xlsx',
  xls: 'xls',
  csv: 'csv',
  txt: 'txt',
  html: 'html',
  json: 'json',
} as const;

export type EmbedderChoices = typeof EmbedderChoices[keyof typeof EmbedderChoices];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const EmbedderChoices = {
  openai: 'openai',
  sentence_transformer_fast: 'sentence_transformer_fast',
} as const;

/**
 * Configuration for Embedder
 */
export interface EmbedderConfig {
  /** Type of an embedding model used for creating embeddings */
  embedding?: EmbedderChoices;
}

/**
 * Configuration for Embedding Manager
 */
export interface EmbeddingManagerConfigInput {
  vector_database_kind?: VectorDatabaseKind;
  /** An embedder config that governs how the embeddings should be created */
  embedder_config?: EmbedderConfig;
}

/**
 * Configuration for Embedding Manager
 */
export interface EmbeddingManagerConfigOutput {
  vector_database_kind?: VectorDatabaseKind;
  /** An embedder config that governs how the embeddings should be created */
  embedder_config?: EmbedderConfig;
}

export interface ExtractImagesFromPdfResponse {
  /** Images encoded to a Base64 string */
  images: string[];
}

export type ExtractedMetadataModelMetadataItem = string | number;

/**
 * The extracted metadata from the user query
 */
export type ExtractedMetadataModelMetadata = {[key: string]: ExtractedMetadataModelMetadataItem[]};

/**
 * Model to validate the structure of the extracted metadata
 */
export interface ExtractedMetadataModel {
  /** The extracted metadata from the user query */
  metadata: ExtractedMetadataModelMetadata;
}

export type ExtractorModelEnum = typeof ExtractorModelEnum[keyof typeof ExtractorModelEnum];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ExtractorModelEnum = {
  auto: 'auto',
  pdf_layour_parser: 'pdf_layour_parser',
  image_ocr: 'image_ocr',
} as const;

export interface GeneralConfig { [key: string]: unknown }

/**
 * Node data
 */
export type GraphNodeModelNodeData = { [key: string]: unknown };

/**
 * Model of a Node
 */
export interface GraphNodeModel {
  /** Node id */
  id?: string;
  /** Graph collection */
  collection?: string;
  /** Node type */
  node_type?: GraphNodeType;
  /** Node data */
  node_data?: GraphNodeModelNodeData;
}

export type GraphNodeType = typeof GraphNodeType[keyof typeof GraphNodeType];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const GraphNodeType = {
  entity: 'entity',
  file: 'file',
  table: 'table',
  artifact: 'artifact',
  object: 'object',
  identifier: 'identifier',
} as const;

export type GraphResponseNodes = {[key: string]: { [key: string]: unknown }};

export type GraphResponseEdges = {[key: string]: { [key: string]: unknown }};

export interface GraphResponse {
  nodes: GraphResponseNodes;
  edges: GraphResponseEdges;
}

export interface HTTPValidationError {
  detail?: ValidationError[];
}

/**
 * Chose a provider for a given model. When provider is not specified then it is chosen automatically
 */
export type LLMInferenceConfigProvider = LLMProvider | null;

/**
 * Seed responsible for model reproducibility
 */
export type LLMInferenceConfigSeed = number | null;

/**
 * Maximum amount of tokens on output. Could be omitted, then equal half the output limit
 */
export type LLMInferenceConfigMaxTokens = number | null;

/**
 * Some very model specific configuration that is not repeatable among models
 */
export type LLMInferenceConfigModelSpecificConfiguration = { [key: string]: unknown };

/**
 * Config for llm inference
 */
export interface LLMInferenceConfig {
  /**
   * Model's temperature that is used to change its behaviour. Higher values mean more randomness.
   * @minimum 0
   * @maximum 1
   */
  temperature_scale?: number;
  /** An LLM model used for inference. */
  llm?: AvailableModels;
  /** Chose a provider for a given model. When provider is not specified then it is chosen automatically */
  provider?: LLMInferenceConfigProvider;
  /** Seed responsible for model reproducibility */
  seed?: LLMInferenceConfigSeed;
  /** Use streaming capabilities of a model */
  use_stream?: boolean;
  /** Use vision capabilities of a model */
  use_vision?: boolean;
  /** Maximum amount of tokens on output. Could be omitted, then equal half the output limit */
  max_tokens?: LLMInferenceConfigMaxTokens;
  /** Some very model specific configuration that is not repeatable among models */
  model_specific_configuration?: LLMInferenceConfigModelSpecificConfiguration;
}

/**
 * Available providers for llms
 */
export type LLMProvider = typeof LLMProvider[keyof typeof LLMProvider];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const LLMProvider = {
  openai: 'openai',
  bedrock: 'bedrock',
  vllm: 'vllm',
  echo: 'echo',
} as const;

export interface LlmConfig {
  llm?: LlmEnum;
  [key: string]: unknown;
 }

export interface LlmContext {
  /** Chat messages for the llm */
  chat_messages?: ChatMessages;
  /** A custom instruction injected at the end of the system prompt */
  custom_instructions?: string;
  /** A chosen prompt, used by default for each llm request */
  system_message_type?: SystemPromptEnum;
}

export type LlmEnum = typeof LlmEnum[keyof typeof LlmEnum];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const LlmEnum = {
  openai: 'openai',
  'gpt-35-turbo': 'gpt-3.5-turbo',
  'gpt-4o': 'gpt-4o',
  'gpt-4o-mini': 'gpt-4o-mini',
  llama_cpp_17b: 'llama_cpp_17b',
  'openllm-external': 'openllm-external',
  'openllm-external-other': 'openllm-external-other',
  bedrock: 'bedrock',
  vllm: 'vllm',
  _echo: '_echo',
  'anthropicclaude-3-5-sonnet-20240620-v1:0': 'anthropic.claude-3-5-sonnet-20240620-v1:0',
} as const;

export type MessageKind = typeof MessageKind[keyof typeof MessageKind];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const MessageKind = {
  text: 'text',
  image: 'image',
} as const;

export type NerModelEnum = typeof NerModelEnum[keyof typeof NerModelEnum];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const NerModelEnum = {
  spacy: 'spacy',
} as const;

/**
 * List of nodes to which a given node points to.
 */
export type NodeInputChildren = NodeInput[] | string[];

export interface NodeInput {
  /** Node id */
  id?: string;
  /** Name of a given node */
  name: string;
  /** List of nodes to which a given node points to. */
  children?: NodeInputChildren;
  /** A list of ids pointing to a given text chunk containing node related text */
  text_ids?: string[];
}

export interface NodeOutput {
  /** Node id */
  id?: string;
  /** Name of a given node */
  name: string;
  /** List of nodes to which a given node points to. */
  children?: string[];
  /** A list of ids pointing to a given text chunk containing node related text */
  text_ids?: string[];
}

export interface NodeListInput {
  /** A list of nodes */
  nodes?: NodeInput[];
  /** Nodes which aren't pointed to by any other Node */
  roots?: NodeInput[];
}

export interface NodeListOutput {
  /** A list of nodes */
  nodes?: NodeOutput[];
  /** Nodes which aren't pointed to by any other Node */
  roots?: NodeOutput[];
}

export type ProcessingType = typeof ProcessingType[keyof typeof ProcessingType];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const ProcessingType = {
  regular: 'regular',
  other: 'other',
} as const;

export interface QueryTableResponse {
  answer: string;
  kind: string;
  code: string;
}

/**
 * Configuration for QA module
 */
export interface RagConfig {
  /** Config used for semantic search */
  search_config?: SearchConfig;
  /** Config used for llm inference */
  llm_config?: LLMInferenceConfig;
  /** Config used for reference manager */
  reference_manager_config?: ReferenceManagerConfig;
  /** Config used for anonymization */
  anonymizer_config?: AnonymizerConfig;
}

export interface RankerConfig {
  /** Model name of the pre-trained ranker */
  model_name?: CrossEmbeddingModelEnum;
  /** Batch size for ranker */
  batch_size?: number;
  activation_function?: ActivationFunctionEnum;
}

export interface RankerData {
  query: string;
  chunks: string[];
  config?: RankerConfig;
}

export type RankerResponseMsg = string | null;

export interface RankerResponse {
  result: number[];
  status: string;
  msg?: RankerResponseMsg;
}

export interface ReadTemplateResponse {
  /** Retrieved prompts from .docx file for QA */
  prompts: string[];
}

export interface ReferenceManagerConfig {
  /**
   * Number of top documents to return
   */
  top_k?: number;
  /**
   * Alpha parameter 0 for bm25, 1 for embeddings, 0.5 for both
   * @minimum 0
   * @maximum 1
   */
  alpha?: number;
  /** Threshold to select most relevant documents */
  threshold?: number;
  /** Whether to rescore references */
  rescore_references?: boolean;
}

export interface SearchConfig {
  /**
   * Number of top documents to return
   */
  top_k?: number;
  /**
   * Number of top chunks from web search
   * @minimum 0
   */
  top_k_www?: number;
  /**
   * Alpha parameter 0 for bm25, 1 for embeddings, 0.5 for both
   * @minimum 0
   * @maximum 1
   */
  alpha?: number;
  /** Whether to use ranker */
  use_ranker?: boolean;
  /** Whether to group the same documents together. When enabled it groups documents's chunks into bucket and sorts them by page */
  group_documents?: boolean;
  /** When enabled the same documents' chunks are merged i.e.There is one (big) chunk per document. */
  merge_documents?: boolean;
}

export type SplitterModelsEnum = typeof SplitterModelsEnum[keyof typeof SplitterModelsEnum];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SplitterModelsEnum = {
  spacy: 'spacy',
  nltk: 'nltk',
  langchain: 'langchain',
  adjecent_clustering: 'adjecent_clustering',
  bypass: 'bypass',
} as const;

export type SubstitutionTypeEnum = typeof SubstitutionTypeEnum[keyof typeof SubstitutionTypeEnum];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SubstitutionTypeEnum = {
  simple: 'simple',
  advanced: 'advanced',
} as const;

export type SystemPromptEnum = typeof SystemPromptEnum[keyof typeof SystemPromptEnum];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const SystemPromptEnum = {
  default: 'default',
  ioactive: 'ioactive',
  bmw: 'bmw',
  sita_code_assistant: 'sita_code_assistant',
} as const;

export type TabularFileConfigThousands = string | null;

export interface TabularFileConfig {
  header?: number;
  thousands?: TabularFileConfigThousands;
}

export type TopicModelingModels = typeof TopicModelingModels[keyof typeof TopicModelingModels];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const TopicModelingModels = {
  KeyBERT: 'KeyBERT',
} as const;

export type ValidationErrorLocItem = string | number;

export interface ValidationError {
  loc: ValidationErrorLocItem[];
  msg: string;
  type: string;
}

export type VectorDatabaseKind = typeof VectorDatabaseKind[keyof typeof VectorDatabaseKind];


// eslint-disable-next-line @typescript-eslint/no-redeclare
export const VectorDatabaseKind = {
  chroma: 'chroma',
  weaviate: 'weaviate',
  postgres: 'postgres',
} as const;

export type SummarizeCollectionParams = {
collection_id: string;
};

export type ListVectorsParams = {
exclude_vectors?: boolean;
};

export type AddVectorCollectionParams = {
collection_name?: string;
processing_type?: ProcessingType;
};

export type AddVectorsParams = {
collection_id: string;
};

export type GetChunksForDocumentParams = {
document_id: string;
collection_id?: string | null;
};

export type GetAllChunksFromCollectionParams = {
collection_id: string;
};

export type GetKeywordsForChunksParams = {
document_id: string;
collection_id?: string | null;
};

export type GetKeywordsForChunks200 = {[key: string]: string[]};

export type AddTopologyToCollectionParams = {
collection_id: string;
};

export type GetFilledTopologyGraphParams = {
collection_id: string;
};

export type GetFilledTopologyGraph200Item = { [key: string]: unknown };

export type GetFilledTopologyGraph200 = {[key: string]: GetFilledTopologyGraph200Item[]};

export type CopyEmbeddingsParams = {
collection_id_source: string;
collection_id_target: string;
document_id?: string | null;
overwrite?: boolean;
};

export type ResetCollectionParams = {
collection_id: string;
};

export type UpdateCollectionMetadataToExtractParams = {
collection_id: string;
};

export type UpdateCollectionMetadataToExtractBody = {[key: string]: string | null};

export type DownloadCollectionDocumentsParams = {
collection_id: string;
};

export type ChangeDocumentStatusParams = {
initial_status: DocumentStatus;
target_status: DocumentStatus;
};

export type DeleteDocumentsWithGivenStatusParams = {
document_status: DocumentStatus;
};

export type GetDocumentsParams = {
document_category?: DocumentCategory;
document_type?: DocumentType;
n?: number;
include_chunks?: boolean;
};

export type DeleteAllDocumentsParams = {
delete_confirmation?: boolean;
delete_in_process?: boolean;
};

export type GetTokenStatisticsOfDocumentBody = LLMInferenceConfig | null;

export type DownloadFromTemporaryLinkParams = {
file_path: string;
expires: number;
signature: string;
};

export type AskQuestionParams = {
collection_id: string;
};

export type AskQuestionOldParams = {
collection_id: string;
};

export type AskQuestionAsyncParams = {
collection_id: string;
};

export type AskQuestionAsync200AnyOf = { [key: string]: unknown };

export type AskQuestionAsync200 = {[key: string]: string | AskQuestionAsync200AnyOf};

export type AskQuestionAsyncOldParams = {
collection_id: string;
};

export type AskQuestionAsyncOld200AnyOf = { [key: string]: unknown };

export type AskQuestionAsyncOld200 = {[key: string]: string | AskQuestionAsyncOld200AnyOf};

export type QueryTableParams = {
document_id: string;
};

export type PromptLLM200 = {[key: string]: string};

export type PromptLLMAsync200 = {[key: string]: string};

export type QueryDatabaseParams = {
query: string;
table_name?: string | null;
};

export type GenerateQuestionsAboutSourceParams = {
query?: string | null;
document_id?: string | null;
collection_id?: string | null;
};

export type GenerateQuestionsAboutSourceBody = LLMInferenceConfig | null;

export type ExtractMetadataFromCollectionParams = {
collection_id: string;
};

export type ExtractMetadataFromCollectionBody = LLMInferenceConfig | null;

export type ExtractMetadataFromCollection200 = {[key: string]: ExtractedMetadataModel};

export type UploadDocumentsParams = {
collection_id?: string | null;
};

export type UploadSqlTableParams = {
table_name: string;
db_name?: string;
db_host?: string;
db_port?: number;
db_user?: string;
db_password?: string;
};

export type UploadAndProcessParams = {
collection_id?: string | null;
add_keywords_to_chunks?: boolean;
anonymize?: boolean;
};

export type AnalysePdfImagesReportAnalysePdfImagesPostParams = {
document_id: string;
prompt?: string | null;
};

export type ExtractImagesFromPdfReportExtractImagesFromPdfPostParams = {
document_id: string;
};

export type ProcessDocumentsParams = {
overwrite?: boolean;
collection_id?: string | null;
anonymize?: boolean;
wait?: boolean;
add_keywords_to_chunks?: boolean;
add_chunk_summarizations?: boolean;
};

export type ProcessDocumentsBody = DocumentProcessorConfig | null;

export type ProcessDocumentsSyncParams = {
collection_id: string;
overwrite?: boolean;
};

export type ProcessDocumentsSyncBody = DocumentProcessorConfig | null;

export type ProcessDocumentsSync200Item = { [key: string]: unknown };

export type ProcessDocumentsSync200 = {[key: string]: ProcessDocumentsSync200Item[]};

export type StopProcessingParams = {
collection?: string;
};

export type StopProcessing200 = {[key: string]: boolean};

export type ProcessParams = {
collection_id: string;
};

export type ProcessStatusChangeParams = {
document_id: string;
};

export type DocumentExtractionAndSplitting200 = {[key: string]: CollectionEntry[]};

export type ExtractDocumentParams = {
document_id: string;
};

export type ExtractDocumentBody = BaseExtractorConfig | null;

export type ExtractDocument200 = { [key: string]: unknown };

export type SplitDocument200 = { [key: string]: unknown };

export type ExtractGraphFromDocumentParams = {
document_id: string;
sub_node?: string | null;
};

export type ExtractGraphFromDocument200Item = { [key: string]: unknown };

export type ExtractGraphFromDocument200 = {[key: string]: ExtractGraphFromDocument200Item[]};

export type AsyncProcessingTestsParams = {
overwrite?: boolean;
collection_id?: string | null;
anonymize?: boolean;
add_keywords_to_chunks?: boolean;
add_chunk_summarizations?: boolean;
};

export type AsyncProcessingTestsBody = DocumentProcessorConfig | null;

export type ExtractEntitiesGraphFromDocuments200 = { [key: string]: unknown };

export type ExtractEntitiesGraphSender1200 = { [key: string]: unknown };

export type ExtractEntitiesGraphSender2200 = { [key: string]: unknown };

export type ExtractEntitiesGraphSenderFileNodes200 = { [key: string]: unknown };

export type ExtractEntitiesGraphSenderModelFileNodes200 = { [key: string]: unknown };

export type ExtractEntitiesGraphSenderTables200 = { [key: string]: unknown };

export type ExtractEntitiesGraphSenderIdentifiers200 = { [key: string]: unknown };

export type ExtractEntitiesGraphSenderInstances200 = { [key: string]: unknown };

export type ExtractEntitiesGraphSenderEnrichmentMatching200 = { [key: string]: unknown };

export type ExtractEntitiesGraphSenderEnrichmentModels200 = { [key: string]: unknown };

export type ExtractEntitiesGraphSenderEnrichmentFuzzy200 = { [key: string]: unknown };

export type ExtractEntitiesGraphSenderJoinIdentifiers200 = { [key: string]: unknown };

export type ProcessStepParams = {
step: number;
collection?: string;
};

export type ProcessStep202 = { [key: string]: unknown };

export type GetProcessingStatusParams = {
collection?: string;
};

export type GetProcessingStatus200 = { [key: string]: unknown };

export type ResetProcessingParams = {
step?: number;
collection?: string;
};

export type ResetProcessing200 = { [key: string]: unknown };

export type ListCollections200 = { [key: string]: unknown };

export type CreateCollectionParams = {
name: string;
};

export type CreateCollection201 = { [key: string]: unknown };

export type DeleteCollection200 = { [key: string]: unknown };

export type CopyCollectionParams = {
source: string;
target: string;
};

export type CopyCollection200 = { [key: string]: unknown };

export type GetRelevantDocumentsParams = {
collection_id?: string;
};

export type GetActions200 = {[key: string]: ActionModel[]};

export type RateActionParams = {
action_id: string;
rating: number;
};

export type MakeSnapshot200 = string | null;

export type LoadSnapshotParams = {
snapshot_id: string;
};

export type LoadSnapshot200 = string | null;

export type DeleteSnapshotParams = {
snapshot_id: string;
};

export type ListDocumentsKnowledgeBaseDocumentsGetParams = {
document_category?: DocumentCategory;
document_type?: DocumentType;
n?: number;
include_chunks?: boolean;
};

export type GetDocumentKnowledgeBaseDocumentsDocumentIdGetParams = {
include_chunks?: boolean;
};

export type DownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetParams = {
collection_id: string;
};

export type ChangeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPostParams = {
initial_status: DocumentStatus;
target_status: DocumentStatus;
allow_unsafe?: boolean;
};

export type DeleteAllDocumentsKnowledgeBaseDocumentsDeleteParams = {
delete_confirmation?: boolean;
delete_in_process?: boolean;
};

export type AddCollectionKnowledgeBaseCollectionsCreatePostParams = {
collection_name?: string;
};

export type GetObjectsGraphParams = {
collection?: string;
};

export type GetGraphParams = {
collection?: string;
};

export type GetGraphViewParams = {
node_types: unknown[];
base_relation?: string;
metadata_only?: boolean;
collection?: string;
optimization_level?: number;
};

export type GetGraphView200 = { [key: string]: unknown };

export type DeleteNodeParams = {
node_id: string;
collection?: string;
};

export type GetNodeParams = {
node_id: string;
collection?: string;
};

export type UpdateObjectNodeParams = {
node_name?: string;
node_id?: string;
collection?: string;
upsert_if_missing?: boolean;
};

export type UpdateObjectNodeBody = { [key: string]: unknown };

export type UpdateObjectNode200AnyOf = { [key: string]: unknown };

export type UpdateObjectNode200 = boolean | UpdateObjectNode200AnyOf | GraphNodeModel;

export type CreateNodeParams = {
name: string;
node_type?: GraphNodeType;
collection?: string;
};

export type CreateRelationParams = {
source_id: string;
target_id: string;
relation_type?: string;
is_bidirectional?: boolean;
collection?: string;
};

export type CreateRelationBody = { [key: string]: unknown };

export type CreateRelation200 = { [key: string]: unknown };

export type DeleteRelationParams = {
source_id: string;
target_id: string;
relation_type?: string;
collection?: string;
};

export type DeleteRelation200 = { [key: string]: unknown };

export type CopyNodeToCollectionParams = {
node_id: string;
source_collection: string;
target_collection: string;
};

