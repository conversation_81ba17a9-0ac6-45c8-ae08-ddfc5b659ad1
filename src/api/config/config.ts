/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import { apiUrlMutator } from '../../config/mutator';




/**
 * @summary List Embeddings And Llms
 */
export const listEmbeddingsAndLLMs = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/config/list_embeddings_and_llms`, method: 'GET', signal
    },
  );
}


export const getListEmbeddingsAndLLMsQueryKey = () => {
  return [`/config/list_embeddings_and_llms`] as const;
}


export const getListEmbeddingsAndLLMsQueryOptions = <TData = Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>, TError = unknown>(options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getListEmbeddingsAndLLMsQueryKey();



  const queryFn: QueryFunction<Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>> = ({ signal }) => listEmbeddingsAndLLMs(signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type ListEmbeddingsAndLLMsQueryResult = NonNullable<Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>>
export type ListEmbeddingsAndLLMsQueryError = unknown


export function useListEmbeddingsAndLLMs<TData = Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>,
        TError,
        Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListEmbeddingsAndLLMs<TData = Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>,
        TError,
        Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListEmbeddingsAndLLMs<TData = Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary List Embeddings And Llms
 */

export function useListEmbeddingsAndLLMs<TData = Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listEmbeddingsAndLLMs>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getListEmbeddingsAndLLMsQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * @summary List Text Splitters
 */
export const listTextSplitters = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/config/list_text_splitters`, method: 'GET', signal
    },
  );
}


export const getListTextSplittersQueryKey = () => {
  return [`/config/list_text_splitters`] as const;
}


export const getListTextSplittersQueryOptions = <TData = Awaited<ReturnType<typeof listTextSplitters>>, TError = unknown>(options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listTextSplitters>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getListTextSplittersQueryKey();



  const queryFn: QueryFunction<Awaited<ReturnType<typeof listTextSplitters>>> = ({ signal }) => listTextSplitters(signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof listTextSplitters>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type ListTextSplittersQueryResult = NonNullable<Awaited<ReturnType<typeof listTextSplitters>>>
export type ListTextSplittersQueryError = unknown


export function useListTextSplitters<TData = Awaited<ReturnType<typeof listTextSplitters>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof listTextSplitters>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof listTextSplitters>>,
        TError,
        Awaited<ReturnType<typeof listTextSplitters>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListTextSplitters<TData = Awaited<ReturnType<typeof listTextSplitters>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listTextSplitters>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof listTextSplitters>>,
        TError,
        Awaited<ReturnType<typeof listTextSplitters>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListTextSplitters<TData = Awaited<ReturnType<typeof listTextSplitters>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listTextSplitters>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary List Text Splitters
 */

export function useListTextSplitters<TData = Awaited<ReturnType<typeof listTextSplitters>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listTextSplitters>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getListTextSplittersQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * @summary List Extractor Models
 */
export const listExtractorModels = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/config/list_extractor_models`, method: 'GET', signal
    },
  );
}


export const getListExtractorModelsQueryKey = () => {
  return [`/config/list_extractor_models`] as const;
}


export const getListExtractorModelsQueryOptions = <TData = Awaited<ReturnType<typeof listExtractorModels>>, TError = unknown>(options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listExtractorModels>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getListExtractorModelsQueryKey();



  const queryFn: QueryFunction<Awaited<ReturnType<typeof listExtractorModels>>> = ({ signal }) => listExtractorModels(signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof listExtractorModels>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type ListExtractorModelsQueryResult = NonNullable<Awaited<ReturnType<typeof listExtractorModels>>>
export type ListExtractorModelsQueryError = unknown


export function useListExtractorModels<TData = Awaited<ReturnType<typeof listExtractorModels>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof listExtractorModels>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof listExtractorModels>>,
        TError,
        Awaited<ReturnType<typeof listExtractorModels>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListExtractorModels<TData = Awaited<ReturnType<typeof listExtractorModels>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listExtractorModels>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof listExtractorModels>>,
        TError,
        Awaited<ReturnType<typeof listExtractorModels>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListExtractorModels<TData = Awaited<ReturnType<typeof listExtractorModels>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listExtractorModels>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary List Extractor Models
 */

export function useListExtractorModels<TData = Awaited<ReturnType<typeof listExtractorModels>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listExtractorModels>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getListExtractorModelsQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



