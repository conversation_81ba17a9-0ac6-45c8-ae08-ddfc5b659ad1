/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation
} from '@tanstack/react-query';
import type {
  MutationFunction,
  QueryClient,
  UseMutationOptions,
  UseMutationResult
} from '@tanstack/react-query';

import type {
  BodyExtractMetadata,
  ExtractMetadataFromCollection200,
  ExtractMetadataFromCollectionBody,
  ExtractMetadataFromCollectionParams,
  ExtractedMetadataModel,
  HTTPValidationError
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * Extract chosen metadata from the query

Args:
    metadata_to_extract (list[str]): The metadata to extract from the query ex. name, location etc.
    query (str): The query to extract the metadata from
    llm_config (LLMInferenceConfig | None): The LLM configuration to use for extraction.

Returns:
    A dictionary with the extracted metadata
 * @summary Extract Metadata
 */
export const extractMetadata = (
  bodyExtractMetadata: BodyExtractMetadata,
  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractedMetadataModel>(
    {
      url: `/extraction/extract_metadata`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyExtractMetadata, signal
    },
  );
}



export const getExtractMetadataMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractMetadata>>, TError, { data: BodyExtractMetadata }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractMetadata>>, TError, { data: BodyExtractMetadata }, TContext> => {

  const mutationKey = ['extractMetadata'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractMetadata>>, { data: BodyExtractMetadata }> = (props) => {
    const { data } = props ?? {};

    return extractMetadata(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractMetadataMutationResult = NonNullable<Awaited<ReturnType<typeof extractMetadata>>>
export type ExtractMetadataMutationBody = BodyExtractMetadata
export type ExtractMetadataMutationError = HTTPValidationError

/**
* @summary Extract Metadata
*/
export const useExtractMetadata = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractMetadata>>, TError, { data: BodyExtractMetadata }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractMetadata>>,
      TError,
      { data: BodyExtractMetadata },
      TContext
    > => {

  const mutationOptions = getExtractMetadataMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Extract metadata from all documents in a collection.

Args:
collection_id (str): The ID of the collection to extract metadata from.
llm_config (LLMInferenceConfig | None): The LLM configuration to use for extraction.

Returns:
A mapping of document IDs to extracted metadata.
* @summary Extract Metadata From Collection
*/
export const extractMetadataFromCollection = (
  extractMetadataFromCollectionBody: ExtractMetadataFromCollectionBody,
  params: ExtractMetadataFromCollectionParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<ExtractMetadataFromCollection200>(
    {
      url: `/extraction/extract_metadata_from_collection`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: extractMetadataFromCollectionBody,
      params, signal
    },
  );
}



export const getExtractMetadataFromCollectionMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractMetadataFromCollection>>, TError, { data: ExtractMetadataFromCollectionBody; params: ExtractMetadataFromCollectionParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof extractMetadataFromCollection>>, TError, { data: ExtractMetadataFromCollectionBody; params: ExtractMetadataFromCollectionParams }, TContext> => {

  const mutationKey = ['extractMetadataFromCollection'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof extractMetadataFromCollection>>, { data: ExtractMetadataFromCollectionBody; params: ExtractMetadataFromCollectionParams }> = (props) => {
    const { data, params } = props ?? {};

    return extractMetadataFromCollection(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ExtractMetadataFromCollectionMutationResult = NonNullable<Awaited<ReturnType<typeof extractMetadataFromCollection>>>
export type ExtractMetadataFromCollectionMutationBody = ExtractMetadataFromCollectionBody
export type ExtractMetadataFromCollectionMutationError = HTTPValidationError

/**
* @summary Extract Metadata From Collection
*/
export const useExtractMetadataFromCollection = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof extractMetadataFromCollection>>, TError, { data: ExtractMetadataFromCollectionBody; params: ExtractMetadataFromCollectionParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof extractMetadataFromCollection>>,
      TError,
      { data: ExtractMetadataFromCollectionBody; params: ExtractMetadataFromCollectionParams },
      TContext
    > => {

  const mutationOptions = getExtractMetadataFromCollectionMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
