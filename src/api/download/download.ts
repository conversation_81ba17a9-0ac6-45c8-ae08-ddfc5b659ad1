/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import type {
  DownloadFromTemporaryLinkParams,
  HTTPValidationError
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * @summary Download File
 */
export const downloadDocument = (
  documentId: string,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/download_file/${documentId}`, method: 'GET', signal
    },
  );
}


export const getDownloadDocumentQueryKey = (documentId: string,) => {
  return [`/download_file/${documentId}`] as const;
}


export const getDownloadDocumentQueryOptions = <TData = Awaited<ReturnType<typeof downloadDocument>>, TError = HTTPValidationError>(documentId: string, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadDocument>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getDownloadDocumentQueryKey(documentId);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof downloadDocument>>> = ({ signal }) => downloadDocument(documentId, signal);





  return { queryKey, queryFn, enabled: !!(documentId), ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof downloadDocument>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type DownloadDocumentQueryResult = NonNullable<Awaited<ReturnType<typeof downloadDocument>>>
export type DownloadDocumentQueryError = HTTPValidationError


export function useDownloadDocument<TData = Awaited<ReturnType<typeof downloadDocument>>, TError = HTTPValidationError>(
  documentId: string, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadDocument>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof downloadDocument>>,
        TError,
        Awaited<ReturnType<typeof downloadDocument>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useDownloadDocument<TData = Awaited<ReturnType<typeof downloadDocument>>, TError = HTTPValidationError>(
  documentId: string, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadDocument>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof downloadDocument>>,
        TError,
        Awaited<ReturnType<typeof downloadDocument>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useDownloadDocument<TData = Awaited<ReturnType<typeof downloadDocument>>, TError = HTTPValidationError>(
  documentId: string, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadDocument>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Download File
 */

export function useDownloadDocument<TData = Awaited<ReturnType<typeof downloadDocument>>, TError = HTTPValidationError>(
  documentId: string, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadDocument>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getDownloadDocumentQueryOptions(documentId, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * @summary Download Snapshot
 */
export const downloadSnapshot = (
  snapshotId: string,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/snapshot/download/${snapshotId}`, method: 'GET', signal
    },
  );
}


export const getDownloadSnapshotQueryKey = (snapshotId: string,) => {
  return [`/snapshot/download/${snapshotId}`] as const;
}


export const getDownloadSnapshotQueryOptions = <TData = Awaited<ReturnType<typeof downloadSnapshot>>, TError = HTTPValidationError>(snapshotId: string, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadSnapshot>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getDownloadSnapshotQueryKey(snapshotId);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof downloadSnapshot>>> = ({ signal }) => downloadSnapshot(snapshotId, signal);





  return { queryKey, queryFn, enabled: !!(snapshotId), ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof downloadSnapshot>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type DownloadSnapshotQueryResult = NonNullable<Awaited<ReturnType<typeof downloadSnapshot>>>
export type DownloadSnapshotQueryError = HTTPValidationError


export function useDownloadSnapshot<TData = Awaited<ReturnType<typeof downloadSnapshot>>, TError = HTTPValidationError>(
  snapshotId: string, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadSnapshot>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof downloadSnapshot>>,
        TError,
        Awaited<ReturnType<typeof downloadSnapshot>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useDownloadSnapshot<TData = Awaited<ReturnType<typeof downloadSnapshot>>, TError = HTTPValidationError>(
  snapshotId: string, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadSnapshot>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof downloadSnapshot>>,
        TError,
        Awaited<ReturnType<typeof downloadSnapshot>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useDownloadSnapshot<TData = Awaited<ReturnType<typeof downloadSnapshot>>, TError = HTTPValidationError>(
  snapshotId: string, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadSnapshot>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Download Snapshot
 */

export function useDownloadSnapshot<TData = Awaited<ReturnType<typeof downloadSnapshot>>, TError = HTTPValidationError>(
  snapshotId: string, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadSnapshot>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getDownloadSnapshotQueryOptions(snapshotId, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * Download a file from a temporary link
 * @summary Download
 */
export const downloadFromTemporaryLink = (
  params: DownloadFromTemporaryLinkParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/download`, method: 'GET',
      params, signal
    },
  );
}


export const getDownloadFromTemporaryLinkQueryKey = (params: DownloadFromTemporaryLinkParams,) => {
  return [`/download`, ...(params ? [params] : [])] as const;
}


export const getDownloadFromTemporaryLinkQueryOptions = <TData = Awaited<ReturnType<typeof downloadFromTemporaryLink>>, TError = HTTPValidationError>(params: DownloadFromTemporaryLinkParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadFromTemporaryLink>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getDownloadFromTemporaryLinkQueryKey(params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof downloadFromTemporaryLink>>> = ({ signal }) => downloadFromTemporaryLink(params, signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof downloadFromTemporaryLink>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type DownloadFromTemporaryLinkQueryResult = NonNullable<Awaited<ReturnType<typeof downloadFromTemporaryLink>>>
export type DownloadFromTemporaryLinkQueryError = HTTPValidationError


export function useDownloadFromTemporaryLink<TData = Awaited<ReturnType<typeof downloadFromTemporaryLink>>, TError = HTTPValidationError>(
  params: DownloadFromTemporaryLinkParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadFromTemporaryLink>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof downloadFromTemporaryLink>>,
        TError,
        Awaited<ReturnType<typeof downloadFromTemporaryLink>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useDownloadFromTemporaryLink<TData = Awaited<ReturnType<typeof downloadFromTemporaryLink>>, TError = HTTPValidationError>(
  params: DownloadFromTemporaryLinkParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadFromTemporaryLink>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof downloadFromTemporaryLink>>,
        TError,
        Awaited<ReturnType<typeof downloadFromTemporaryLink>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useDownloadFromTemporaryLink<TData = Awaited<ReturnType<typeof downloadFromTemporaryLink>>, TError = HTTPValidationError>(
  params: DownloadFromTemporaryLinkParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadFromTemporaryLink>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Download
 */

export function useDownloadFromTemporaryLink<TData = Awaited<ReturnType<typeof downloadFromTemporaryLink>>, TError = HTTPValidationError>(
  params: DownloadFromTemporaryLinkParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadFromTemporaryLink>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getDownloadFromTemporaryLinkQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



