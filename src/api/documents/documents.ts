/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation,
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import type {
  BodyAttachCollectionToDocument,
  ChangeDocumentStatusParams,
  DeleteAllDocumentsParams,
  DeleteDocumentsWithGivenStatusParams,
  DownloadCollectionDocumentsParams,
  GetDocumentsParams,
  GetTokenStatisticsOfDocumentBody,
  HTTPValidationError
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * Download all the documents present in the given collection in form of a zip file.
 * @summary Download Collection Documents
 */
export const downloadCollectionDocuments = (
  params: DownloadCollectionDocumentsParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/documents/download_collection_documents`, method: 'GET',
      params, signal
    },
  );
}


export const getDownloadCollectionDocumentsQueryKey = (params: DownloadCollectionDocumentsParams,) => {
  return [`/documents/download_collection_documents`, ...(params ? [params] : [])] as const;
}


export const getDownloadCollectionDocumentsQueryOptions = <TData = Awaited<ReturnType<typeof downloadCollectionDocuments>>, TError = HTTPValidationError>(params: DownloadCollectionDocumentsParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadCollectionDocuments>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getDownloadCollectionDocumentsQueryKey(params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof downloadCollectionDocuments>>> = ({ signal }) => downloadCollectionDocuments(params, signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof downloadCollectionDocuments>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type DownloadCollectionDocumentsQueryResult = NonNullable<Awaited<ReturnType<typeof downloadCollectionDocuments>>>
export type DownloadCollectionDocumentsQueryError = HTTPValidationError


export function useDownloadCollectionDocuments<TData = Awaited<ReturnType<typeof downloadCollectionDocuments>>, TError = HTTPValidationError>(
  params: DownloadCollectionDocumentsParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadCollectionDocuments>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof downloadCollectionDocuments>>,
        TError,
        Awaited<ReturnType<typeof downloadCollectionDocuments>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useDownloadCollectionDocuments<TData = Awaited<ReturnType<typeof downloadCollectionDocuments>>, TError = HTTPValidationError>(
  params: DownloadCollectionDocumentsParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadCollectionDocuments>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof downloadCollectionDocuments>>,
        TError,
        Awaited<ReturnType<typeof downloadCollectionDocuments>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useDownloadCollectionDocuments<TData = Awaited<ReturnType<typeof downloadCollectionDocuments>>, TError = HTTPValidationError>(
  params: DownloadCollectionDocumentsParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadCollectionDocuments>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Download Collection Documents
 */

export function useDownloadCollectionDocuments<TData = Awaited<ReturnType<typeof downloadCollectionDocuments>>, TError = HTTPValidationError>(
  params: DownloadCollectionDocumentsParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadCollectionDocuments>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getDownloadCollectionDocumentsQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * @summary Change Documents Status
 */
export const changeDocumentStatus = (
  params: ChangeDocumentStatusParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/documents/change_document_status`, method: 'POST',
      params, signal
    },
  );
}



export const getChangeDocumentStatusMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof changeDocumentStatus>>, TError, { params: ChangeDocumentStatusParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof changeDocumentStatus>>, TError, { params: ChangeDocumentStatusParams }, TContext> => {

  const mutationKey = ['changeDocumentStatus'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof changeDocumentStatus>>, { params: ChangeDocumentStatusParams }> = (props) => {
    const { params } = props ?? {};

    return changeDocumentStatus(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ChangeDocumentStatusMutationResult = NonNullable<Awaited<ReturnType<typeof changeDocumentStatus>>>

export type ChangeDocumentStatusMutationError = HTTPValidationError

/**
* @summary Change Documents Status
*/
export const useChangeDocumentStatus = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof changeDocumentStatus>>, TError, { params: ChangeDocumentStatusParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof changeDocumentStatus>>,
      TError,
      { params: ChangeDocumentStatusParams },
      TContext
    > => {

  const mutationOptions = getChangeDocumentStatusMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Delete Documents With Given Status
*/
export const deleteDocumentsWithGivenStatus = (
  params: DeleteDocumentsWithGivenStatusParams,
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/documents/delete_documents_with_given_status`, method: 'DELETE',
      params
    },
  );
}



export const getDeleteDocumentsWithGivenStatusMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteDocumentsWithGivenStatus>>, TError, { params: DeleteDocumentsWithGivenStatusParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof deleteDocumentsWithGivenStatus>>, TError, { params: DeleteDocumentsWithGivenStatusParams }, TContext> => {

  const mutationKey = ['deleteDocumentsWithGivenStatus'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteDocumentsWithGivenStatus>>, { params: DeleteDocumentsWithGivenStatusParams }> = (props) => {
    const { params } = props ?? {};

    return deleteDocumentsWithGivenStatus(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type DeleteDocumentsWithGivenStatusMutationResult = NonNullable<Awaited<ReturnType<typeof deleteDocumentsWithGivenStatus>>>

export type DeleteDocumentsWithGivenStatusMutationError = HTTPValidationError

/**
* @summary Delete Documents With Given Status
*/
export const useDeleteDocumentsWithGivenStatus = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteDocumentsWithGivenStatus>>, TError, { params: DeleteDocumentsWithGivenStatusParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof deleteDocumentsWithGivenStatus>>,
      TError,
      { params: DeleteDocumentsWithGivenStatusParams },
      TContext
    > => {

  const mutationOptions = getDeleteDocumentsWithGivenStatusMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary List Documents
*/
export const getDocuments = (
  params?: GetDocumentsParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/documents`, method: 'GET',
      params, signal
    },
  );
}


export const getGetDocumentsQueryKey = (params?: GetDocumentsParams,) => {
  return [`/documents`, ...(params ? [params] : [])] as const;
}


export const getGetDocumentsQueryOptions = <TData = Awaited<ReturnType<typeof getDocuments>>, TError = HTTPValidationError>(params?: GetDocumentsParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocuments>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetDocumentsQueryKey(params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof getDocuments>>> = ({ signal }) => getDocuments(params, signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getDocuments>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type GetDocumentsQueryResult = NonNullable<Awaited<ReturnType<typeof getDocuments>>>
export type GetDocumentsQueryError = HTTPValidationError


export function useGetDocuments<TData = Awaited<ReturnType<typeof getDocuments>>, TError = HTTPValidationError>(
  params: undefined | GetDocumentsParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocuments>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getDocuments>>,
        TError,
        Awaited<ReturnType<typeof getDocuments>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetDocuments<TData = Awaited<ReturnType<typeof getDocuments>>, TError = HTTPValidationError>(
  params?: GetDocumentsParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocuments>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getDocuments>>,
        TError,
        Awaited<ReturnType<typeof getDocuments>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetDocuments<TData = Awaited<ReturnType<typeof getDocuments>>, TError = HTTPValidationError>(
  params?: GetDocumentsParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocuments>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary List Documents
 */

export function useGetDocuments<TData = Awaited<ReturnType<typeof getDocuments>>, TError = HTTPValidationError>(
  params?: GetDocumentsParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocuments>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getGetDocumentsQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * @summary Delete All Documents
 */
export const deleteAllDocuments = (
  params?: DeleteAllDocumentsParams,
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/documents`, method: 'DELETE',
      params
    },
  );
}



export const getDeleteAllDocumentsMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteAllDocuments>>, TError, { params?: DeleteAllDocumentsParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof deleteAllDocuments>>, TError, { params?: DeleteAllDocumentsParams }, TContext> => {

  const mutationKey = ['deleteAllDocuments'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteAllDocuments>>, { params?: DeleteAllDocumentsParams }> = (props) => {
    const { params } = props ?? {};

    return deleteAllDocuments(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type DeleteAllDocumentsMutationResult = NonNullable<Awaited<ReturnType<typeof deleteAllDocuments>>>

export type DeleteAllDocumentsMutationError = HTTPValidationError

/**
* @summary Delete All Documents
*/
export const useDeleteAllDocuments = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteAllDocuments>>, TError, { params?: DeleteAllDocumentsParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof deleteAllDocuments>>,
      TError,
      { params?: DeleteAllDocumentsParams },
      TContext
    > => {

  const mutationOptions = getDeleteAllDocumentsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Get Document
*/
export const getDocument = (
  documentId: string,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/documents/${documentId}`, method: 'GET', signal
    },
  );
}


export const getGetDocumentQueryKey = (documentId: string,) => {
  return [`/documents/${documentId}`] as const;
}


export const getGetDocumentQueryOptions = <TData = Awaited<ReturnType<typeof getDocument>>, TError = HTTPValidationError>(documentId: string, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetDocumentQueryKey(documentId);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof getDocument>>> = ({ signal }) => getDocument(documentId, signal);





  return { queryKey, queryFn, enabled: !!(documentId), ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type GetDocumentQueryResult = NonNullable<Awaited<ReturnType<typeof getDocument>>>
export type GetDocumentQueryError = HTTPValidationError


export function useGetDocument<TData = Awaited<ReturnType<typeof getDocument>>, TError = HTTPValidationError>(
  documentId: string, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getDocument>>,
        TError,
        Awaited<ReturnType<typeof getDocument>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetDocument<TData = Awaited<ReturnType<typeof getDocument>>, TError = HTTPValidationError>(
  documentId: string, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getDocument>>,
        TError,
        Awaited<ReturnType<typeof getDocument>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetDocument<TData = Awaited<ReturnType<typeof getDocument>>, TError = HTTPValidationError>(
  documentId: string, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Get Document
 */

export function useGetDocument<TData = Awaited<ReturnType<typeof getDocument>>, TError = HTTPValidationError>(
  documentId: string, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocument>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getGetDocumentQueryOptions(documentId, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * @summary Delete Document
 */
export const deleteDocument = (
  documentId: string,
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/documents/${documentId}`, method: 'DELETE'
    },
  );
}



export const getDeleteDocumentMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteDocument>>, TError, { documentId: string }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof deleteDocument>>, TError, { documentId: string }, TContext> => {

  const mutationKey = ['deleteDocument'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteDocument>>, { documentId: string }> = (props) => {
    const { documentId } = props ?? {};

    return deleteDocument(documentId,)
  }




  return { mutationFn, ...mutationOptions }
}

export type DeleteDocumentMutationResult = NonNullable<Awaited<ReturnType<typeof deleteDocument>>>

export type DeleteDocumentMutationError = HTTPValidationError

/**
* @summary Delete Document
*/
export const useDeleteDocument = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteDocument>>, TError, { documentId: string }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof deleteDocument>>,
      TError,
      { documentId: string },
      TContext
    > => {

  const mutationOptions = getDeleteDocumentMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Hard Reset Document
*/
export const resetDocumentDatabase = (

) => {


  return apiUrlMutator<unknown>(
    {
      url: `/documentsreset_document_database`, method: 'DELETE'
    },
  );
}



export const getResetDocumentDatabaseMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof resetDocumentDatabase>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof resetDocumentDatabase>>, TError, void, TContext> => {

  const mutationKey = ['resetDocumentDatabase'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof resetDocumentDatabase>>, void> = () => {


    return resetDocumentDatabase()
  }




  return { mutationFn, ...mutationOptions }
}

export type ResetDocumentDatabaseMutationResult = NonNullable<Awaited<ReturnType<typeof resetDocumentDatabase>>>

export type ResetDocumentDatabaseMutationError = unknown

/**
* @summary Hard Reset Document
*/
export const useResetDocumentDatabase = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof resetDocumentDatabase>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof resetDocumentDatabase>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getResetDocumentDatabaseMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Attach Collection To Document
*/
export const attachCollectionToDocument = (
  bodyAttachCollectionToDocument: BodyAttachCollectionToDocument,
  signal?: AbortSignal
) => {

  const formUrlEncoded = new URLSearchParams();
  formUrlEncoded.append(`collection_id`, bodyAttachCollectionToDocument.collection_id)
  formUrlEncoded.append(`document_id`, bodyAttachCollectionToDocument.document_id)

  return apiUrlMutator<unknown>(
    {
      url: `/documents/attach_collection_to_document`, method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded', },
      data: formUrlEncoded, signal
    },
  );
}



export const getAttachCollectionToDocumentMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof attachCollectionToDocument>>, TError, { data: BodyAttachCollectionToDocument }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof attachCollectionToDocument>>, TError, { data: BodyAttachCollectionToDocument }, TContext> => {

  const mutationKey = ['attachCollectionToDocument'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof attachCollectionToDocument>>, { data: BodyAttachCollectionToDocument }> = (props) => {
    const { data } = props ?? {};

    return attachCollectionToDocument(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AttachCollectionToDocumentMutationResult = NonNullable<Awaited<ReturnType<typeof attachCollectionToDocument>>>
export type AttachCollectionToDocumentMutationBody = BodyAttachCollectionToDocument
export type AttachCollectionToDocumentMutationError = HTTPValidationError

/**
* @summary Attach Collection To Document
*/
export const useAttachCollectionToDocument = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof attachCollectionToDocument>>, TError, { data: BodyAttachCollectionToDocument }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof attachCollectionToDocument>>,
      TError,
      { data: BodyAttachCollectionToDocument },
      TContext
    > => {

  const mutationOptions = getAttachCollectionToDocumentMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Get Token Statistics
*/
export const getTokenStatisticsOfDocument = (
  getTokenStatisticsOfDocumentBody: GetTokenStatisticsOfDocumentBody,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/documents/get_token_statistics_of_documents`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: getTokenStatisticsOfDocumentBody, signal
    },
  );
}



export const getGetTokenStatisticsOfDocumentMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof getTokenStatisticsOfDocument>>, TError, { data: GetTokenStatisticsOfDocumentBody }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof getTokenStatisticsOfDocument>>, TError, { data: GetTokenStatisticsOfDocumentBody }, TContext> => {

  const mutationKey = ['getTokenStatisticsOfDocument'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof getTokenStatisticsOfDocument>>, { data: GetTokenStatisticsOfDocumentBody }> = (props) => {
    const { data } = props ?? {};

    return getTokenStatisticsOfDocument(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type GetTokenStatisticsOfDocumentMutationResult = NonNullable<Awaited<ReturnType<typeof getTokenStatisticsOfDocument>>>
export type GetTokenStatisticsOfDocumentMutationBody = GetTokenStatisticsOfDocumentBody
export type GetTokenStatisticsOfDocumentMutationError = HTTPValidationError

/**
* @summary Get Token Statistics
*/
export const useGetTokenStatisticsOfDocument = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof getTokenStatisticsOfDocument>>, TError, { data: GetTokenStatisticsOfDocumentBody }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof getTokenStatisticsOfDocument>>,
      TError,
      { data: GetTokenStatisticsOfDocumentBody },
      TContext
    > => {

  const mutationOptions = getGetTokenStatisticsOfDocumentMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
