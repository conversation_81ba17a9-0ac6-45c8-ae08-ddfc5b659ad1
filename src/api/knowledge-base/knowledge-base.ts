/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * FastAPI
 * OpenAPI spec version: 0.1.0
 */
import {
  useMutation,
  useQuery
} from '@tanstack/react-query';
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult
} from '@tanstack/react-query';

import type {
  AddCollectionKnowledgeBaseCollectionsCreatePostParams,
  BodyAddCollectionKnowledgeBaseCollectionsCreatePost,
  BodyUploadKnowledgeBaseUploadPost,
  ChangeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPostParams,
  DeleteAllDocumentsKnowledgeBaseDocumentsDeleteParams,
  DownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetParams,
  GetDocumentKnowledgeBaseDocumentsDocumentIdGetParams,
  HTTPValidationError,
  ListDocumentsKnowledgeBaseDocumentsGetParams
} from '../fastAPI.schemas';

import { apiUrlMutator } from '../../config/mutator';




/**
 * @summary Initialize Databases
 */
export const initializeDatabasesKnowledgeBaseInitializeDatabasesPost = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/knowledge_base/initialize_databases`, method: 'POST', signal
    },
  );
}



export const getInitializeDatabasesKnowledgeBaseInitializeDatabasesPostMutationOptions = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof initializeDatabasesKnowledgeBaseInitializeDatabasesPost>>, TError, void, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof initializeDatabasesKnowledgeBaseInitializeDatabasesPost>>, TError, void, TContext> => {

  const mutationKey = ['initializeDatabasesKnowledgeBaseInitializeDatabasesPost'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof initializeDatabasesKnowledgeBaseInitializeDatabasesPost>>, void> = () => {


    return initializeDatabasesKnowledgeBaseInitializeDatabasesPost()
  }




  return { mutationFn, ...mutationOptions }
}

export type InitializeDatabasesKnowledgeBaseInitializeDatabasesPostMutationResult = NonNullable<Awaited<ReturnType<typeof initializeDatabasesKnowledgeBaseInitializeDatabasesPost>>>

export type InitializeDatabasesKnowledgeBaseInitializeDatabasesPostMutationError = unknown

/**
* @summary Initialize Databases
*/
export const useInitializeDatabasesKnowledgeBaseInitializeDatabasesPost = <TError = unknown,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof initializeDatabasesKnowledgeBaseInitializeDatabasesPost>>, TError, void, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof initializeDatabasesKnowledgeBaseInitializeDatabasesPost>>,
      TError,
      void,
      TContext
    > => {

  const mutationOptions = getInitializeDatabasesKnowledgeBaseInitializeDatabasesPostMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Upload
*/
export const uploadKnowledgeBaseUploadPost = (
  bodyUploadKnowledgeBaseUploadPost: BodyUploadKnowledgeBaseUploadPost,
  signal?: AbortSignal
) => {

  const formData = new FormData();
  bodyUploadKnowledgeBaseUploadPost.files.forEach(value => formData.append(`files`, value));

  return apiUrlMutator<unknown>(
    {
      url: `/knowledge_base/upload/`, method: 'POST',
      headers: { 'Content-Type': 'multipart/form-data', },
      data: formData, signal
    },
  );
}



export const getUploadKnowledgeBaseUploadPostMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof uploadKnowledgeBaseUploadPost>>, TError, { data: BodyUploadKnowledgeBaseUploadPost }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof uploadKnowledgeBaseUploadPost>>, TError, { data: BodyUploadKnowledgeBaseUploadPost }, TContext> => {

  const mutationKey = ['uploadKnowledgeBaseUploadPost'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof uploadKnowledgeBaseUploadPost>>, { data: BodyUploadKnowledgeBaseUploadPost }> = (props) => {
    const { data } = props ?? {};

    return uploadKnowledgeBaseUploadPost(data,)
  }




  return { mutationFn, ...mutationOptions }
}

export type UploadKnowledgeBaseUploadPostMutationResult = NonNullable<Awaited<ReturnType<typeof uploadKnowledgeBaseUploadPost>>>
export type UploadKnowledgeBaseUploadPostMutationBody = BodyUploadKnowledgeBaseUploadPost
export type UploadKnowledgeBaseUploadPostMutationError = HTTPValidationError

/**
* @summary Upload
*/
export const useUploadKnowledgeBaseUploadPost = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof uploadKnowledgeBaseUploadPost>>, TError, { data: BodyUploadKnowledgeBaseUploadPost }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof uploadKnowledgeBaseUploadPost>>,
      TError,
      { data: BodyUploadKnowledgeBaseUploadPost },
      TContext
    > => {

  const mutationOptions = getUploadKnowledgeBaseUploadPostMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary List Documents
*/
export const listDocumentsKnowledgeBaseDocumentsGet = (
  params?: ListDocumentsKnowledgeBaseDocumentsGetParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/knowledge_base/documents/`, method: 'GET',
      params, signal
    },
  );
}


export const getListDocumentsKnowledgeBaseDocumentsGetQueryKey = (params?: ListDocumentsKnowledgeBaseDocumentsGetParams,) => {
  return [`/knowledge_base/documents/`, ...(params ? [params] : [])] as const;
}


export const getListDocumentsKnowledgeBaseDocumentsGetQueryOptions = <TData = Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>, TError = HTTPValidationError>(params?: ListDocumentsKnowledgeBaseDocumentsGetParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getListDocumentsKnowledgeBaseDocumentsGetQueryKey(params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>> = ({ signal }) => listDocumentsKnowledgeBaseDocumentsGet(params, signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type ListDocumentsKnowledgeBaseDocumentsGetQueryResult = NonNullable<Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>>
export type ListDocumentsKnowledgeBaseDocumentsGetQueryError = HTTPValidationError


export function useListDocumentsKnowledgeBaseDocumentsGet<TData = Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>, TError = HTTPValidationError>(
  params: undefined | ListDocumentsKnowledgeBaseDocumentsGetParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>,
        TError,
        Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListDocumentsKnowledgeBaseDocumentsGet<TData = Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>, TError = HTTPValidationError>(
  params?: ListDocumentsKnowledgeBaseDocumentsGetParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>,
        TError,
        Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListDocumentsKnowledgeBaseDocumentsGet<TData = Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>, TError = HTTPValidationError>(
  params?: ListDocumentsKnowledgeBaseDocumentsGetParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary List Documents
 */

export function useListDocumentsKnowledgeBaseDocumentsGet<TData = Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>, TError = HTTPValidationError>(
  params?: ListDocumentsKnowledgeBaseDocumentsGetParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listDocumentsKnowledgeBaseDocumentsGet>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getListDocumentsKnowledgeBaseDocumentsGetQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * @summary Get Document
 */
export const getDocumentKnowledgeBaseDocumentsDocumentIdGet = (
  documentId: string,
  params?: GetDocumentKnowledgeBaseDocumentsDocumentIdGetParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/knowledge_base/documents/${documentId}`, method: 'GET',
      params, signal
    },
  );
}


export const getGetDocumentKnowledgeBaseDocumentsDocumentIdGetQueryKey = (documentId: string,
  params?: GetDocumentKnowledgeBaseDocumentsDocumentIdGetParams,) => {
  return [`/knowledge_base/documents/${documentId}`, ...(params ? [params] : [])] as const;
}


export const getGetDocumentKnowledgeBaseDocumentsDocumentIdGetQueryOptions = <TData = Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>, TError = HTTPValidationError>(documentId: string,
  params?: GetDocumentKnowledgeBaseDocumentsDocumentIdGetParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetDocumentKnowledgeBaseDocumentsDocumentIdGetQueryKey(documentId, params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>> = ({ signal }) => getDocumentKnowledgeBaseDocumentsDocumentIdGet(documentId, params, signal);





  return { queryKey, queryFn, enabled: !!(documentId), ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type GetDocumentKnowledgeBaseDocumentsDocumentIdGetQueryResult = NonNullable<Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>>
export type GetDocumentKnowledgeBaseDocumentsDocumentIdGetQueryError = HTTPValidationError


export function useGetDocumentKnowledgeBaseDocumentsDocumentIdGet<TData = Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>, TError = HTTPValidationError>(
  documentId: string,
  params: undefined | GetDocumentKnowledgeBaseDocumentsDocumentIdGetParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>,
        TError,
        Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetDocumentKnowledgeBaseDocumentsDocumentIdGet<TData = Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>, TError = HTTPValidationError>(
  documentId: string,
  params?: GetDocumentKnowledgeBaseDocumentsDocumentIdGetParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>,
        TError,
        Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useGetDocumentKnowledgeBaseDocumentsDocumentIdGet<TData = Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>, TError = HTTPValidationError>(
  documentId: string,
  params?: GetDocumentKnowledgeBaseDocumentsDocumentIdGetParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Get Document
 */

export function useGetDocumentKnowledgeBaseDocumentsDocumentIdGet<TData = Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>, TError = HTTPValidationError>(
  documentId: string,
  params?: GetDocumentKnowledgeBaseDocumentsDocumentIdGetParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getDocumentKnowledgeBaseDocumentsDocumentIdGet>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getGetDocumentKnowledgeBaseDocumentsDocumentIdGetQueryOptions(documentId, params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * @summary Delete Document
 */
export const deleteDocumentKnowledgeBaseDocumentsDocumentIdDelete = (
  documentId: string,
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/knowledge_base/documents/${documentId}`, method: 'DELETE'
    },
  );
}



export const getDeleteDocumentKnowledgeBaseDocumentsDocumentIdDeleteMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteDocumentKnowledgeBaseDocumentsDocumentIdDelete>>, TError, { documentId: string }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof deleteDocumentKnowledgeBaseDocumentsDocumentIdDelete>>, TError, { documentId: string }, TContext> => {

  const mutationKey = ['deleteDocumentKnowledgeBaseDocumentsDocumentIdDelete'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteDocumentKnowledgeBaseDocumentsDocumentIdDelete>>, { documentId: string }> = (props) => {
    const { documentId } = props ?? {};

    return deleteDocumentKnowledgeBaseDocumentsDocumentIdDelete(documentId,)
  }




  return { mutationFn, ...mutationOptions }
}

export type DeleteDocumentKnowledgeBaseDocumentsDocumentIdDeleteMutationResult = NonNullable<Awaited<ReturnType<typeof deleteDocumentKnowledgeBaseDocumentsDocumentIdDelete>>>

export type DeleteDocumentKnowledgeBaseDocumentsDocumentIdDeleteMutationError = HTTPValidationError

/**
* @summary Delete Document
*/
export const useDeleteDocumentKnowledgeBaseDocumentsDocumentIdDelete = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteDocumentKnowledgeBaseDocumentsDocumentIdDelete>>, TError, { documentId: string }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof deleteDocumentKnowledgeBaseDocumentsDocumentIdDelete>>,
      TError,
      { documentId: string },
      TContext
    > => {

  const mutationOptions = getDeleteDocumentKnowledgeBaseDocumentsDocumentIdDeleteMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* Download all the documents present in the given collection in form of a zip file.
* @summary Download Collection Documents
*/
export const downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet = (
  params: DownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/knowledge_base/documents/download_collection_documents`, method: 'GET',
      params, signal
    },
  );
}


export const getDownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetQueryKey = (params: DownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetParams,) => {
  return [`/knowledge_base/documents/download_collection_documents`, ...(params ? [params] : [])] as const;
}


export const getDownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetQueryOptions = <TData = Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>, TError = HTTPValidationError>(params: DownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getDownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetQueryKey(params);



  const queryFn: QueryFunction<Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>> = ({ signal }) => downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet(params, signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type DownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetQueryResult = NonNullable<Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>>
export type DownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetQueryError = HTTPValidationError


export function useDownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet<TData = Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>, TError = HTTPValidationError>(
  params: DownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetParams, options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>,
        TError,
        Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useDownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet<TData = Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>, TError = HTTPValidationError>(
  params: DownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetParams, options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>,
        TError,
        Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useDownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet<TData = Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>, TError = HTTPValidationError>(
  params: DownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary Download Collection Documents
 */

export function useDownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet<TData = Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>, TError = HTTPValidationError>(
  params: DownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetParams, options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof downloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGet>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getDownloadCollectionDocumentsKnowledgeBaseDocumentsDownloadCollectionDocumentsGetQueryOptions(params, options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * @summary Change Documents Status
 */
export const changeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPost = (
  params: ChangeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPostParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/knowledge_base/documents/change_document_status`, method: 'POST',
      params, signal
    },
  );
}



export const getChangeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPostMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof changeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPost>>, TError, { params: ChangeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPostParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof changeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPost>>, TError, { params: ChangeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPostParams }, TContext> => {

  const mutationKey = ['changeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPost'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof changeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPost>>, { params: ChangeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPostParams }> = (props) => {
    const { params } = props ?? {};

    return changeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPost(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type ChangeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPostMutationResult = NonNullable<Awaited<ReturnType<typeof changeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPost>>>

export type ChangeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPostMutationError = HTTPValidationError

/**
* @summary Change Documents Status
*/
export const useChangeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPost = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof changeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPost>>, TError, { params: ChangeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPostParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof changeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPost>>,
      TError,
      { params: ChangeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPostParams },
      TContext
    > => {

  const mutationOptions = getChangeDocumentsStatusKnowledgeBaseDocumentsChangeDocumentStatusPostMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Delete All Documents
*/
export const deleteAllDocumentsKnowledgeBaseDocumentsDelete = (
  params?: DeleteAllDocumentsKnowledgeBaseDocumentsDeleteParams,
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/knowledge_base/documents`, method: 'DELETE',
      params
    },
  );
}



export const getDeleteAllDocumentsKnowledgeBaseDocumentsDeleteMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteAllDocumentsKnowledgeBaseDocumentsDelete>>, TError, { params?: DeleteAllDocumentsKnowledgeBaseDocumentsDeleteParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof deleteAllDocumentsKnowledgeBaseDocumentsDelete>>, TError, { params?: DeleteAllDocumentsKnowledgeBaseDocumentsDeleteParams }, TContext> => {

  const mutationKey = ['deleteAllDocumentsKnowledgeBaseDocumentsDelete'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteAllDocumentsKnowledgeBaseDocumentsDelete>>, { params?: DeleteAllDocumentsKnowledgeBaseDocumentsDeleteParams }> = (props) => {
    const { params } = props ?? {};

    return deleteAllDocumentsKnowledgeBaseDocumentsDelete(params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type DeleteAllDocumentsKnowledgeBaseDocumentsDeleteMutationResult = NonNullable<Awaited<ReturnType<typeof deleteAllDocumentsKnowledgeBaseDocumentsDelete>>>

export type DeleteAllDocumentsKnowledgeBaseDocumentsDeleteMutationError = HTTPValidationError

/**
* @summary Delete All Documents
*/
export const useDeleteAllDocumentsKnowledgeBaseDocumentsDelete = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteAllDocumentsKnowledgeBaseDocumentsDelete>>, TError, { params?: DeleteAllDocumentsKnowledgeBaseDocumentsDeleteParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof deleteAllDocumentsKnowledgeBaseDocumentsDelete>>,
      TError,
      { params?: DeleteAllDocumentsKnowledgeBaseDocumentsDeleteParams },
      TContext
    > => {

  const mutationOptions = getDeleteAllDocumentsKnowledgeBaseDocumentsDeleteMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary List Collections
*/
export const listCollectionsKnowledgeBaseCollectionsGet = (

  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/knowledge_base/collections/`, method: 'GET', signal
    },
  );
}


export const getListCollectionsKnowledgeBaseCollectionsGetQueryKey = () => {
  return [`/knowledge_base/collections/`] as const;
}


export const getListCollectionsKnowledgeBaseCollectionsGetQueryOptions = <TData = Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>, TError = unknown>(options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>, TError, TData>>, }
) => {

  const { query: queryOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getListCollectionsKnowledgeBaseCollectionsGetQueryKey();



  const queryFn: QueryFunction<Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>> = ({ signal }) => listCollectionsKnowledgeBaseCollectionsGet(signal);





  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>, TError, TData> & { queryKey: DataTag<QueryKey, TData> }
}

export type ListCollectionsKnowledgeBaseCollectionsGetQueryResult = NonNullable<Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>>
export type ListCollectionsKnowledgeBaseCollectionsGetQueryError = unknown


export function useListCollectionsKnowledgeBaseCollectionsGet<TData = Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>, TError = unknown>(
  options: {
    query: Partial<UseQueryOptions<Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>, TError, TData>> & Pick<
      DefinedInitialDataOptions<
        Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>,
        TError,
        Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): DefinedUseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListCollectionsKnowledgeBaseCollectionsGet<TData = Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>, TError = unknown>(
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>, TError, TData>> & Pick<
      UndefinedInitialDataOptions<
        Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>,
        TError,
        Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>
      >, 'initialData'
    >,
  }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
export function useListCollectionsKnowledgeBaseCollectionsGet<TData = Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> }
/**
 * @summary List Collections
 */

export function useListCollectionsKnowledgeBaseCollectionsGet<TData = Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>, TError = unknown>(
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof listCollectionsKnowledgeBaseCollectionsGet>>, TError, TData>>, }
  , queryClient?: QueryClient
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> } {

  const queryOptions = getListCollectionsKnowledgeBaseCollectionsGetQueryOptions(options)

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData> };

  query.queryKey = queryOptions.queryKey;

  return query;
}



/**
 * @summary Add Collection
 */
export const addCollectionKnowledgeBaseCollectionsCreatePost = (
  bodyAddCollectionKnowledgeBaseCollectionsCreatePost: BodyAddCollectionKnowledgeBaseCollectionsCreatePost,
  params?: AddCollectionKnowledgeBaseCollectionsCreatePostParams,
  signal?: AbortSignal
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/knowledge_base/collections/create`, method: 'POST',
      headers: { 'Content-Type': 'application/json', },
      data: bodyAddCollectionKnowledgeBaseCollectionsCreatePost,
      params, signal
    },
  );
}



export const getAddCollectionKnowledgeBaseCollectionsCreatePostMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof addCollectionKnowledgeBaseCollectionsCreatePost>>, TError, { data: BodyAddCollectionKnowledgeBaseCollectionsCreatePost; params?: AddCollectionKnowledgeBaseCollectionsCreatePostParams }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof addCollectionKnowledgeBaseCollectionsCreatePost>>, TError, { data: BodyAddCollectionKnowledgeBaseCollectionsCreatePost; params?: AddCollectionKnowledgeBaseCollectionsCreatePostParams }, TContext> => {

  const mutationKey = ['addCollectionKnowledgeBaseCollectionsCreatePost'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof addCollectionKnowledgeBaseCollectionsCreatePost>>, { data: BodyAddCollectionKnowledgeBaseCollectionsCreatePost; params?: AddCollectionKnowledgeBaseCollectionsCreatePostParams }> = (props) => {
    const { data, params } = props ?? {};

    return addCollectionKnowledgeBaseCollectionsCreatePost(data, params,)
  }




  return { mutationFn, ...mutationOptions }
}

export type AddCollectionKnowledgeBaseCollectionsCreatePostMutationResult = NonNullable<Awaited<ReturnType<typeof addCollectionKnowledgeBaseCollectionsCreatePost>>>
export type AddCollectionKnowledgeBaseCollectionsCreatePostMutationBody = BodyAddCollectionKnowledgeBaseCollectionsCreatePost
export type AddCollectionKnowledgeBaseCollectionsCreatePostMutationError = HTTPValidationError

/**
* @summary Add Collection
*/
export const useAddCollectionKnowledgeBaseCollectionsCreatePost = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof addCollectionKnowledgeBaseCollectionsCreatePost>>, TError, { data: BodyAddCollectionKnowledgeBaseCollectionsCreatePost; params?: AddCollectionKnowledgeBaseCollectionsCreatePostParams }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof addCollectionKnowledgeBaseCollectionsCreatePost>>,
      TError,
      { data: BodyAddCollectionKnowledgeBaseCollectionsCreatePost; params?: AddCollectionKnowledgeBaseCollectionsCreatePostParams },
      TContext
    > => {

  const mutationOptions = getAddCollectionKnowledgeBaseCollectionsCreatePostMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
/**
* @summary Delete Collection
*/
export const deleteCollectionKnowledgeBaseCollectionsCollectionIdDelete = (
  collectionId: string,
) => {


  return apiUrlMutator<unknown>(
    {
      url: `/knowledge_base/collections/${collectionId}`, method: 'DELETE'
    },
  );
}



export const getDeleteCollectionKnowledgeBaseCollectionsCollectionIdDeleteMutationOptions = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteCollectionKnowledgeBaseCollectionsCollectionIdDelete>>, TError, { collectionId: string }, TContext>, }
  ): UseMutationOptions<Awaited<ReturnType<typeof deleteCollectionKnowledgeBaseCollectionsCollectionIdDelete>>, TError, { collectionId: string }, TContext> => {

  const mutationKey = ['deleteCollectionKnowledgeBaseCollectionsCollectionIdDelete'];
  const { mutation: mutationOptions } = options ?
    options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey ?
      options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey, } };




  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteCollectionKnowledgeBaseCollectionsCollectionIdDelete>>, { collectionId: string }> = (props) => {
    const { collectionId } = props ?? {};

    return deleteCollectionKnowledgeBaseCollectionsCollectionIdDelete(collectionId,)
  }




  return { mutationFn, ...mutationOptions }
}

export type DeleteCollectionKnowledgeBaseCollectionsCollectionIdDeleteMutationResult = NonNullable<Awaited<ReturnType<typeof deleteCollectionKnowledgeBaseCollectionsCollectionIdDelete>>>

export type DeleteCollectionKnowledgeBaseCollectionsCollectionIdDeleteMutationError = HTTPValidationError

/**
* @summary Delete Collection
*/
export const useDeleteCollectionKnowledgeBaseCollectionsCollectionIdDelete = <TError = HTTPValidationError,
  TContext = unknown>(options?: { mutation?: UseMutationOptions<Awaited<ReturnType<typeof deleteCollectionKnowledgeBaseCollectionsCollectionIdDelete>>, TError, { collectionId: string }, TContext>, }
    , queryClient?: QueryClient): UseMutationResult<
      Awaited<ReturnType<typeof deleteCollectionKnowledgeBaseCollectionsCollectionIdDelete>>,
      TError,
      { collectionId: string },
      TContext
    > => {

  const mutationOptions = getDeleteCollectionKnowledgeBaseCollectionsCollectionIdDeleteMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
}
