/**
 * This module handles environment variable validation and provides type-safe access to configuration.
 * It validates required environment variables at build time to fail fast if any are missing.
 */

// Build-time environment variables (available during next build)
const buildTimeEnvs = {
    NODE_ENV: process.env.NODE_ENV,
    GOOGLE_KEY: process.env.GOOGLE_KEY || null,
    GOOGLE_SECRET: process.env.GOOGLE_SECRET || null,
} as const;

// Runtime environment variables (available in the browser)
const runtimeEnvs = {
    NEXT_PUBLIC_APP_API_URL: process.env.NEXT_PUBLIC_APP_API_URL,
    NEXT_PUBLIC_AUTH_API_URL: process.env.NEXT_PUBLIC_AUTH_API_URL,
} as const;

/**
 * Validates that all required build-time environment variables are present.
 * This function is called during the build process.
 */
export function validateBuildTimeEnv() {
    const requiredBuildTimeEnvs: (keyof typeof buildTimeEnvs)[] = [
        'NODE_ENV',
    ];

    // Optional build-time envs
    const optionalPairs = [
        ['GOOGLE_KEY', 'GOOGLE_SECRET'],
    ] as const;

    // Validate build-time envs
    for (const env of requiredBuildTimeEnvs) {
        if (!buildTimeEnvs[env]) {
            throw new Error(`Missing required build-time environment variable: ${env}`);
        }
    }

    // Validate optional pairs - if one is present, both must be present
    for (const [key1, key2] of optionalPairs) {
        if (buildTimeEnvs[key1] && !buildTimeEnvs[key2]) {
            throw new Error(`When ${key1} is provided, ${key2} must also be provided`);
        }
        if (!buildTimeEnvs[key1] && buildTimeEnvs[key2]) {
            throw new Error(`When ${key2} is provided, ${key1} must also be provided`);
        }
    }
}

/**
 * Validates that all required runtime environment variables are present.
 * This function should be called during runtime, e.g., in your app initialization.
 */
export function validateRuntimeEnv() {
    const requiredRuntimeEnvs: (keyof typeof runtimeEnvs)[] = [
        'NEXT_PUBLIC_APP_API_URL',
        'NEXT_PUBLIC_AUTH_API_URL',
    ];

    // Validate runtime envs
    for (const env of requiredRuntimeEnvs) {
        if (!runtimeEnvs[env]) {
            throw new Error(`Missing required runtime environment variable: ${env}`);
        }
    }
}

// Run build-time validation immediately during build
validateBuildTimeEnv();

/**
 * Type-safe configuration object that combines both build-time and runtime environment variables.
 * This ensures we can't accidentally use build-time variables at runtime.
 */
export const config = {
    // Build time configuration
    isDevelopment: buildTimeEnvs.NODE_ENV === 'development',
    isProduction: buildTimeEnvs.NODE_ENV === 'production',
    isTest: buildTimeEnvs.NODE_ENV === 'test',

    // OAuth configuration
    oauth: {
        google: {
            enabled: Boolean(buildTimeEnvs.GOOGLE_KEY && buildTimeEnvs.GOOGLE_SECRET),
            clientId: buildTimeEnvs.GOOGLE_KEY,
            clientSecret: buildTimeEnvs.GOOGLE_SECRET,
        },
    },

    // Runtime configuration
    api: {
        appUrl: runtimeEnvs.NEXT_PUBLIC_APP_API_URL!,
        authUrl: runtimeEnvs.NEXT_PUBLIC_AUTH_API_URL!,
    },
} as const;

// Type for the config object
export type Config = typeof config;
