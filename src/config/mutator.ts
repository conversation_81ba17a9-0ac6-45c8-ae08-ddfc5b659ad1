import axios from 'axios';

const axiosInstance = axios.create({
  baseURL: `${process.env.NEXT_PUBLIC_APP_API_URL}/api/v1`,
});

type MutatorConfig = {
  url: string;
  method: 'get' | 'post' | 'put' | 'delete' | string;
  data?: unknown;
  params?: Record<string, any>;
  signal?: AbortSignal;
  config?: {
    headers?: Record<string, any>;
    [key: string]: any;
  };
};

export const apiUrlMutator = async ({
  url,
  method,
  data,
  params,
  signal,
  config = {},
}: MutatorConfig) => {
  return axiosInstance.request({
    url,
    method,
    data,
    params,
    signal,
    ...config,
  });
};