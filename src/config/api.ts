import { config } from './env';

const API_PREFIX = 'api/v1';

const joinUrl = (...parts: string[]) => 
    parts.map(part => part.replace(/^\/+|\/+$/g, '')).join('/');

export const apiConfig = {
    baseUrl: config.api.appUrl,
    authUrl: config.api.authUrl,
    getApiUrl: (endpoint: string) => joinUrl(apiConfig.baseUrl, API_PREFIX, endpoint),
    getAuthUrl: (endpoint: string) => joinUrl(apiConfig.authUrl, endpoint)
};
