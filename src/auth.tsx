import { Provider } from "next-auth/providers/index";
import { SignInCommand, getMeServer } from "./fetch/api/Auth";
import { jwtDecode, JwtPayload } from "jwt-decode";
import { cookies } from "next/headers";
import { CookieName } from "./enums/CookieName";
import { User } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { config } from "./config/env";

type AppJwtPayload = { user_id: string } & JwtPayload;
type AppUser = { access: string; refresh: string } & User;

export const providers: Provider[] = [
    CredentialsProvider({
        id: "credentials",
        name: "Credentials",
        credentials: {
            username: { label: "Username", type: "text", placeholder: "jsmith" },
            password: { label: "Password", type: "password" },
        },
        async authorize(credentials) {
            const crd = { ...credentials } as { username: string; password: string; access_token: string; refresh_token: string };
            if (!crd) {
                return null;
            }
            if (crd.password && crd.username) {
                try {
                    const { access, refresh } = await SignInCommand(crd);
                    const { user_id } = jwtDecode<AppJwtPayload>(access);

                    const cookise = await cookies();

                    cookise.set(CookieName.API_TOKEN, access);
                    cookise.set(CookieName.API_REFRESH_TOKEN, refresh, { httpOnly: true });

                    // Get real user data using getMeServer
                    const userData = await getMeServer(access);

                    return {
                        id: user_id,
                        name: userData.first_name + ' ' + userData.last_name,
                        email: userData.email,
                        image: userData.image || "",
                        access,
                        refresh,
                    } as AppUser;
                } catch (error) {
                    console.error("Error signing in:", error);
                    return null;
                }
            }
            if (crd?.access_token && crd?.refresh_token) {
                // Validate the token (optional)
                const decoded = validateToken(crd.access_token);
                if (!decoded) {
                    return null;
                }
                const cookise = await cookies();

                cookise.set(CookieName.API_TOKEN, crd.access_token);
                cookise.set(CookieName.API_REFRESH_TOKEN, crd.refresh_token, { httpOnly: true });

                // Get real user data using getMeServer
                const userData = await getMeServer(crd.access_token);

                // Return user data to be stored in the session
                return {
                    id: decoded.user_id,
                    name: userData.first_name + ' ' + userData.last_name,
                    email: userData.email,
                    image: userData.image || "",
                    access: crd?.access_token,
                    refresh: crd?.refresh_token,
                } as AppUser;
            }

            return null;
        },
    }),
    // Only include Google provider if credentials are available
    ...(config.oauth.google.enabled ? [{
        id: "customProvider",
        name: "Google Authorization",
        authorization: `${config.api.authUrl}/external/google/dev/`,
        token: `${config.api.authUrl}/csrf-token/`,
        redirectUrl: "",
        authorizationUrl: "",
        type: "oauth",
        scope: "", // Make sure to request the users email address
        clientId: config.oauth.google.clientId!,
        clientSecret: config.oauth.google.clientSecret!,
        /* eslint-disable  @typescript-eslint/no-explicit-any */
    } as any] : []),
];

const validateToken = (token: string) => {
    try {
        const decoded = jwtDecode<AppJwtPayload>(token);
        return decoded;
    } catch (error) {
        console.error("Token validation failed:", error);
        return null;
    }
};
