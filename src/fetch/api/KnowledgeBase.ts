import "client-only";
import cookies from "js-cookie";
import { ApiRequest } from "../FetchRequest";
import { <PERSON>ieName } from "@/enums/CookieName";
import { captureError } from "@/utils/errorHandling";
import { config } from '@/config/env';

export type KnowledgeBaseDocument = Readonly<{
    id: string;
    name: string;
    source: string;
    document_type: string;
    created_at: string;
    updated_at: string;
    status: string;
    collections: string[];
    processor_configs: {
        default: {
            extractor_config: {
                extractor_hierarchy: string[];
                apply_ocr: boolean;
                add_document_title_to_chunks: boolean;
                extract_annotations: boolean;
                structure_depth: number;
            };
            splitter_config: {
                splitter_model: string;
                chunk_size: number;
                chunk_overlap: number;
                min_cluster_characters: number;
                max_cluster_characters: number;
            };
        };
    };
    chunks: string[];
    keywords: string[];
    summary: object;
    source_file: {
        id: string;
        name: string;
        f_bytes: string;
    };
}>;
export type KnowledgeBaseDocuments = Readonly<{
    documents: KnowledgeBaseDocument[];
}>;

export interface EmbeddingCollection {
    name: string;
}

export async function LoadKnowlageBase() {
    try {
        const bearer = cookies.get(CookieName.API_TOKEN);
        console.log('Fetching knowledge base documents...');
        const { data, response } = await ApiRequest<KnowledgeBaseDocuments>(
            "documents",
            { bearer, init: { method: "GET" } }
        );

        if (response?.ok && data) {
            console.log('Knowledge base documents loaded:', {
                totalDocuments: data.documents.length,
                documentsWithCollections: data.documents.filter(doc => doc.collections?.length > 0).length,
                collections: [...new Set(data.documents.flatMap(doc => doc.collections))],
                documents: data.documents.map(doc => ({
                    id: doc.id,
                    name: doc.name,
                    collections: doc.collections,
                    status: doc.status
                }))
            });
            return data;
        }

        throw new Error("Failed to load knowledge base documents");
    } catch (error) {
        captureError(error, {
            action: 'LoadKnowlageBase',
            component: 'KnowledgeBaseAPI'
        });
        throw error;
    }
}

export async function DeleteDocument(documentId: string) {
    try {
        const token = cookies.get(CookieName.API_TOKEN);
        const { response } = await ApiRequest(`documents/${documentId}`, {
            init: {
                method: 'DELETE',
            },
            bearer: token,
        });

        if (!response?.ok) {
            throw new Error('Failed to delete document');
        }

        return true;
    } catch (error) {
        captureError(error, {
            action: 'DeleteDocument',
            documentId,
            component: 'KnowledgeBaseAPI'
        });
        throw error;
    }
}

export function getDownloadFileUrl(documentId: string) {
    return `${config.api.appUrl}/download_file/${documentId}`;
}

export async function CreateCollection(name: string) {
    try {
        const token = cookies.get(CookieName.API_TOKEN);
        const { response } = await ApiRequest(`embedding/add_collection?collection_name=${encodeURIComponent(name)}`, {
            init: {
                method: 'POST',
                body: JSON.stringify({ }),
            },
            bearer: token,
        });

        if (!response?.ok) {
            throw new Error('Failed to create collection');
        }

        return true;
    } catch (error) {
        captureError(error, {
            action: 'CreateCollection',
            name,
            component: 'KnowledgeBaseAPI'
        });
        throw error;
    }
}

export async function DeleteCollection(collection_id: string) {
    try {
        const token = cookies.get(CookieName.API_TOKEN);
        const { response } = await ApiRequest(`embedding/collection/${encodeURIComponent(collection_id)}`, {
            init: {
                method: 'DELETE',
            },
            bearer: token,
        });

        if (!response?.ok) {
            throw new Error('Failed to delete collection');
        }

        return true;
    } catch (error) {
        captureError(error, {
            action: 'DeleteCollection',
            collection_id,
            component: 'KnowledgeBaseAPI'
        });
        throw error;
    }
}

export async function downloadCollectionDocuments(collectionId: string) {
    try {
        const token = cookies.get(CookieName.API_TOKEN);
        
        // Create URL exactly matching Swagger format 
        const baseUrl = process.env.NEXT_PUBLIC_APP_API_URL || '';
        // Make sure to include /api/v1/ in the path
        const url = `${baseUrl}/api/v1/documents/download_collection_documents?collection_id=${encodeURIComponent(collectionId)}`;
        
        console.log("Downloading from:", url);
        console.log("Using token (first 10 chars):", token?.substring(0, 10) + "...");
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json, application/zip' // Try adding this
            }
        });
        
        console.log("Response status:", response.status);
        console.log("Response headers:", Object.fromEntries([...response.headers]));
        
        // Check content length
        const contentLength = response.headers.get('content-length');
        if (contentLength && parseInt(contentLength) < 100) {
            console.warn("Warning: Content length is very small:", contentLength);
        }
        
        if (!response.ok) {
            throw new Error(`Failed to download: ${response.status}`);
        }
        
        return await response.blob();
    } catch (error) {
        console.error("Download error details:", error);
        throw error;
    }
};

export { listEmbeddingVectorCollections } from "./Chat";
