import "client-only";
import cookies from "js-cookie";
import { ApiRequest } from "../FetchRequest";
import { CookieName } from "@/enums/CookieName";
import { CONFIGURE_STORAGE_KEY, configureDefaults } from "@/modules/Chat/components/Knowledge";
import { captureError } from "@/utils/errorHandling";

export type AskConfigParams = Readonly<{
    rag_config: {
        temperature: number; // >= 0
        llm: llm;
        top_k: number; // >= 1
        seed: number | null;
    };
    additional_context: string;
    alpha: number; // 1
    queryParams: {
        collection_id: string;
    };
}>;

export type llm = string;

export enum ChatRoles {
    SYSTEM = "system",
    USER = "user",
    CHATBOT = "chatbot"
}

export enum MessageKind {
    TEXT = "text",
    IMAGE = "image"
}

export interface ChatMessage {
    kind: MessageKind;
    content: string;  // text or encoded image
}

export interface ChatEntries {
    messages: ChatEntry[];
}

export interface ChatEntry {
    role: ChatRoles;
    message: ChatMessage;
}

export interface ChatMessages {
    messages: ChatEntry[];
}

type ListEmbeddingsAndLlmsResponse = Readonly<{
    embeddings: string[];
    llms: llm[];
}>;

export type SendMessagePayload = Readonly<{
    query: string;
    chat_messages?: ChatMessages;
    collection_id: string;
}> &
    AskConfigParams;

export type DocumentEntryDetails = { 
    id: string | null;
    chunk_id: string;
    chunk: string;
    collection: string | null;
    document_metadata: { 
        document_name: string; 
        source: string;
    };
};

export type SendMessageResponse = Readonly<{
    result: string;
    query: string;
    action_id: string;
    relevant_documents: {
        collection_retriever_entries: DocumentEntryDetails[];
    };
}>;

type EmbeddingManagerConfig = Readonly<{
    vector_database_kind: string;
    embedder_config: {
        embedding: string;
    };
}>;

export interface EmbeddingVector {
    id: string;
    name: string;
    created_at: string | null;
    updated_at: string;
    anonymization_mapping: Record<string, unknown>;
    embedding_manager_config: EmbeddingManagerConfig;
}

export async function askMessage(payload: SendMessagePayload) {
    try {
        const bearer = cookies.get(CookieName.API_TOKEN);
        const params = payload.queryParams.collection_id ? new URLSearchParams({ collection_id: payload.queryParams.collection_id }) : new URLSearchParams({});
        // const params = new URLSearchParams({ collection_id: "Onward_documents" })
        
        console.log('API Call Details:', {
            endpoint: 'qa/ask_old',
            method: 'POST',
            params: params?.toString(),
            body: JSON.stringify(payload, null, 2)
        });
        
        const { data, response } = await ApiRequest<SendMessageResponse>("qa/ask_old", { bearer, init: { method: "POST", body: JSON.stringify(payload) }, params });

        console.log('API Response Details:', {
            status: response?.status,
            statusText: response?.statusText,
            ok: response?.ok,
            data: data,
            error: response && !response.ok ? await response?.text() : null
        });

        if (!data) {
            throw new Error('No data received from the server');
        }

        if (response?.ok && data) {
            return data;
        }

        throw new Error(`Request failed: ${response?.statusText || 'Unknown error'}`);
    } catch (error) {
        captureError(error, {
            action: 'askMessage',
            component: 'ChatAPI'
        });
        console.error('API Error:', error);
        throw error;
    }
}

export async function filesUpload(files: File[]) {
    try {
        const body = new FormData();
        files.forEach((file) => body.append("files", file));

        // Get collection_id from localStorage
        const currentConfig = JSON.parse(localStorage.getItem(CONFIGURE_STORAGE_KEY) || JSON.stringify(configureDefaults));
        const collection_id = currentConfig.queryParams.collection_id;
        
        const bearer = cookies.get(CookieName.API_TOKEN);
        const params = collection_id ? new URLSearchParams({ collection_id }) : new URLSearchParams({});
        
        console.log('File Upload API Call Details:', {
            endpoint: 'upload/upload_and_process',
            method: 'POST',
            params: params.toString(),
            files: files.map(f => ({ name: f.name, size: f.size, type: f.type })),
            collection_id,
            currentConfig
        });

        const { data, response } = await ApiRequest<SendMessageResponse>("upload/upload_and_process", {
            contentType: null,
            bearer,
            init: {
                method: "POST",
                body,
            },
            params
        });

        console.log('File Upload API Response:', {
            status: response?.status,
            statusText: response?.statusText,
            ok: response?.ok,
            data: data,
            error: response && !response.ok ? await response?.text() : null
        });


        if (response?.ok && response.status === 200) {
            return data;
        }

        throw new Error("Unhandled state");
    } catch (error) {
        captureError(error, {
            action: 'filesUpload',
            component: 'ChatAPI'
        });
        throw error;
    }
}

export async function listEmbeddingsAndLlms() {
    try {
        const bearer = cookies.get(CookieName.API_TOKEN);
        const { data, response } = await ApiRequest<ListEmbeddingsAndLlmsResponse>("config/list_embeddings_and_llms", { bearer, init: { method: "GET" } });

        console.log('API Response Details:', {
            status: response?.status,
            statusText: response?.statusText,
            ok: response?.ok,
            data: data,
            error: response && !response.ok ? await response?.text() : null
        });

        if (!data) {
            throw new Error('No data received from the server');
        }

        if (response?.ok && data) {
            return data;
        }

        throw new Error("Unhandled state");
    } catch (error) {
        captureError(error, {
            action: 'listEmbeddingsAndLlms',
            component: 'ChatAPI'
        });
        throw error;
    }
}

export async function listEmbeddingVectorCollections() {
    try {
        const bearer = cookies.get(CookieName.API_TOKEN);
        console.log('Fetching embedding vector collections...');
        const { data, response } = await ApiRequest<EmbeddingVector[]>("embedding/vector_collections", { bearer, init: { method: "GET" } });

        console.log('API Response Details:', {
            status: response?.status,
            statusText: response?.statusText,
            ok: response?.ok,
            data: data,
            error: response && !response.ok ? await response?.text() : null
        });

        if (!data) {
            throw new Error('No data received from the server');
        }

        if (response?.ok && data) {
            console.log('Embedding collections loaded:', {
                totalCollections: data.length,
                collections: data.map(col => ({
                    id: col.id,
                    name: col.name,
                    created_at: col.created_at,
                    updated_at: col.updated_at,
                    config: col.embedding_manager_config
                }))
            });
            return data;
        }

        console.error('Failed to load embedding collections:', { status: response?.status, ok: response?.ok });
        throw new Error("Unhandled state");
    } catch (error) {
        captureError(error, {
            action: 'listEmbeddingVectorCollections',
            component: 'ChatAPI'
        });
        throw error;
    }
}

interface AskDocumentPayload {
    document_ids: string[];
    query: string;
}

export async function askDocument(payload: AskDocumentPayload): Promise<string> {
    try {
        const bearer = cookies.get(CookieName.API_TOKEN);
        
        console.log('API Call Details:', {
            endpoint: 'qa/ask_documents',
            method: 'POST',
            body: JSON.stringify(payload, null, 2)
        });
        
        const { data, response } = await ApiRequest<string>("qa/ask_documents", {
            bearer,
            init: {
                method: "POST",
                body: JSON.stringify(payload)
            }
        });

        console.log('API Response Details:', {
            status: response?.status,
            statusText: response?.statusText,
            ok: response?.ok,
            data: data,
            error: response && !response.ok ? await response?.text() : null
        });

        if (!data) {
            throw new Error('No data received from the server');
        }

        return data;
    } catch (error) {
        captureError(error, {
            action: 'askDocument',
            component: 'ChatAPI'
        });
        console.error('API Error:', error);
        throw error;
    }
}
