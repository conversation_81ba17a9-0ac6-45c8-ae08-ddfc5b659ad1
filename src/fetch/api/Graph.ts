import "client-only";
import cookies from "js-cookie";
import { ApiRequest } from "../FetchRequest";
import { <PERSON>ieName } from "@/enums/CookieName";
import { captureError } from "@/utils/errorHandling";

// Types for GraphResponse from the Python API
export interface NodeData {
  name?: string;
  entity_id?: string;
  [key: string]: unknown;
}

export interface GraphNode {
  id: string;
  name: string;
  type: string;
  node_data: NodeData;
}

export interface GraphEdge {
  source: string;
  target: string;
  type: string;
}

export interface GraphResponse {
  nodes: GraphNode[];
  edges: GraphEdge[];
}

// Types for graph view parameters
export interface GraphViewParams {
  node_types: string[];
  base_relation?: string;
  metadata_only?: boolean;
}

// Types for updating a node
export interface UpdateNodeParams {
  node_data: NodeData;
  node_name?: string;
  node_id?: string;
  upsert_if_missing?: boolean;
}

// Types for enhancing a node
export interface EnhanceNodeResponse {
  status: string;
  enhanced_data: string | Record<string, unknown>;
  sources: string[];
}

/**
 * Fetches the objects graph
 * @returns GraphResponse with nodes and edges
 */
export async function getObjectsGraph() {
  try {
    const bearer = cookies.get(CookieName.API_TOKEN);
    
    console.log('Fetching objects graph...');
    
    const { data, response } = await ApiRequest<GraphResponse>("graph_query/get_objects_graph", { 
      bearer, 
      init: { method: "GET" } 
    });

    if (response?.ok && data) {
      console.log('Objects graph loaded:', {
        totalNodes: data.nodes.length,
        totalEdges: data.edges.length
      });
      return data;
    }

    console.error('Failed to load objects graph:', { 
      status: response?.status, 
      ok: response?.ok 
    });
    throw new Error("Failed to load objects graph");
  } catch (error) {
    captureError(error, {
      action: 'getObjectsGraph',
      component: 'GraphAPI'
    });
    throw error;
  }
}

/**
 * Fetches the complete graph
 * @returns GraphResponse with nodes and edges
 */
export async function getGraph() {
  try {
    const bearer = cookies.get(CookieName.API_TOKEN);
    
    console.log('Fetching graph...');
    
    const { data, response } = await ApiRequest<GraphResponse>("graph_query/get_graph", { 
      bearer, 
      init: { method: "GET" } 
    });

    if (response?.ok && data) {
      console.log('Graph loaded:', {
        totalNodes: data.nodes.length,
        totalEdges: data.edges.length
      });
      return data;
    }

    console.error('Failed to load graph:', { 
      status: response?.status, 
      ok: response?.ok 
    });
    throw new Error("Failed to load graph");
  } catch (error) {
    captureError(error, {
      action: 'getGraph',
      component: 'GraphAPI'
    });
    throw error;
  }
}

/**
 * Fetches a filtered view of the graph based on node types and other parameters
 * @param params GraphViewParams containing filtering options
 * @returns GraphResponse with filtered nodes and edges
 */
export async function getGraphView(params: GraphViewParams) {
  try {
    const bearer = cookies.get(CookieName.API_TOKEN);
    const queryParams = new URLSearchParams();
    
    // Add node_types as query parameters
    params.node_types.forEach(type => {
      queryParams.append('node_types', type);
    });
    
    // Add other parameters if provided
    if (params.base_relation) {
      queryParams.append('base_relation', params.base_relation);
    }
    
    if (params.metadata_only !== undefined) {
      queryParams.append('metadata_only', params.metadata_only.toString());
    }
    
    console.log('Fetching graph view with params:', {
      nodeTypes: params.node_types,
      baseRelation: params.base_relation,
      metadataOnly: params.metadata_only
    });
    
    const { data, response } = await ApiRequest<GraphResponse>("graph_query/get_graph_view", { 
      bearer, 
      init: { method: "GET" },
      params: queryParams
    });

    if (response?.ok && data) {
      console.log('Graph view loaded:', {
        totalNodes: data.nodes.length,
        totalEdges: data.edges.length
      });
      return data;
    }

    console.error('Failed to load graph view:', { 
      status: response?.status, 
      ok: response?.ok,
      params: params
    });
    throw new Error("Failed to load graph view");
  } catch (error) {
    captureError(error, {
      action: 'getGraphView',
      component: 'GraphAPI'
    });
    throw error;
  }
}

/**
 * Deletes a node from the graph
 * @param nodeId ID of the node to delete
 * @returns boolean indicating success
 */
export async function deleteNode(nodeId: string) {
  try {
    const bearer = cookies.get(CookieName.API_TOKEN);
    const params = new URLSearchParams({ node_id: nodeId });
    
    console.log('Deleting node:', nodeId);
    
    const { data, response } = await ApiRequest<boolean>("graph_query/delete_node", { 
      bearer, 
      init: { method: "DELETE" },
      params
    });

    if (response?.ok) {
      console.log('Node deleted:', {
        nodeId: nodeId,
        success: data
      });
      return data;
    }

    console.error('Failed to delete node:', { 
      status: response?.status, 
      ok: response?.ok,
      nodeId: nodeId
    });
    throw new Error("Failed to delete node");
  } catch (error) {
    captureError(error, {
      action: 'deleteNode',
      component: 'GraphAPI'
    });
    throw error;
  }
}

/**
 * Updates or creates a node in the graph
 * @param params UpdateNodeParams containing node data and options
 * @returns boolean, object or GraphNode depending on the operation
 */
export async function updateObjectNode(params: UpdateNodeParams) {
  try {
    const bearer = cookies.get(CookieName.API_TOKEN);
    const queryParams = new URLSearchParams();
    
    // Add parameters if provided
    if (params.node_name) {
      queryParams.append('node_name', params.node_name);
    }
    
    if (params.node_id) {
      queryParams.append('node_id', params.node_id);
    }
    
    if (params.upsert_if_missing !== undefined) {
      queryParams.append('upsert_if_missing', params.upsert_if_missing.toString());
    }
    
    console.log('Updating object node:', {
      nodeId: params.node_id || 'Not specified',
      nodeName: params.node_name || 'Not specified',
      upsert: params.upsert_if_missing || false
    });
    
    const { data, response } = await ApiRequest<boolean | Record<string, unknown> | GraphNode>("graph_query/update_object_node", { 
      bearer, 
      init: { 
        method: "POST",
        body: JSON.stringify(params.node_data)
      },
      params: queryParams
    });

    if (response?.ok) {
      console.log('Node updated:', {
        success: !!data,
        data: data
      });
      return data;
    }

    console.error('Failed to update node:', { 
      status: response?.status, 
      ok: response?.ok,
      params: params
    });
    throw new Error("Failed to update node");
  } catch (error) {
    captureError(error, {
      action: 'updateObjectNode',
      component: 'GraphAPI'
    });
    throw error;
  }
}

/**
 * Enhances a node with additional information from external sources
 * @param query Query string to send to enhancement service
 * @returns EnhanceNodeResponse with enhanced data and sources
 */
export async function enhanceNode(query: string) {
  try {
    const bearer = cookies.get(CookieName.API_TOKEN);
    const params = new URLSearchParams({ query });
    
    console.log('Enhancing node with query:', query);
    
    const { data, response } = await ApiRequest<EnhanceNodeResponse>("graph_query/enhance_node", { 
      bearer, 
      init: { method: "GET" },
      params
    });

    if (response?.ok && data) {
      console.log('Node enhanced:', {
        status: data.status,
        sourcesCount: data.sources.length
      });
      return data;
    }

    console.error('Failed to enhance node:', { 
      status: response?.status, 
      ok: response?.ok,
      query: query
    });
    throw new Error("Failed to enhance node");
  } catch (error) {
    captureError(error, {
      action: 'enhanceNode',
      component: 'GraphAPI'
    });
    throw error;
  }
}