import { AuthRequest } from "../FetchRequest";
import cookies from "js-cookie";
import { CookieName } from "@/enums/CookieName";
import { captureError } from "@/utils/errorHandling";
import { AppRoutes } from "@/enums/AppRoutes";

const isClient = typeof window !== 'undefined';

export type Credentials = Readonly<{
    username: string;
    password: string;
}>;

export type SignInResponse = {
    access: string;
    refresh: string;
    user?: User;
};

export interface AppConfig {
    ui: {
        new_chat_button_disabled: boolean;
        default_landing_page: AppRoutes; // '/chat' | '/documents' | '/settings'
    };
    features: {
        // Add feature flags here
        enable_new_chat: boolean;
        enable_chats: boolean;
    };
};

export type PartialAppConfig = {
    ui?: Partial<AppConfig['ui']>;
    features?: Partial<AppConfig['features']>;
}

export type User = {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    name: string;
    image?: string;
    app_config?: AppConfig;
    assigned_tenant?: {
        tenant_contextclue_api_key: string;
        tenant_contextclue_api_key_name: string;
        tenant_deleted: boolean;
        tenant_description: string;
        tenant_document_db_port: number;
        tenant_document_processor_port: number;
        tenant_host: string;
        tenant_id: number;
        tenant_max_users: number;
        tenant_name: string;
        tenant_ner_port: number | null;
        tenant_port: number;
        tenant_schema_name: string;
        tenant_table_analysis_port: number | null;
        tenant_vector_db_port: number;
    };
};

export async function SignInCommand(credentials: Credentials) {
    const url = "token/"; // or the full URL if AuthRequest doesn't log it internally
    try {
        const { data, response } = await AuthRequest<SignInResponse>(url, {
            init: {
                method: "POST",
                body: JSON.stringify(credentials),
                headers: {
                    "Content-Type": "application/json"
                }
            }
        });

        if (response?.ok && data && response.status === 200) {
            if (isClient) {
                const storedConfig = localStorage.getItem('app_config');
                if (!storedConfig && data.user?.app_config) {
                    localStorage.setItem('app_config', JSON.stringify(data.user.app_config));
                }
            }
            return data;
        }

        let errorMessage = "Invalid credentials";
        if (response) {
            try {
                const errorData = await response.clone().json();
                errorMessage = errorData?.message || JSON.stringify(errorData);
            } catch {
                const errorText = await response.text();
                errorMessage = `Raw response: ${errorText}`;
            }
        }

        throw new Error(`POST ${response?.url} [${response?.status}] ${errorMessage}`);
    } catch (error) {
        captureError(error, {
            action: "SignIn",
            component: "AuthAPI",
            username: credentials.username,
            url,
            payload: JSON.stringify(credentials),
        });

        throw error;
    }
}

// Client-side getMe using js-cookie
export async function getMe(): Promise<User> {
    try {
        const bearer = cookies.get(CookieName.API_TOKEN);

        if (!bearer) {
            if (isClient) {
                const cachedUserData = localStorage.getItem('userData');
                if (cachedUserData) {
                    return JSON.parse(cachedUserData);
                }
            }
            throw new Error('No authentication token available');
        }

        const { data, response } = await AuthRequest<User>("account/me", {
            bearer,
            init: {
                method: "GET"
            }
        });

        if (response?.ok && data && response.status === 200) {
            data.name = data.first_name + ' ' + data.last_name;
            if (isClient) {
                localStorage.setItem('userData', JSON.stringify(data));
            }
            return data;
        }

        if (response?.status === 401 || response?.status === 403) {
            if (isClient) {
                localStorage.removeItem('userData');
            }
        }

        throw new Error(`Failed to fetch user data: ${response?.status}`);
    } catch (error) {
        captureError(error, {
            action: 'GetMe',
            component: 'AuthAPI'
        });
        throw error;
    }
}

// Server-side getMe using access token directly
export async function getMeServer(access: string): Promise<User> {
    try {
        const { data, response } = await AuthRequest<User>("account/me", {
            bearer: access,
            init: {
                method: "GET"
            }
        });

        if (response?.ok && data && response.status === 200) {
            data.name = data.first_name + ' ' + data.last_name;
            return data;
        }

        throw new Error(`Failed to fetch user data: ${response?.status}`);
    } catch (error) {
        captureError(error, {
            action: 'GetMeServer',
            component: 'AuthAPI'
        });
        throw error;
    }
}
