import "client-only";
import cookies from "js-cookie";
import { ApiRequest } from "../FetchRequest";
import { <PERSON>ieName } from "@/enums/CookieName";
import { captureError } from "@/utils/errorHandling";

// Type definitions for the process step response
export interface ProcessStepResponse {
  success: boolean;
  details: string;
  error: string | null;
  traceback?: string;
}

/**
 * Process a single step of the graph extraction
 * @param step - The step number to process (0-9)
 * @returns Promise with the process step response
 */
export async function processStep(step: number): Promise<ProcessStepResponse> {
  try {
    const bearer = cookies.get(CookieName.API_TOKEN);
    
    console.log('API Call Details:', {
      endpoint: `process_step?step=${step}`,
      method: 'POST',
      step: step
    });
    
    const { data, response } = await ApiRequest<ProcessStepResponse>(`process_step?step=${step}`, { 
      bearer, 
      init: { 
        method: "POST"
      } 
    });

    console.log('API Response Details:', {
      status: response?.status,
      statusText: response?.statusText,
      ok: response?.ok,
      data: data
    });

    if (response?.ok && data) {
      return data;
    }

    // Check for 422 status and provide a more specific error message
    if (response?.status === 422) {
      return {
        success: false,
        details: "Invalid step parameters",
        error: `The server rejected the step parameter: ${step}. This may occur if the step is out of sequence or invalid.`
      };
    }

    throw new Error(`Request failed: ${response?.statusText || 'Unknown error'}`);
  } catch (error) {
    captureError(error, {
      action: 'processStep',
      component: 'GraphProcessingAPI',
      step: step
    });
    console.error('API Error:', error);
    
    // Return a safe error response
    return {
      success: false,
      details: "Request failed",
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Helper function to get a human-readable description of a processing step
 * @param step - The step number
 * @returns String description of the step
 */
export function getStepDescription(step: number): string {
  switch (step) {
    case 0:
      return "Initialize processing environment";
    case 1:
      return "Process file nodes";
    case 2:
      return "Process model file nodes";
    case 3:
      return "Process tables";
    case 4:
      return "Process identifiers";
    case 5:
      return "Process instances";
    case 6:
      return "Process enrichment matching";
    case 7:
      return "Process enrichment models";
    case 8:
      return "Process enrichment fuzzy";
    case 9:
      return "Process join identifiers";
    default:
      return `Unknown step ${step}`;
  }
}