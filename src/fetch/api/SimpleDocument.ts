import { captureError } from "@/utils/errorHandling";
import cookies from "js-cookie";
import { CookieName } from "@/enums/CookieName";
import { ApiRequest } from "@/fetch/FetchRequest";

export enum DocumentType {
    PDF = "pdf",
    TXT = "txt",
    DOCX = "docx",
    // Add other types as needed
}

export enum DocumentStatus {
    PENDING = "pending",
    PROCESSING = "processing",
    PROCESSED = "processed",
    FAILED = "failed",
    // Add other statuses as needed
}

export interface ChunkMetadata {
    // Add specific metadata fields if known
    [key: string]: unknown;
}

export interface ChunkModel {
    id: string;
    text: string;
    chunk_metadata: ChunkMetadata;
    keywords: string[];
    text_hash: string | null;
}

export interface FileReferenceModel {
    id: string;
    name: string;
    f_bytes: string;
}

export interface SimpleDocument {
    id: string;
    name: string | null;
    source: string | null;
    document_type: DocumentType | null;
    created_at: string | null;  // datetime as ISO string
    updated_at: string;         // datetime as ISO string
    status: DocumentStatus | null;
    document_metadata: Record<string, unknown>;
    collections: string[];
    processor_configs: Record<string, unknown>;
    chunks: string[] | ChunkModel[];
    keywords: string[];
    summary: Record<string, unknown>;
    source_file: FileReferenceModel | null;
    processing_hash: string | null;
}

export async function GetSimpleDocument(id: string): Promise<SimpleDocument> {
    try {
        console.log('[GetSimpleDocument] Fetching document:', id);
        const token = cookies.get(CookieName.API_TOKEN);
        const { data, response } = await ApiRequest<{ document: SimpleDocument }>(
            `documents/${id}`,
            { bearer: token, init: { method: "GET" } }
        );

        console.log('[GetSimpleDocument] Raw API response:', {
            status: response?.status,
            statusText: response?.statusText,
            fullData: data,
            dataType: typeof data,
            hasChunks: data?.document?.chunks ? 'yes' : 'no',
            chunksType: data?.document?.chunks ? typeof data.document.chunks : 'N/A',
            firstChunk: data?.document?.chunks?.[0]
        });

        if (!response?.ok || !data?.document) {
            console.error('[GetSimpleDocument] Failed to fetch document:', {
                id,
                status: response?.status,
                statusText: response?.statusText
            });
            throw new Error('Failed to fetch document');
        }

        const document = data.document;
        console.log('[GetSimpleDocument] Successfully fetched document:', {
            id: document.id,
            name: document.name,
            chunks: document.chunks?.length ?? 0,
            fullData: document
        });

        return document;
    } catch (error) {
        console.error('[GetSimpleDocument] Error:', error);
        captureError(error, {
            action: 'GetSimpleDocument',
            component: 'SimpleDocumentAPI',
            documentId: id
        });
        throw error;
    }
}

export async function UpdateSimpleDocument(id: string, document: Partial<SimpleDocument>): Promise<SimpleDocument> {
    try {
        const token = cookies.get(CookieName.API_TOKEN);
        const { data, response } = await ApiRequest<SimpleDocument>(
            `documents/${id}`,
            {
                init: {
                    method: 'PUT',
                    body: JSON.stringify(document)
                },
                bearer: token,
            }
        );

        if (!response?.ok || !data) {
            throw new Error('Failed to update document');
        }

        return data;
    } catch (error) {
        captureError(error, {
            action: 'UpdateSimpleDocument',
            component: 'SimpleDocumentAPI',
            documentId: id,
            updateData: document
        });
        throw error;
    }
}

export async function CreateSimpleDocument(document: Omit<SimpleDocument, 'id'>): Promise<SimpleDocument> {
    try {
        const token = cookies.get(CookieName.API_TOKEN);
        const { data, response } = await ApiRequest<SimpleDocument>(
            'documents',
            {
                init: {
                    method: 'POST',
                    body: JSON.stringify(document)
                },
                bearer: token,
            }
        );

        if (!response?.ok || !data) {
            throw new Error('Failed to create document');
        }

        return data;
    } catch (error) {
        captureError(error, {
            action: 'CreateSimpleDocument',
            component: 'SimpleDocumentAPI',
            documentData: document
        });
        throw error;
    }
}
