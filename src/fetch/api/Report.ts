import { ApiRequest } from "../FetchRequest";
import cookies from "js-cookie";
import { CookieName } from "@/enums/CookieName";
import { config } from "@/config/env";

interface FillPPTXTemplateRequest {
    chatId: string;
    templateFile: File;
    generalConfig: {
        collection_id: string;
        context: {
            custom_instructions: string;
        };
    };
}

interface FillPPTXTemplateResponse {
    download_link?: string;
    error?: string;
}

export async function fillPPTXTemplate(params: FillPPTXTemplateRequest): Promise<FillPPTXTemplateResponse> {
    const bearer = cookies.get(CookieName.API_TOKEN);
    const formData = new FormData();

    // Add the file first
    formData.append("report_template", params.templateFile);
    
    // Add other fields as form data
    formData.append("general_config", JSON.stringify(params.generalConfig));
    formData.append("chat_id", params.chatId);

    console.log('PPTX Export API Call Details:', {
        endpoint: '/api/v1/report/fill_pptx_template',
        method: 'POST',
        chatId: params.chatId,
        generalConfig: params.generalConfig,
        fileName: params.templateFile.name
    });

    const response = await ApiRequest<FillPPTXTemplateResponse>("/api/v1/report/fill_pptx_template", {
        contentType: null,
        bearer,
        init: {
            method: "POST",
            body: formData,
        },
    });

    console.log('Raw response:', response);
    
    // Parse the response JSON if it hasn't been parsed already
    let data = response.data;
    if (!data && response.response) {
        try {
            data = await response.response.json();
            console.log('Parsed response data:', data);
        } catch (error) {
            console.error('Error parsing response:', error);
            return { error: "Failed to parse response" };
        }
    }

    if (data?.download_link) {
        console.log('Original download link:', data.download_link);
        
        // Replace the host and port in the download link with our API URL
        const originalUrl = new URL(data.download_link);
        const apiUrl = new URL(config.api.appUrl);
        
        console.log('URL parts:', {
            originalHost: originalUrl.origin,
            originalPath: originalUrl.pathname,
            originalQuery: originalUrl.search,
            newHost: apiUrl.origin
        });
        
        // Keep the path and query parameters but use our API host
        const downloadUrl = `${apiUrl.origin}${originalUrl.pathname}${originalUrl.search}`;
        console.log('Final download URL:', downloadUrl);
        
        // Create a download link
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `chat-export-${new Date().toISOString().split("T")[0]}.pptx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    return data || { error: "No data received" };
}
