import { isServer } from "@/utils";
import { toast } from "react-toastify";
import { config } from "@/config/env";
import { apiConfig } from "@/config/api";

type ApiRequestConfig = Readonly<{
    bearer?: string;
    contentType?: string | null;
    init?: RequestInit;
    params?: URLSearchParams;
    domain?: string;
    showError?: boolean;
}>;

type Response<T> = Readonly<{
    data: T | null;
    response: globalThis.Response | null;
}>;

export const FetchJsonRequest = async <T>(
    url: string,
    requestConfig: ApiRequestConfig = {},
): Promise<Response<T>> => {
    const {
        init = {},
        params = new URLSearchParams(),
        domain = config.api.appUrl,
        bearer,
        contentType = "application/json",
        showError = true,
    } = requestConfig;
    console.log('Request URL:', url);
    console.log('Request domain:', domain);
    let fetchUrl: URL;
    try {
        fetchUrl = new URL(url.startsWith('http') ? url : url, domain);
    } catch {
        throw new Error(`Invalid URL construction: url=${url}, domain=${domain}`);
    }

    params.forEach((value, key) => {
        fetchUrl.searchParams.set(key, value);
    });

    const defaultHeaders: Record<string, string> = {};

    if (bearer) {
        defaultHeaders["Authorization"] = `Bearer ${bearer}`;
    }

    if (contentType) {
        defaultHeaders["Content-Type"] = contentType;
    }

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 60000);
    console.log('Fetching URL:', fetchUrl.toString());
    try {
        const response = await fetch(fetchUrl.toString(), {
            ...init,
            headers: {
                ...defaultHeaders,
                ...init.headers,
            },
            signal: controller.signal,
        });

        clearTimeout(timeout);

        if (response.status === 401) {
            const errorContent = await response.text();
            console.error(`Fetch failed request "${fetchUrl.toString()}" content: "${errorContent}"`);

            if (isServer()) {
                const { redirect } = await import("next/navigation");
                await fetch("/api/auth/signout", { method: "POST" });
                redirect("/");
            } else {
                const { signOut } = await import("next-auth/react");
                await signOut();
            }

            throw new Error("Unauthorized");
        }

        if (!response.ok && showError && !isServer()) {
            toast.error(`Request failed: ${response.statusText}`);
        }

        if (contentType && contentType.includes("application/json")) {
            return {
                data: await response.json(),
                response,
            };
        } else {
            return {
                data: null,
                response,
            };
        }
    } catch (error: unknown) {
        clearTimeout(timeout);

        if (error instanceof Error) {
            console.error(`Fetch request "${url}" exception:`, error);

            if (showError && !isServer()) {
                const errorMessage = error.name === 'AbortError'
                    ? 'Request timed out'
                    : error instanceof TypeError && error.message.includes('Failed to fetch')
                        ? 'Network connection error'
                        : error.message;

                toast.error(`Request failed: ${errorMessage}`);
            }
        }

        return {
            data: null,
            response: null,
        };
    }
};

export const ApiRequest = async <T>(url: string, requestConfig: Omit<ApiRequestConfig, 'domain'> = {}) => {
    return FetchJsonRequest<T>("api/v1/" + url, {
        ...requestConfig,
        domain: apiConfig.baseUrl,
    });
};

export const AuthRequest = async <T>(url: string, requestConfig: Omit<ApiRequestConfig, 'domain'> = {}) => {
    return FetchJsonRequest<T>(url, {
        ...requestConfig,
        domain: apiConfig.authUrl,
    });
};

export async function checkApiHealth() {
    const response = await fetch(apiConfig.getApiUrl('health/ready'));

    return response.ok;
}

export async function checkAuthHealth() {
    const response = await fetch(apiConfig.getAuthUrl('docs/'));

    return response.ok;
}
