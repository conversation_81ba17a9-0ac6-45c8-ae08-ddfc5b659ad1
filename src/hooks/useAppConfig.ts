import { useCallback } from 'react';
import { AppConfig, PartialAppConfig } from '@/fetch/api/Auth';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { AppRoutes } from "@/enums/AppRoutes";

const APP_CONFIG_KEY = 'app_config';
const USER_QUERY_KEY = ['user'];

const DEFAULT_CONFIG: AppConfig = {
    ui: {
        new_chat_button_disabled: false,
        default_landing_page: AppRoutes.CHAT 
    },
    features: {
        enable_new_chat: false,
        enable_chats: false
    }
};

export function useAppConfig() {
    const queryClient = useQueryClient();

    const { data: storedConfig } = useQuery<AppConfig>({
        queryKey: ['app_config'],
        queryFn: () => {
            const stored = localStorage.getItem(APP_CONFIG_KEY);
            return stored ? JSON.parse(stored) : DEFAULT_CONFIG;
        },
        initialData: () => {
            const stored = localStorage.getItem(APP_CONFIG_KEY);
            return stored ? JSON.parse(stored) : DEFAULT_CONFIG;
        }
    });

    const updateAppConfig = useCallback((newConfig: PartialAppConfig) => {
        // Get current config
        const currentConfig = queryClient.getQueryData<AppConfig>(['app_config']) || DEFAULT_CONFIG;

        // Merge new config with current config
        const updatedConfig = {
            ...currentConfig,
            ui: {
                ...currentConfig.ui,
                ...newConfig.ui
            },
            features: {
                ...currentConfig.features,
                ...newConfig.features
            }
        };

        // Save to localStorage
        localStorage.setItem(APP_CONFIG_KEY, JSON.stringify(updatedConfig));

        // Update React Query cache
        queryClient.setQueryData(['app_config'], updatedConfig);

        return updatedConfig;
    }, [queryClient]);

    return {
        appConfig: storedConfig || DEFAULT_CONFIG,
        updateAppConfig
    };
}
