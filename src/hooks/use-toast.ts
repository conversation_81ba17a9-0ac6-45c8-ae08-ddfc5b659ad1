"use client";

import { toast as toastify } from "react-toastify";

interface ToastProps {
    title?: string;
    description?: string;
    variant?: "default" | "destructive";
}

export function toast({ title, description, variant }: ToastProps) {
    const message = description || title;
    if (!message) return;

    if (variant === "destructive") {
        toastify.error(message);
    } else {
        toastify.success(message);
    }
}

export function useToast() {
    return {
        toast,
        dismiss: toastify.dismiss,
    };
}
