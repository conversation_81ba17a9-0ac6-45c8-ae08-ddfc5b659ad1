import { useRouter } from "next/navigation";
import { useAppConfig } from "./useAppConfig";
import { AppRoutes } from "@/enums/AppRoutes";

export function useDefaultNavigation() {
    const router = useRouter();
    const { appConfig } = useAppConfig();

    const navigateToDefault = () => {
        const defaultPage = appConfig?.ui?.default_landing_page || AppRoutes.CHAT;
        router.push(defaultPage);
    };

    return { navigateToDefault };
}
