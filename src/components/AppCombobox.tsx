"use client";

import { Check, ChevronsUpDown } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/utils";
import { t } from "@/i18n";
import { useState } from "react";

export type AppComboboxOption = Readonly<{
    value: string;
    label: string;
    disabled?: boolean;
}>;

type AppComboboxProps = Readonly<{
    className?: string;
    options: AppComboboxOption[];
    defaultValue: string;
    onChange: (value: string) => void;
}>;

export function AppCombobox({ options, className, defaultValue, onChange }: AppComboboxProps) {
    const [open, setOpen] = useState(false);
    const [value, setValue] = useState(defaultValue);

    return (
        <Popover
            open={open}
            onOpenChange={setOpen}
        >
            <PopoverTrigger asChild>
                <Button
                    role="combobox"
                    aria-expanded={open}
                    className={cn(className, "w-full justify-between")}
                >
                    {options.length}
                    <span className="overflow-hidden">{value ? options.find((option) => option.value === value)?.label : t("Select")}</span>
                    <ChevronsUpDown className="opacity-50" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
                <Command>
                    <CommandInput placeholder={t("Search")} />
                    <CommandList>
                        <CommandEmpty>{t("No options found.")}</CommandEmpty>
                        <CommandGroup>
                            {options.map((option) => (
                                <CommandItem
                                    key={option.value}
                                    value={option.value}
                                    onSelect={(currentValue) => {
                                        setValue(currentValue);
                                        onChange(currentValue);
                                        setOpen(false);
                                    }}
                                >
                                    {option.label}
                                    <Check className={cn("ml-auto", value === option.value ? "opacity-100" : "opacity-0")} />
                                </CommandItem>
                            ))}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    );
}
