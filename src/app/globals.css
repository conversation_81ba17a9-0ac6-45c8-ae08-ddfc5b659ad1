@tailwind base;
@tailwind components;
@tailwind utilities;

@import 'react-toastify/dist/ReactToastify.css';

@layer utilities {
    .text-balance {
        text-wrap: balance;
    }
}

@layer base {
    :root {
        --context-clue-primary: 0 0% 95.29%;
        --context-clue-secondary: 242 69% 51%;
        --context-clue-sidebar: 242 66% 27%;
        --context-clue-accent: 79 98.72% 69.41%;
        --context-clue-background: 232 0% 100%;
        --context-clue-hover: #1A1A40;

        --background: var(--context-clue-background);
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: var(--context-clue-primary);
        --primary-foreground: var(--secondary);
        --secondary: var(--context-clue-secondary);
        --secondary-foreground: var(--primary);
        --muted: 0 0% 46.1%;
        --muted-foreground: 0 0% 45.1%;
        --accent: var(--context-clue-accent);
        --accent-foreground: var(--primary);
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 89.8%;
        --input: 0 0% 89.8%;
        --ring: 0 0% 3.9%;
        --hover: var(--context-clue-hover);
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;

        --sidebar-background: var(--context-clue-sidebar);
        --sidebar-foreground: var(--primary);
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;

        --left-sidebar-background: #f4f5fa;

        --toastify-color-light: #fff;
        --toastify-color-dark: #121212;
        --toastify-color-info: #3498db;
        --toastify-color-success: #07bc0c;
        --toastify-color-warning: #f1c40f;
        --toastify-color-error: hsl(231, 78%, 57%);
        --toastify-color-transparent: rgba(255, 255, 255, 0.7);
        
        --toastify-icon-color-info: var(--toastify-color-info);
        --toastify-icon-color-success: var(--toastify-color-success);
        --toastify-icon-color-warning: var(--toastify-color-warning);
        --toastify-icon-color-error: var(--toastify-color-error);
        
        --toastify-container-width: fit-content;
        --toastify-toast-width: 320px;
        --toastify-toast-offset: 16px;
        --toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));
        --toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));
        --toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));
        --toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));
        --toastify-toast-background: #fff;
        --toastify-toast-padding: 8px;
        --toastify-toast-min-height: 32px;
        --toastify-toast-max-height: 800px;
        --toastify-toast-bd-radius: 6px;
        --toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
        --toastify-font-family: sans-serif;
        --toastify-z-index: 9999;
        --toastify-text-color-light: #757575;
        --toastify-text-color-dark: #fff;
      
        /* Used only for colored theme */
        --toastify-text-color-info: #fff;
        --toastify-text-color-success: #fff;
        --toastify-text-color-warning: #fff;
        --toastify-text-color-error: #fff;
      
        --toastify-spinner-color: #616161;
        --toastify-spinner-color-empty-area: #e0e0e0;
        --toastify-color-progress-light: linear-gradient(to right, #4cd964, #5ac8fa, #007aff, #34aadc, #5856d6, #ff2d55);
        --toastify-color-progress-dark: #bb86fc;
        --toastify-color-progress-info: var(--toastify-color-info);
        --toastify-color-progress-success: var(--toastify-color-success);
        --toastify-color-progress-warning: var(--toastify-color-warning);
        --toastify-color-progress-error: var(--toastify-color-error);
        /* used to control the opacity of the progress trail */
        --toastify-color-progress-bgo: 0.2;
    }

    .dark {
        --background: 0 0% 3.9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 0 0% 9%;
        --secondary: 0 0% 14.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 14.9%;
        --muted-foreground: 0 0% 63.9%;
        --accent: 0 0% 14.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 14.9%;
        --input: 0 0% 14.9%;
        --ring: 0 0% 83.1%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}
