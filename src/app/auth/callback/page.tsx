import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import ClientAuth from "./ClientAuth"; // New client component for handling signIn

export default async function AuthCallback() {
    const cookieStore = await cookies();
    const accessToken = cookieStore.get("custom_access_token")?.value;
    const refreshToken = cookieStore.get("custom_refresh_token")?.value;

    if (!accessToken || !refreshToken) {
        redirect("/error");
    }

    return (
        <ClientAuth
            accessToken={accessToken}
            refreshToken={refreshToken}
        />
    );
}
