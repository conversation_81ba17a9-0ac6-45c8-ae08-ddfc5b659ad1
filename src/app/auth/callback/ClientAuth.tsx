"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { signIn } from "next-auth/react";

export default function ClientAuth({ accessToken, refreshToken }: { accessToken: string; refreshToken: string }) {
    const router = useRouter();
    const [status, setStatus] = useState<'loading' | 'error' | 'success'>('loading');
    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    useEffect(() => {
        const authenticate = async () => {
            try {
                console.log("Starting authentication with tokens");
                setStatus('loading');
                
                // Clear any existing error parameters from the URL
                if (window.location.href.includes('error=')) {
                    const cleanUrl = window.location.href.split('?')[0];
                    window.history.replaceState({}, document.title, cleanUrl);
                }
                
                const result = await signIn("credentials", {
                    redirect: false,
                    access_token: accessToken,
                    refresh_token: refreshToken,
                });

                if (result?.error) {
                    console.error("Sign in failed:", result.error);
                    setStatus('error');
                    setErrorMessage(result.error);
                    
                    // Redirect to sign-in page without any query parameters
                    router.replace("/auth/signin");
                } else {
                    console.log("Authentication successful, redirecting to chat");
                    setStatus('success');
                    
                    // Use replace instead of push to avoid having the callback URL in history
                    router.replace("/chat");
                }
            } catch (error) {
                console.error("Authentication error:", error);
                setStatus('error');
                setErrorMessage(error instanceof Error ? error.message : 'Unknown error');
                
                // Redirect to sign-in page without any query parameters
                router.replace("/auth/signin");
            }
        };

        authenticate();
    }, [accessToken, refreshToken, router]);

    if (status === 'error') {
        return (
            <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100">
                <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
                    <h1 className="text-2xl font-bold text-red-600 mb-4">Authentication Error</h1>
                    <p className="text-gray-700 mb-4">{errorMessage || 'Failed to authenticate. Please try again.'}</p>
                    <button 
                        onClick={() => router.push('/auth/signin')}
                        className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
                    >
                        Return to Sign In
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100">
            <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
                <h1 className="text-2xl font-bold text-gray-800 mb-4">Authorizing...</h1>
                <p className="text-gray-600">Please wait while we authenticate your session.</p>
                <div className="mt-4 flex justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
            </div>
        </div>
    );
}
