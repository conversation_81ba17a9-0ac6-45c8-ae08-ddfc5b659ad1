import "server-only";

import NextAuth, { AuthOptions } from "next-auth";
import { providers } from "@/auth";

const authOptions = {
    secret: process.env.NEXTAUTH_SECRET,
    session: {
        strategy: "jwt",
    },
    callbacks: {
        async jwt({ token, user }) {
            if (user && "access" in user && "refresh" in user) {
                return { ...token, ...user };
            } else {
                return token;
            }
        },
    },
    theme: {
        colorScheme: "light",
        logo: "/contextclue.png",
    },
    pages: {
        signIn: '/auth/signin',
    },
    providers,
} as AuthOptions;

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
