import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { CookieName } from "@/enums/CookieName";

export async function GET(request: NextRequest) {
    const url = request.nextUrl.searchParams.get("url");
    if (!url) {
        return NextResponse.json({ error: "No URL provided" }, { status: 400 });
    }

    try {
        const cookieStore = await cookies();
        const apiToken = cookieStore.get(CookieName.API_TOKEN)?.value;
        if (!apiToken) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const response = await fetch(url, {
            headers: {
                "Authorization": `Bearer ${apiToken}`
            }
        });

        if (!response.ok) {
            return NextResponse.json({ error: "Failed to fetch file" }, { status: response.status });
        }

        const contentType = response.headers.get("content-type");
        const contentDisposition = response.headers.get("content-disposition");

        const fileBuffer = await response.arrayBuffer();

        return new NextResponse(fileBuffer, {
            status: 200,
            headers: {
                "Content-Type": contentType || "application/octet-stream",
                "Content-Disposition": contentDisposition || 'attachment; filename="report.pptx"'
            }
        });
    } catch (error) {
        console.error("Download error:", error);
        return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    }
}
