import { NextResponse } from 'next/server'

export async function GET() {
  const encoder = new TextEncoder()

  const stream = new ReadableStream({
    async start(controller) {
      // Send initial message
      controller.enqueue(encoder.encode('data: {"message": "Connected to stream"}\n\n'))

      // Example: Stream your custom data
      const interval = setInterval(() => {
        const data = {
          message: `Update at ${new Date().toISOString()}`,
          // Add your custom data here
          customData: {
            status: "active",
            value: Math.random() * 100
          }
        }
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}\n\n`))
      }, 3000)

      // Cleanup on close
      return () => {
        clearInterval(interval)
      }
    }
  })

  return new NextResponse(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  })
}
