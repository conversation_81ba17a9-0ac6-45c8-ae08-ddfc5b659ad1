import "server-only";
import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import Chat from "@/modules/Chat/Chat";
import { ChatSlug } from "@/enums/ChatSlug";
import { AppRoutes } from "@/enums/AppRoutes";

interface PageProps {
    readonly params: Promise<{
        readonly slug?: string[];
    }>;
}

export default async function Page({ params }: PageProps) {
    const { slug } = await params;
    const session = await getServerSession();

    if (!session) {
        return redirect(AppRoutes.AUTH);
    }

    const path = slug !== undefined ? (slug.join("/") as ChatSlug) : ChatSlug.DEFAULT;

    if (!Object.values(ChatSlug).includes(path)) {
        redirect(AppRoutes.CHAT);
    }

    return <Chat slug={path} />;
}
