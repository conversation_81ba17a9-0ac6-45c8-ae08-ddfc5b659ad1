import "server-only";
import type { Metadata } from "next";
import "./globals.css";
import AppContextsProvider from "@/context/AppContextsProvider";
import { ReactNode } from "react";
import { Poppins } from "next/font/google";
import { ToastContainer, Slide } from "react-toastify";

const poppins = Poppins({
    subsets: ["latin"],
    display: "swap",
    variable: "--font-poppins",
    weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
    title: "ContextClue",
    description: "ContextClue - Contextual Search Engine",
};

interface RootLayoutProps {
    readonly children: ReactNode;
}

export default async function RootLayout({ children }: RootLayoutProps) {
    return (
        <html
            lang="en"
            className={poppins.className}
        >
            <body>
                <AppContextsProvider>
                    <main className="bg-background h-screen w-full">{children}</main>
                    <ToastContainer
                        position="top-center"
                        autoClose={5000}
                        hideProgressBar={true}
                        newestOnTop={true}
                        closeOnClick={true}
                        rtl={false}
                        theme="light"
                        transition={Slide}
                    />
                </AppContextsProvider>
            </body>
        </html>
    );
}
