import { NextRequest, NextResponse } from "next/server";
import { jwtDecode, JwtPayload } from "jwt-decode";

type AppJwtPayload = { user_id: string } & JwtPayload;

export function middleware(request: NextRequest) {
    const { searchParams } = new URL(request.url);
    const accessToken = searchParams.get("access_token");
    const refreshToken = searchParams.get("refresh_token");

    if (accessToken && refreshToken) {
        const decoded = validateToken(accessToken);
        if (decoded) {
            // Store tokens in a cookie or header
            const response = NextResponse.redirect(new URL("/auth/callback", request.url));
            response.cookies.set("custom_access_token", accessToken, { httpOnly: true, secure: true, sameSite: "strict" });
            response.cookies.set("custom_refresh_token", refreshToken, { httpOnly: true, secure: true, sameSite: "strict" });
            return response;
        } else {
            return NextResponse.redirect(new URL("/error", request.url));
        }
    }

    return NextResponse.next();
}

const validateToken = (token: string) => {
    try {
        const decoded = jwtDecode<AppJwtPayload>(token);
        return decoded;
    } catch (error) {
        console.error("Token validation failed:", error);
        return null;
    }
};

// export async function middleware(request: NextRequest) {
//   // const accessToken = request.nextUrl.searchParams.get('access_token');
//   // const refreshToken = request.nextUrl.searchParams.get('refresh_token');

//   // if (accessToken && refreshToken) {
//   //   const cookise = await cookies();
//   //   cookise.set(CookieName.API_TOKEN, accessToken);
//   //   cookise.set(CookieName.API_REFRESH_TOKEN, refreshToken, { httpOnly: true });

//   //   return NextResponse.redirect(new URL('/chat', request.url));
//   // }

//   return NextResponse.redirect(new URL('', request.url));
//   }
