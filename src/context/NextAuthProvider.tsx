"use client";

import "client-only";
import { ReactNode } from "react";
import { SessionProvider } from "next-auth/react";
import { Session } from "next-auth";

interface AppContextProviderProps {
    readonly children: ReactNode;
    readonly session: Session | null;
}

export default function NextAuthProvider({ children, session }: AppContextProviderProps) {
    return <SessionProvider session={session}>{children}</SessionProvider>;
}
