import "server-only";
import { ReactNode } from "react";
import { getServerSession } from "next-auth";
import NextAuthProvider from "@/context/NextAuthProvider";
import { SidebarProvider } from "@/components/ui/sidebar";
import ReactQueryProvider from "@/context/ReactQueryProvider";

interface AppContextProviderProps {
    readonly children: ReactNode;
}

export default async function AppContextsProvider({ children }: AppContextProviderProps) {
    const session = await getServerSession();

    return (
        <SidebarProvider>
            <NextAuthProvider session={session}>
                <ReactQueryProvider>{children}</ReactQueryProvider>
            </NextAuthProvider>
        </SidebarProvider>
    );
}
