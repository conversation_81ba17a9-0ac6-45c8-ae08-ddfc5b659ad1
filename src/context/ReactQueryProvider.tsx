"use client";

import "client-only";
import { ReactNode } from "react";
import { QueryClient, QueryClientProvider, QueryCache, MutationCache } from "@tanstack/react-query";

interface AppContextProviderProps {
    readonly children: ReactNode;
}

// Create a client with better error handling and retry logic
const client = new QueryClient({
    defaultOptions: {
        queries: {
            // Retry failed queries up to 1 time
            retry: 1,
            // Don't refetch on window focus for auth-related queries
            refetchOnWindowFocus: false,
            // Cache data for 5 minutes
            staleTime: 5 * 60 * 1000,
        },
        mutations: {
            // Default retry for mutations
            retry: 0
        }
    },
    queryCache: new QueryCache({
        onError: (error) => {
            console.error('Query error:', error);
        }
    }),
    mutationCache: new MutationCache({
        onError: (error) => {
            console.error('Mutation error:', error);
        }
    })
});

export default function ReactQueryProvider({ children }: AppContextProviderProps) {
    return <QueryClientProvider client={client}>{children}</QueryClientProvider>;
}
