"use client";

import "client-only";
import { createContext, Dispatch, ReactNode, useEffect, useReducer, useState } from "react";
import { produce } from "immer";
import { SendMessageResponse } from "@/fetch/api/Chat";

interface AppContextProviderProps {
    readonly children: ReactNode;
}

interface ChatState {
    readonly chats: { [chatId: string]: Chat | undefined };
    currentChatId: string | null;
    currentMessageId: string | null;
    showWelcomePage: boolean;
}

export interface ChatMessage {
    createdAt: number;
    messageId: string;
    query: string;
    result: SendMessageResponse | null;
    isError: boolean;
}

export type Chat = Readonly<{
    createdAt: number;
    chatId: string;
    name: string;
    userId: string | null;
    messages: { [messageId: string]: ChatMessage | undefined };
}>;

export enum ChatAction {
    INITIALIZE = "INITIALIZE",
    CREATE_CHAT = "CREATE_CHAT",

    SET_CURRENT_CHAT = "SET_CURRENT_CHAT",
    DELETE_CURRENT_CHAT = "DELETE_CURRENT_CHAT",
    SET_CURRENT_MESSAGE = "SELECT_CHAT",

    CREATE_MESSAGE = "CREATE_MESSAGE",
    SET_MESSAGE_RESPONSE = "SET_MESSAGE_RESPONSE",
    SET_MESSAGE_ERROR_STATE = "SET_MESSAGE_ERROR_STATE",
    TOGGLE_WELCOME_PAGE = "TOGGLE_WELCOME_PAGE",
}

export type ChatActions =
    | Readonly<{ type: ChatAction.INITIALIZE; payload: Readonly<{ state: ChatState }> }>
    | Readonly<{ type: ChatAction.CREATE_CHAT; payload: Readonly<{ chatId: string; name: string; userId: string | null }> }>
    | Readonly<{ type: ChatAction.CREATE_MESSAGE; payload: Readonly<{ chatId: string; messageId: string; query: string }> }>
    | Readonly<{ type: ChatAction.SET_MESSAGE_ERROR_STATE; payload: Readonly<{ chatId: string; messageId: string; error: boolean }> }>
    | Readonly<{ type: ChatAction.SET_MESSAGE_RESPONSE; payload: Readonly<{ chatId: string; messageId: string; response: SendMessageResponse }> }>
    | Readonly<{ type: ChatAction.SET_CURRENT_MESSAGE; payload: Readonly<{ messageId: string | null }> }>
    | Readonly<{ type: ChatAction.SET_CURRENT_CHAT; payload: Readonly<{ chatId: string | null }> }>
    | Readonly<{ type: ChatAction.DELETE_CURRENT_CHAT; payload: Readonly<{ chatId: string }> }>
    | Readonly<{ type: ChatAction.TOGGLE_WELCOME_PAGE; payload: Readonly<{ show: boolean }> }>;

const chatReducer = produce((state: ChatState, action: ChatActions) => {
    const { type } = action;
    switch (type) {
        case ChatAction.INITIALIZE: {
            Object.assign(state, action.payload.state);
            break;
        }
        case ChatAction.CREATE_CHAT: {
            const { chatId, name, userId } = action.payload;
            state.currentChatId = chatId;
            state.chats[chatId] = {
                createdAt: Date.now(),
                chatId,
                name,
                userId,
                messages: {},
            };
            console.log(`Chat created in store: ${chatId} for user: ${userId || 'anonymous'}`);
            break;
        }
        case ChatAction.CREATE_MESSAGE: {
            const { chatId, query, messageId } = action.payload;

            if (!state.chats[chatId]) {
                throw new Error(`Chat with ${chatId} not exists`);
            }

            state.chats[chatId].messages[messageId] = {
                createdAt: Date.now(),
                messageId,
                query,
                result: null,
                isError: false,
            };
            break;
        }
        case ChatAction.SET_MESSAGE_ERROR_STATE: {
            const { chatId, messageId, error } = action.payload;
            if (!state.chats[chatId]) {
                throw new Error(`Chat with ${chatId} not exists`);
            }
            if (!state.chats[chatId].messages[messageId]) {
                throw new Error(`Message with id "${chatId}" not exists`);
            }
            state.chats[chatId].messages[messageId].isError = error;
            break;
        }
        case ChatAction.SET_MESSAGE_RESPONSE: {
            const { chatId, messageId, response: result } = action.payload;

            if (!state.chats[chatId]) {
                throw new Error(`Chat with ${chatId} not exists`);
            }

            if (!state.chats[chatId].messages[messageId]) {
                throw new Error(`Message with ${messageId} not exists`);
            }

            state.chats[chatId].messages[messageId].result = result;
            break;
        }
        case ChatAction.SET_CURRENT_CHAT: {
            state.currentChatId = action.payload.chatId;
            break;
        }
        case ChatAction.SET_CURRENT_MESSAGE: {
            state.currentMessageId = action.payload.messageId;
            break;
        }
        case ChatAction.DELETE_CURRENT_CHAT: {
            delete state.chats[action.payload.chatId];
            break;
        }
        case ChatAction.TOGGLE_WELCOME_PAGE: {
            state.showWelcomePage = action.payload.show;
            break;
        }
        default:
            throw new Error(`Unhandled action type: ${type}`);
    }
});

const defaultValues = {
    state: { chats: {}, currentChatId: null, showWelcomePage: true } as ChatState,
    dispatch: ((value: ChatActions) => {
        throw new Error(`Dispatch not initialized for action ${value}`);
    }) as Dispatch<ChatActions>,
} as const;

export const ChatContext = createContext(defaultValues);
const chatsStorage = "chats";

const MAX_CHATS = 25; // Reduced from 50 to 25 to prevent storage issues
const MAX_MESSAGES_PER_CHAT = 50; // Reduced from 100 to 50
const MAX_STORAGE_SIZE = 4.5 * 1024 * 1024; // 4.5MB limit (localStorage typically has 5MB)

const cleanupOldChats = (state: ChatState): ChatState => {
    const chats = Object.entries(state.chats);
    if (chats.length <= MAX_CHATS) return state;

    // Sort chats by creation time, newest first
    const sortedChats = chats.sort(([, a], [, b]) => {
        return (b?.createdAt || 0) - (a?.createdAt || 0);
    });

    // Keep only the MAX_CHATS most recent chats
    const newChats = Object.fromEntries(sortedChats.slice(0, MAX_CHATS));
    return {
        ...state,
        chats: newChats
    };
};

const cleanupChatMessages = (chat: Chat): Chat => {
    const messages = Object.entries(chat.messages);
    if (messages.length <= MAX_MESSAGES_PER_CHAT) return chat;

    // Sort messages by creation time, newest first
    const sortedMessages = messages.sort(([, a], [, b]) => {
        return (b?.createdAt || 0) - (a?.createdAt || 0);
    });

    // Keep only the MAX_MESSAGES_PER_CHAT most recent messages
    const newMessages = Object.fromEntries(sortedMessages.slice(0, MAX_MESSAGES_PER_CHAT));
    return {
        ...chat,
        messages: newMessages
    };
};

const cleanupStorage = (state: ChatState): ChatState => {
    // First try normal cleanup
    let cleanState = cleanupOldChats({
        ...state,
        chats: Object.fromEntries(
            Object.entries(state.chats).map(([id, chat]) => [
                id,
                chat ? cleanupChatMessages(chat) : chat
            ])
        )
    });

    // Check if the cleaned state is still too large
    const stateSize = new Blob([JSON.stringify(cleanState)]).size;
    if (stateSize > MAX_STORAGE_SIZE) {
        // Calculate how many chats we can keep based on average size
        const avgChatSize = stateSize / Object.keys(cleanState.chats).length;
        const maxChatsToKeep = Math.floor(MAX_STORAGE_SIZE / avgChatSize) - 1; // Keep one less for safety

        // Sort chats by creation time, newest first
        const sortedChats = Object.entries(cleanState.chats)
            .sort(([, a], [, b]) => (b?.createdAt || 0) - (a?.createdAt || 0))
            .slice(0, maxChatsToKeep);

        cleanState = {
            ...cleanState,
            chats: Object.fromEntries(sortedChats)
        };
    }

    return cleanState;
};

export default function ChatContextProvider({ children }: AppContextProviderProps) {
    const [isSynced, setIsSynced] = useState(false);
    const [state, dispatch] = useReducer(chatReducer, { chats: {}, currentChatId: null, showWelcomePage: true } as ChatState);

    useEffect(() => {
        const chats = localStorage.getItem(chatsStorage);

        if (chats) {
            dispatch({
                type: ChatAction.INITIALIZE,
                payload: { state: JSON.parse(chats) },
            });
        }

        setIsSynced(true);
    }, [setIsSynced]);

    useEffect(() => {
        if (!isSynced) {
            return;
        }

        try {
            // Use the new cleanupStorage function
            const cleanState = cleanupStorage(state);
            const serializedState = JSON.stringify(cleanState);
            
            // Check if we can store this state
            if (new Blob([serializedState]).size > MAX_STORAGE_SIZE) {
                throw new Error('State too large even after cleanup');
            }

            localStorage.setItem(chatsStorage, serializedState);
            
            // Log storage usage
            const usage = new Blob([serializedState]).size / (1024 * 1024);
            console.log(`Storage usage: ${usage.toFixed(2)}MB / ${(MAX_STORAGE_SIZE / (1024 * 1024)).toFixed(2)}MB`);
        } catch (error) {
            if (error instanceof Error) {
                console.error('Failed to save chats to localStorage:', error.message);
                
                // Clear old data if we hit storage limits
                if (error.name === 'QuotaExceededError' || error.message === 'State too large even after cleanup') {
                    try {
                        // Keep only the 10 most recent chats with 25 messages each
                        const emergencyCleanState = {
                            ...state,
                            chats: Object.fromEntries(
                                Object.entries(state.chats)
                                    .sort(([, a], [, b]) => (b?.createdAt || 0) - (a?.createdAt || 0))
                                    .slice(0, 10)
                                    .map(([id, chat]) => [
                                        id,
                                        chat ? {
                                            ...chat,
                                            messages: Object.fromEntries(
                                                Object.entries(chat.messages)
                                                    .sort(([, a], [, b]) => (b?.createdAt || 0) - (a?.createdAt || 0))
                                                    .slice(0, 25)
                                            )
                                        } : chat
                                    ])
                            )
                        };
                        localStorage.setItem(chatsStorage, JSON.stringify(emergencyCleanState));
                        console.log('Emergency cleanup completed');
                    } catch (retryError) {
                        console.error('Failed to save chats even after emergency cleanup:', retryError);
                        // Last resort: clear all chats
                        localStorage.removeItem(chatsStorage);
                        console.log('All chat data cleared due to persistent storage issues');
                    }
                }
            }
        }
    }, [state, isSynced]);

    return (
        <ChatContext.Provider
            value={{
                state,
                dispatch,
            }}
        >
            {children}
        </ChatContext.Provider>
    );
}
