"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useConfiguration } from '../hooks/useConfiguration';

interface ApiVersionContextType {
  useExperimentalApi: boolean;
  toggleApiVersion: () => void;
}

const ApiVersionContext = createContext<ApiVersionContextType>({
  useExperimentalApi: false,
  toggleApiVersion: () => { },
});

export const useApiVersion = () => useContext(ApiVersionContext);

export const ApiVersionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { config, updateConfig } = useConfiguration();
  const [useExperimentalApi, setUseExperimentalApi] = useState<boolean>(!!config.useExperimentalApi);

  // Sync with configuration when it changes
  useEffect(() => {
    setUseExperimentalApi(!!config.useExperimentalApi);
  }, [config.useExperimentalApi]);

  const toggleApiVersion = () => {
    const newValue = !useExperimentalApi;
    setUseExperimentalApi(newValue); // Update state immediately for responsive UI
    updateConfig(['useExperimentalApi'] as const, newValue); // Also update in configuration storage
  };

  return (
    <ApiVersionContext.Provider value={{ useExperimentalApi, toggleApiVersion }}>
      {children}
    </ApiVersionContext.Provider>
  );
};