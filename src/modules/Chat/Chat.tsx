"use client";

import { ChatSlug } from "@/enums/ChatSlug";
import ChatContextProvider from "./contexts/ChatContextProvider";
import LeftSidebar from "./components/LeftSidebar/LeftSidebar";
import ProductivitySection from "./components/ProductivitySection/ProductivitySection";
import Messages from "./components/Messages/Messages";
import RightSidebar from "./components/RightSidebar/RightSidebar";
import { useChats } from "./hooks/ChatState";
import Knowledge from "./components/Knowledge";
import Link from "next/link";
import { X } from "lucide-react";
import { AppRoutes } from "@/enums/AppRoutes";
import { useState } from "react";
import { MessageInputProvider } from "./hooks/MessageInput";
import { EmptyState } from "./components/EmptyState";
import { useQuery } from "@tanstack/react-query";
import { getMe } from "@/fetch/api/Auth";
import { Chat as ChatType } from "./contexts/ChatContextProvider";
import { SimpleDocument, DocumentType, DocumentStatus } from "@/fetch/api/SimpleDocument";
import { useSession } from "next-auth/react";
import { ApiVersionProvider } from './contexts/ApiVersionContext';

// Local interface to match Knowledge component's SimpleDocument
interface KnowledgeDocument {
    id: string;
    name: string;
    status: string;
    created_at: string;
    collections: string[];
    document_type: string;
    keywords: string[];
    source_file: {
        id: string;
        name: string;
        f_bytes: string;
    };
}

interface ChatProps {
    readonly slug: ChatSlug;
}

function ChatContent({ isKnowledge }: { isKnowledge: boolean }) {
    const {
        state: { currentChatId, chats, showWelcomePage },
    } = useChats();
    const { status } = useSession();
    const { data: user } = useQuery({
        queryKey: ["user"],
        queryFn: getMe,
        enabled: status === "authenticated",
        staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
        retry: 3, // Retry failed requests 3 times
    });
    const [selectedDocument, setSelectedDocument] = useState<SimpleDocument | null>(null);

    if (status !== "authenticated" || !user) {
        return null;
    }

    const handleDocumentSelect = (doc: KnowledgeDocument | null) => {
        if (doc) {
            // Convert KnowledgeDocument to SimpleDocument
            const apiDocument: SimpleDocument = {
                ...doc,
                source: null,
                updated_at: doc.created_at,
                document_metadata: {},
                processor_configs: {},
                chunks: [],
                summary: {},
                processing_hash: null,
                // Convert string document_type to enum or null
                document_type: Object.values(DocumentType).includes(doc.document_type as DocumentType) ? (doc.document_type as DocumentType) : null,
                // Convert string status to enum or null
                status: Object.values(DocumentStatus).includes(doc.status as DocumentStatus) ? (doc.status as DocumentStatus) : null,
            };
            setSelectedDocument(apiDocument);
        } else {
            setSelectedDocument(null);
        }
    };

    if (isKnowledge) {
        return (
            <>
                <section className="flex-1 flex flex-col p-12 bg-white overflow-auto">
                    <div className="flex justify-between items-center mb-8">
                        <h1 className="text-2xl font-semibold text-gray-900">Knowledge Base - Collections</h1>
                        <Link
                            href={AppRoutes.CHAT}
                            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                        >
                            <X className="h-5 w-5 text-gray-500" />
                        </Link>
                    </div>
                    <Knowledge onDocumentSelect={handleDocumentSelect} />
                </section>
                <RightSidebar
                    isKnowledge={true}
                    selectedDocument={selectedDocument}
                />
            </>
        );
    }

    // Check if there are any chats for the current user
    const hasUserChats = Object.values(chats)
        .filter((chat): chat is ChatType => chat !== undefined)
        .some((chat) => chat.userId === user?.id || chat.userId === null);

    console.log(`User ${user?.id || "anonymous"} has ${hasUserChats ? "some" : "no"} chats`);

    // Show empty state when welcome page is active (and no chat is selected) or there are no chats for the current user
    if ((showWelcomePage && !currentChatId) || !hasUserChats) {
        return <EmptyState />;
    }

    if (currentChatId) {
        return (
            <>
                <div className="flex flex-row h-full w-full">
                    <div className="flex h-screen flex-1 w-full">
                        <section className="flex-1 w-full flex flex-col bg-white">
                            <div className="flex-1 overflow-auto w-full">
                                <Messages />
                            </div>
                        </section>
                    </div>
                </div>
                <RightSidebar isKnowledge={false} />
            </>
        );
    }

    return (
        <section className="flex-1 flex flex-col p-12 bg-white overflow-auto items-center content-center">
            <h1 className="text-2xl font-semibold text-gray-900 mb-8">
                Enhance your <span className="text-[#2563EB]">productivity</span> with AI
            </h1>
            <ProductivitySection />
        </section>
    );
}

export default function Chat({ slug }: ChatProps) {
    const { status } = useSession();
    const { data: user } = useQuery({
        queryKey: ["user"],
        queryFn: getMe,
        enabled: status === "authenticated",
        staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
        retry: 3, // Retry failed requests 3 times
    });

    if (status !== "authenticated" || !user) {
        return null;
    }

    return (
        <ApiVersionProvider>
            <ChatContextProvider>
                <MessageInputProvider>
                    <div className="flex h-screen">
                        <section className="w-[270px] flex-shrink-0 bg-[#1A1A40] h-full overflow-hidden">
                            <LeftSidebar isKnowledge={slug === ChatSlug.KNOWLEDGE} />
                        </section>
                        <main className="flex-1 flex min-w-0 overflow-hidden">
                            <ChatContent isKnowledge={slug === ChatSlug.KNOWLEDGE} />
                        </main>
                    </div>
                </MessageInputProvider>
            </ChatContextProvider>
        </ApiVersionProvider>
    );
}
