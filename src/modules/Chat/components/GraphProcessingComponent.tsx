'use client';

import React, { useState, useEffect, useCallback, RefObject, useRef } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogDescription, Di<PERSON>Footer, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { t } from "@/i18n";
import { useToast } from "@/hooks/use-toast";
import { captureError } from "@/utils/errorHandling";
import { RefreshCw, Play, FastForward, Download } from "lucide-react";
import cookies from 'js-cookie';
import { CookieName } from '@/enums/CookieName';
import { ApiRequest } from '@/fetch/FetchRequest';
import { GraphResponse } from './Knowledge/GraphView/types';

// Backend response types
export interface ProcessStepResponse {
  success: boolean;
  details: string;
  error: string | null;
  traceback?: string;
}

export interface ProcessingStatus {
  collection: string;
  current_step: number;      // Last completed step
  active_step: number | null; // Currently processing step
  next_step: number;         // Next step to process
  completed_steps: number[];
  processing: boolean;       // Whether processing is happening
  last_updated: string | null;
  start_time: string | null;
  error: string | null;
}


interface GraphProcessingProps {
    onProcessingComplete?: () => void;
    wasmRef?: RefObject<HTMLObjectElement>;
}

// Constants
const TOTAL_STEPS = 10;
const POLLING_INTERVAL_IDLE = 5000;      // 5 seconds when idle
const POLLING_INTERVAL_ACTIVE = 1000;    // 1 second when processing

// Define types for the CustomDropdown props
interface CustomDropdownProps {
    value: string;
    onChange: (value: string) => void;
    options: string[];
    disabled?: boolean;
  }
  
  // CustomDropdown component with proper TypeScript types
  const CustomDropdown: React.FC<CustomDropdownProps> = ({ 
    value, 
    onChange, 
    options,
    disabled = false
  }) => {
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const dropdownRef = useRef<HTMLDivElement>(null);
  
    // Close dropdown when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
          setIsOpen(false);
        }
      };
      
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);
  
    return (
      <div className="relative" ref={dropdownRef}>
        <button
          className="w-60 h-9 px-3 bg-white text-black border border-gray-200 rounded-md flex items-center justify-between"
          onClick={() => setIsOpen(!isOpen)}
          disabled={disabled}
          type="button"
        >
          <span className="truncate">Collection: {value}</span>
          <svg width="12" height="12" viewBox="0 0 12 12" className="ml-2">
            <path d="M2 4L6 8L10 4" fill="none" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        </button>
        
        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-sm">
            {options.map((option) => (
              <div
                key={option}
                className={`px-3 py-2 cursor-pointer hover:bg-gray-50 ${option === value ? 'font-medium' : ''}`}
                onClick={() => {
                  onChange(option);
                  setIsOpen(false);
                }}
              >
                {option}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

export default function GraphProcessingComponent({ onProcessingComplete, wasmRef }: GraphProcessingProps) {
    // State
    const [backendState, setBackendState] = useState<ProcessingStatus | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [processingAllSteps, setProcessingAllSteps] = useState(false);
    const [selectedCollection, setSelectedCollection] = useState<string>("Default");
    const [collections, setCollections] = useState<string[]>(["Default"]);
    const [newCollectionName, setNewCollectionName] = useState<string>("");
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [isCopyDialogOpen, setIsCopyDialogOpen] = useState(false);
    const [copyTargetCollection, setCopyTargetCollection] = useState<string>("");
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    
    // Refs for managing polling and notifications
    const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
    const lastNotifiedStepRef = useRef<number>(0);
    const isComponentMountedRef = useRef<boolean>(true);
    const pendingAutoProgressStepRef = useRef<number | null>(null); // Track pending auto-progress step
    const autoProgressTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Track auto-progress timeout
    
    const { toast } = useToast();

    // Get step description helper
    const getStepDescription = (step: number) => {
        switch (step) {
            case 0: return t("Not started");
            case 1: return t("Initialize environment");
            case 2: return t("Process file nodes");
            case 3: return t("Process model file nodes");
            case 4: return t("Process tables");
            case 5: return t("Process identifiers");
            case 6: return t("Process instances");
            case 7: return t("Process enrichment matching");
            case 8: return t("Process enrichment models");
            case 9: return t("Process enrichment fuzzy");
            case 10: return t("Process join identifiers");
            default: return t("Unknown step");
        }
    };

    // Get step details helper
    const getStepDetails = (step: number) => {
        switch (step) {
            case 0: return t("Click 'Process step 1' to begin the graph extraction process one step at a time, or 'Process all steps' to run the entire sequence automatically without pausing.");
            case 1: return t("Initialize the graph processing environment");
            case 2: return t("Process file nodes for instances and establish initial connections");
            case 3: return t("Process file nodes for models and create connections");
            case 4: return t("Build data tables and model hierarchies from source files");
            case 5: return t("Process identifier mappings and link to table data");
            case 6: return t("Create instances from identifiers and build instance hierarchies");
            case 7: return t("Match related entities and establish enrichment links");
            case 8: return t("Enrich identifiers with model data and add model-based attributes");
            case 9: return t("Perform fuzzy matching between entities and create fuzzy relationships");
            case 10: return t("Join related identifiers and finalize the graph structure");
            default: return "";
        }
    };


    // Fetch available collections
    const fetchCollections = useCallback(async () => {
        try {
            const bearer = cookies.get(CookieName.API_TOKEN);
            const { data, response } = await ApiRequest<{collections: string[]}>(
                "collections",
                {
                    bearer,
                    init: {
                        method: "GET",
                        headers: { "Accept": "application/json" }
                    }
                }
            );


            
            if (response?.ok && data) {
                // Ensure "default" is always in the list
                const collectionsList = data.collections.includes("Default") 
                    ? data.collections 
                    : ["Default", ...data.collections];
                    
                setCollections(collectionsList.sort());
            }
        } catch (err) {
            console.error('Error fetching collections:', err);
            // Ensure at least "default" is available
            setCollections(["Default"]);
        }
    }, []);

    // Create new collection
    const handleCreateCollection = async () => {
        if (!newCollectionName.trim()) {
            toast({
                variant: "destructive",
                title: t("Invalid collection name"),
                description: t("Please enter a valid collection name"),
            });
            return;
        }

        try {
            const bearer = cookies.get(CookieName.API_TOKEN);
            const { data, response } = await ApiRequest<{success: boolean, error?: string}>(
                `collections?name=${encodeURIComponent(newCollectionName.trim())}`,
                {
                    bearer,
                    init: {
                        method: "POST",
                        headers: { "Accept": "application/json" }
                    }
                }
            );

            if (response?.ok && data && data.success) {
                toast({
                    title: t("Collection created"),
                    description: t(`Successfully created collection '${newCollectionName.trim()}'`),
                });
                
                // Update collections and select the new one
                await fetchCollections();
                setSelectedCollection(newCollectionName.trim());
                setNewCollectionName("");
                setIsCreateDialogOpen(false);
                
                // Refresh data for this collection
                await fetchProcessingStatus(newCollectionName.trim());
            } else {
                toast({
                    variant: "destructive",
                    title: t("Failed to create collection"),
                    description: data?.error || t("An error occurred"),
                });
            }
        } catch (err) {
            console.error('Error creating collection:', err);
            toast({
                variant: "destructive",
                title: t("Error"),
                description: t("Failed to create collection"),
            });
        }
    };

    // Copy collection
    const handleCopyCollection = async () => {
        if (!copyTargetCollection.trim()) {
            toast({
                variant: "destructive",
                title: t("Invalid target name"),
                description: t("Please enter a valid collection name"),
            });
            return;
        }

        try {
            const bearer = cookies.get(CookieName.API_TOKEN);
            const { data, response } = await ApiRequest<{success: boolean, error?: string, nodes_copied?: number}>(
                `copy_collection?source=${encodeURIComponent(selectedCollection)}&target=${encodeURIComponent(copyTargetCollection.trim())}`,
                {
                    bearer,
                    init: {
                        method: "POST",
                        headers: { "Accept": "application/json" }
                    }
                }
            );

            if (response?.ok && data && data.success) {
                toast({
                    title: t("Collection copied"),
                    description: t(`Successfully copied ${data.nodes_copied} nodes to '${copyTargetCollection.trim()}'`),
                });
                
                // Update collections and select the new one
                await fetchCollections();
                setSelectedCollection(copyTargetCollection.trim());
                setCopyTargetCollection("");
                setIsCopyDialogOpen(false);
                
                // Refresh data for the new collection
                await fetchProcessingStatus(copyTargetCollection.trim());
                
                // Update graph visualization with the new collection
                await fetchAndUpdateGraph(copyTargetCollection.trim());
            } else {
                toast({
                    variant: "destructive",
                    title: t("Failed to copy collection"),
                    description: data?.error || t("An error occurred"),
                });
            }
        } catch (err) {
            console.error('Error copying collection:', err);
            toast({
                variant: "destructive",
                title: t("Error"),
                description: t("Failed to copy collection"),
            });
        }
    };

    const handleDownloadGraph = useCallback(async () => {
        try {
          // Show loading toast
          toast({
            title: t("Preparing download"),
            description: t("Preparing graph data for download..."),
          });
          
          // Define node types to include
          const nodeTypes = ['artifact', 'entity', 'identifier', 'object', 'file', 'table', 'instance'];
          
          // Create URL with properly formatted query parameters
          const nodeTypesQueryString = nodeTypes.map(type => `node_types=${encodeURIComponent(type)}`).join('&');
          
          // Use the get_graph_view endpoint that we know works
          const url = `graph_query/get_graph_view?${nodeTypesQueryString}&base_relation=all&metadata_only=false&collection=${encodeURIComponent(selectedCollection)}`;
          
          console.log(`Fetching graph data for download: ${url}`);
          
          const bearer = cookies.get(CookieName.API_TOKEN);
          
          // Use ApiRequest since we know it works with this endpoint
          const response = await fetch(
            `/api/graph/${selectedCollection}`,
            {
              method: "GET",
              headers: {
                "Accept": "application/json"
              }
            }
          );
          
          const data: Record<string, any> = await response.json();
          
          if (!response?.ok || !data) {
            throw new Error("Failed to fetch graph data");
          }
          
          // Add export metadata
          if (!('_debug' in data)) {
            data._debug = {};
          }
          
          data._debug.export_date = new Date().toISOString();
          data._debug.collection = selectedCollection;
          data._debug.exported_by = "manual_download";
          
          // Create a JSON blob
          const jsonString = JSON.stringify(data, null, 2);
          const blob = new Blob([jsonString], { type: 'application/json' });
          const downloadUrl = URL.createObjectURL(blob);
          
          // Create filename
          const filename = `${selectedCollection}_graph_export.json`;
          
          // Trigger download
          const a = document.createElement('a');
          a.href = downloadUrl;
          a.download = filename;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          
          // Cleanup object URL
          URL.revokeObjectURL(downloadUrl);
          
          toast({
            title: t("Download complete"),
            description: t(`Graph data for '${selectedCollection}' collection has been downloaded`),
          });
        } catch (err) {
          console.error('Error downloading graph:', err);
          captureError(err, {
            action: 'downloadGraph',
            component: 'GraphProcessingComponent'
          });
          
          toast({
            variant: "destructive",
            title: t("Download failed"),
            description: t("Failed to download graph data. Please try again."),
          });
        }
      }, [selectedCollection, toast, t]);

    // Delete collection
    const handleDeleteCollection = async () => {
        // Don't allow deleting the default collection
        if (selectedCollection === "Default") {
            toast({
                variant: "destructive",
                title: t("Cannot delete Default"),
                description: t("The Default collection cannot be deleted"),
            });
            setIsDeleteDialogOpen(false);
            return;
        }

        try {
            const bearer = cookies.get(CookieName.API_TOKEN);
            const { data, response } = await ApiRequest<{success: boolean, error?: string}>(
                `collections/${encodeURIComponent(selectedCollection)}`,
                {
                    bearer,
                    init: {
                        method: "DELETE",
                        headers: { "Accept": "application/json" }
                    }
                }
            );

            if (response?.ok && data && data.success) {
                toast({
                    title: t("Collection deleted"),
                    description: t(`Successfully deleted collection '${selectedCollection}'`),
                });
                
                // Update collections and select default
                await fetchCollections();
                setSelectedCollection("Default");
                setIsDeleteDialogOpen(false);
                
                // Refresh data for default collection
                await fetchProcessingStatus("Default");
                
                // Update graph visualization to default
                await fetchAndUpdateGraph("Default");
            } else {
                toast({
                    variant: "destructive",
                    title: t("Failed to delete collection"),
                    description: data?.error || t("An error occurred"),
                });
            }
        } catch (err) {
            console.error('Error deleting collection:', err);
            toast({
                variant: "destructive",
                title: t("Error"),
                description: t("Failed to delete collection"),
            });
        }
    };

    // Function to update WASM visualization
    const fetchAndUpdateGraph = useCallback(async (collection?: string) => {
        try {
            const bearer = cookies.get(CookieName.API_TOKEN);
            
            // Define node types to include
            const nodeTypes = ['artifact', 'entity', 'identifier', 'object', 'file', 'table', 'instance'];
            
            // Create URL with properly formatted query parameters
            const nodeTypesQueryString = nodeTypes.map(type => `node_types=${encodeURIComponent(type)}`).join('&');
            const collectionToUse = collection || selectedCollection;
            const url = `graph_query/get_graph_view?${nodeTypesQueryString}&base_relation=all&metadata_only=false&collection=${encodeURIComponent(collectionToUse)}`;
            
            console.log(`Fetching updated graph data for collection: ${collectionToUse}`);
            
            const { data, response } = await ApiRequest<GraphResponse>(
                url, 
                {
                    bearer,
                    init: {
                        method: "GET",
                        headers: {
                            "Accept": "application/json"
                        }
                    }
                }
            );
            
            if (response?.ok && data) {
                console.log("Received fresh graph data");
                
                // Send data to WASM app if available
                if (wasmRef?.current?.contentWindow?.wasm_app) {
                    console.log("Updating WASM graph visualization");
                    wasmRef.current.contentWindow.wasm_app.new_graph_data(JSON.stringify(data));
                } else {
                    console.warn("WASM app not available for graph update");
                }
            } else {
                console.error("Failed to fetch updated graph data:", response?.statusText);
            }
        } catch (err) {
            console.error('Error fetching updated graph data:', err);
            captureError(err, {
                action: 'fetchAndUpdateGraph',
                component: 'GraphProcessingComponent'
            });
        }
    }, [selectedCollection, wasmRef]);
    
    // Fetch processing status from backend
    const fetchProcessingStatus = useCallback(async (collection?: string) => {
        // Skip if component is unmounted
        if (!isComponentMountedRef.current) return;
        
        const collectionToUse = collection || selectedCollection;
        
        try {
            const bearer = cookies.get(CookieName.API_TOKEN);
            const url = `processing_status?collection=${encodeURIComponent(collectionToUse)}`;
            
            const { data, response } = await ApiRequest<ProcessingStatus>(
                url,
                {
                    bearer,
                    init: {
                        method: "GET",
                        headers: {
                            "Accept": "application/json"
                        }
                    }
                }
            );
            
            if (response?.ok && data) {
                // Only update state if this is for the currently selected collection
                if (collectionToUse === selectedCollection) {
                    console.log(`Fetched processing status for collection '${collectionToUse}':`, data);
                    
                    // Store previous state before updating
                    const prevState = backendState;
                    const prevStep = prevState?.current_step || 0;
                    
                    // Update state with new data
                    setBackendState(data);
                    setIsLoading(false);
                    
                    // Check if a step completed (current_step increased)
                    const stepCompleted = prevState && data.current_step > prevStep;
                    
                    // If a step completed, update the graph visualization and show toast (only once)
                    if (stepCompleted && data.current_step > lastNotifiedStepRef.current) {
                        console.log(`Step ${data.current_step} just completed, updating graph`);
                        fetchAndUpdateGraph();
                        
                        // Update last notified step
                        lastNotifiedStepRef.current = data.current_step;
                        
                        toast({
                            title: t("Step completed"),
                            description: t(`Successfully processed step ${data.current_step}: ${getStepDescription(data.current_step)}`),
                        });
                    }
                    
                    // Handle errors
                    if (data.error && (!prevState || prevState.error !== data.error)) {
                        setError(data.error);
                        
                        toast({
                            variant: "destructive",
                            title: t("Processing error"),
                            description: data.error,
                        });
                        
                        setProcessingAllSteps(false);
                    }
                    
                    // If all steps are completed, notify parent
                    if (data.completed_steps.length === TOTAL_STEPS && onProcessingComplete) {
                        onProcessingComplete();
                        
                        // Show toast for all steps completed (only if in process all mode)
                        if (processingAllSteps) {
                            toast({
                                title: t("Processing complete"),
                                description: t("All processing steps have been completed successfully."),
                            });
                            setProcessingAllSteps(false);
                        }
                    }
                    
                    // Handle auto-progression for Process All
                    if (processingAllSteps && 
                        !data.processing && 
                        data.current_step < TOTAL_STEPS && 
                        !data.error) {
                        
                        const nextStep = data.current_step + 1;
                        
                        // Only schedule auto-progress if there isn't one already pending for this step
                        if (pendingAutoProgressStepRef.current !== nextStep) {
                            console.log(`Auto-proceeding to next step ${nextStep}`);
                            
                            // Cancel any existing timeout
                            if (autoProgressTimeoutRef.current) {
                                clearTimeout(autoProgressTimeoutRef.current);
                                autoProgressTimeoutRef.current = null;
                            }
                            
                            // Mark this step as pending
                            pendingAutoProgressStepRef.current = nextStep;
                            
                            // Use a longer timeout to let the backend settle
                            autoProgressTimeoutRef.current = setTimeout(() => {
                                if (isComponentMountedRef.current) {  // Check if still mounted
                                    // Double-check backend state before proceeding
                                    ApiRequest<ProcessingStatus>(
                                        `processing_status?collection=${encodeURIComponent(collectionToUse)}`,
                                        {
                                            bearer: cookies.get(CookieName.API_TOKEN),
                                            init: {
                                                method: "GET",
                                                headers: { "Accept": "application/json" }
                                            }
                                        }
                                    ).then(({ data }) => {
                                        // Only proceed if the backend is still in the expected state
                                        if (data && 
                                            !data.processing && 
                                            data.current_step === nextStep - 1 && 
                                            processingAllSteps) {
                                            processNextStep(nextStep);
                                        } else {
                                            console.log("Backend state changed, skipping auto-progress");
                                        }
                                        // Clear pending step after checking
                                        pendingAutoProgressStepRef.current = null;
                                    }).catch(err => {
                                        console.error("Error checking status before auto-progress:", err);
                                        pendingAutoProgressStepRef.current = null;
                                    });
                                } else {
                                    pendingAutoProgressStepRef.current = null;
                                }
                                autoProgressTimeoutRef.current = null;
                            }, 3000); // Longer timeout (3 seconds)
                        }
                    }
                }
            } else {
                console.error("Failed to fetch status:", response?.statusText);
                // Only update loading state for the current collection
                if (collectionToUse === selectedCollection) {
                    setIsLoading(false);
                }
            }
        } catch (err) {
            console.error('Error fetching processing status:', err);
            captureError(err, {
                action: 'fetchProcessingStatus',
                component: 'GraphProcessingComponent'
            });
            // Only update loading state for the current collection
            if (collectionToUse === selectedCollection) {
                setIsLoading(false);
            }
        }
    }, [backendState, fetchAndUpdateGraph, onProcessingComplete, processingAllSteps, selectedCollection, toast]);

    // Process a single step
    const handleProcessStep = async () => {
        // Disable immediately to prevent double clicks
        setIsLoading(true);
        
        try {
            // Check if processing is already happening
            if (backendState?.processing) {
                toast({
                    variant: "destructive",
                    title: t("Processing already in progress"),
                    description: t("Please wait for the current processing to complete before starting a new step."),
                });
                return;
            }
            
            setError(null);
            
            // Determine step to process
            const stepToProcess = !backendState || backendState.current_step === 0 ? 
                1 : backendState.current_step + 1;
                
            // Don't process beyond the last step
            if (stepToProcess > TOTAL_STEPS) {
                console.log("All steps already completed");
                return;
            }
                
            console.log(`Processing step ${stepToProcess} for collection '${selectedCollection}'`);
            
            const bearer = cookies.get(CookieName.API_TOKEN);
            const url = `process_step?step=${stepToProcess}&collection=${encodeURIComponent(selectedCollection)}`;
            
            const { data, response } = await ApiRequest<ProcessStepResponse>(
                url, 
                {
                    bearer,
                    init: {
                        method: "POST",
                        headers: {
                            "Accept": "application/json"
                        }
                    }
                }
            );
            
            if (response?.ok && data) {
                if (data.success) {
                    console.log(`Successfully started step ${stepToProcess}: ${data.details}`);
                    
                    toast({
                        title: t("Step started"),
                        description: t(`Started processing step ${stepToProcess}: ${getStepDescription(stepToProcess)}`),
                    });
                    
                    // Refresh status to get updated state from backend
                    await fetchProcessingStatus();
                } else if (data.error) {
                    // Handle case where processing is already happening
                    if (data.error.includes("already in progress")) {
                        toast({
                            variant: "destructive",
                            title: t("Processing already in progress"),
                            description: t("Please wait for the current processing to complete before starting a new step."),
                        });
                    } else {
                        setError(data.error);
                        toast({
                            variant: "destructive",
                            title: t("Step failed"),
                            description: data.error,
                        });
                    }
                }
            } else {
                const errorMsg = response?.statusText || t('Unknown error occurred');
                console.error(`Failed to process step ${stepToProcess}:`, errorMsg);
                setError(errorMsg);
                
                toast({
                    variant: "destructive",
                    title: t("Step failed"),
                    description: errorMsg,
                });
            }
        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.error('Error in handleProcessStep:', errorMsg);
            
            captureError(error, {
                action: 'processStep',
                component: 'GraphProcessingComponent'
            });
            
            setError(`${t('Error processing step')}: ${errorMsg}`);
            
            toast({
                variant: "destructive",
                title: t("Processing error"),
                description: t('Failed to process step. Please try again.'),
            });
        } finally {
            setIsLoading(false);
        }
    };

    // Process all remaining steps
    const processAllSteps = async () => {
        // Disable immediately to prevent double clicks
        setIsLoading(true);
        
        try {
            // Clear any pending auto-progress
            if (autoProgressTimeoutRef.current) {
                clearTimeout(autoProgressTimeoutRef.current);
                autoProgressTimeoutRef.current = null;
            }
            pendingAutoProgressStepRef.current = null;
            
            // Fetch latest status to ensure we have current state
            const bearer = cookies.get(CookieName.API_TOKEN);
            const { data: latestStatus } = await ApiRequest<ProcessingStatus>(
                `processing_status?collection=${encodeURIComponent(selectedCollection)}`,
                {
                    bearer,
                    init: {
                        method: "GET",
                        headers: { "Accept": "application/json" }
                    }
                }
            );

            // Check if processing is already happening based on latest state
            if (latestStatus?.processing) {
                toast({
                    variant: "destructive",
                    title: t("Processing already in progress"),
                    description: t("Please wait for the current processing to complete before starting new steps."),
                });
                setIsLoading(false);
                return;
            }
            
            setProcessingAllSteps(true);
            setError(null);
            
            // Determine starting step from latest state
            const startStep = !latestStatus || latestStatus.current_step === 0 ? 
                1 : latestStatus.current_step + 1;
                
            // Don't process beyond the last step
            if (startStep > TOTAL_STEPS) {
                console.log("All steps already completed");
                setProcessingAllSteps(false);
                setIsLoading(false);
                return;
            }
                
            console.log(`Starting process for step ${startStep} in collection '${selectedCollection}'`);
            
            // Flag this step as pending auto-progress to prevent duplicates
            pendingAutoProgressStepRef.current = startStep;
            
            // Start with the first step - the fetchProcessingStatus will handle
            // triggering subsequent steps when each step completes
            await processNextStep(startStep);
            
            // Clear pending flag after initiating the first step
            setTimeout(() => {
                pendingAutoProgressStepRef.current = null;
            }, 1000);
            
            toast({
                title: t("Processing started"),
                description: t(`Started processing all remaining steps beginning with step ${startStep}`),
            });
        } catch (error) {
            pendingAutoProgressStepRef.current = null;
            
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.error('Error in processAllSteps:', errorMsg);
            
            captureError(error, {
                action: 'processAllSteps',
                component: 'GraphProcessingComponent'
            });
            
            setError(`${t('Error processing steps')}: ${errorMsg}`);
            
            toast({
                variant: "destructive",
                title: t("Processing error"),
                description: t('Failed to process all steps. Please try again.'),
            });
            
            setProcessingAllSteps(false);
        } finally {
            setIsLoading(false);
        }
    };

    // Process a single step - helper function for processAllSteps
    const processNextStep = async (stepToProcess: number) => {
        try {
            console.log(`Auto-processing next step ${stepToProcess} for collection '${selectedCollection}'`);
            
            // Extra safety check - ensure we don't process steps that are pending
            if (pendingAutoProgressStepRef.current === stepToProcess && 
                pendingAutoProgressStepRef.current !== null && 
                autoProgressTimeoutRef.current !== null) {
                console.log(`Step ${stepToProcess} is already scheduled, skipping duplicate request`);
                return;
            }
            
            // Ensure we don't try to process steps beyond the total
            if (stepToProcess > TOTAL_STEPS) {
                console.log("All steps already completed");
                setProcessingAllSteps(false);
                pendingAutoProgressStepRef.current = null;
                return;
            }
            
            // Get latest status to double-check if we should proceed
            const bearer = cookies.get(CookieName.API_TOKEN);
            const statusUrl = `processing_status?collection=${encodeURIComponent(selectedCollection)}`;
            
            const { data: latestStatus } = await ApiRequest<ProcessingStatus>(
                statusUrl,
                {
                    bearer,
                    init: {
                        method: "GET",
                        headers: { "Accept": "application/json" }
                    }
                }
            );
            
            // If processing is already happening or the step has changed, abort
            if (latestStatus?.processing) {
                console.log(`Processing already in progress, not starting step ${stepToProcess}`);
                return;
            }
            
            // Verify this is still the correct next step
            const expectedPrevStep = stepToProcess - 1;
            if (latestStatus && latestStatus.current_step !== expectedPrevStep) {
                console.log(`Expected previous step ${expectedPrevStep}, but found ${latestStatus.current_step}. Aborting.`);
                return;
            }
            
            // Now proceed with the step
            const url = `process_step?step=${stepToProcess}&collection=${encodeURIComponent(selectedCollection)}`;
            
            const { data, response } = await ApiRequest<ProcessStepResponse>(
                url, 
                {
                    bearer,
                    init: {
                        method: "POST",
                        headers: {
                            "Accept": "application/json"
                        }
                    }
                }
            );
            
            if (response?.ok && data) {
                if (data.success) {
                    console.log(`Successfully started step ${stepToProcess}`);
                    // Fetch status immediately after starting the step
                    await fetchProcessingStatus();
                } else if (data.error) {
                    // If we get "already in progress", just log and continue
                    if (data.error.includes("already in progress")) {
                        console.log(`Step ${stepToProcess} is already in progress, will continue polling`);
                    } else {
                        // For other errors, stop auto-processing
                        console.error(`Error starting step ${stepToProcess}: ${data.error}`);
                        setProcessingAllSteps(false);
                        pendingAutoProgressStepRef.current = null;
                        
                        toast({
                            variant: "destructive",
                            title: t("Auto-processing stopped"),
                            description: t(`Failed to process step ${stepToProcess}: ${data.error}`),
                        });
                    }
                }
            } else {
                const errorMsg = response?.statusText || t('Unknown error occurred');
                console.error(`Failed to process step ${stepToProcess}: ${errorMsg}`);
                setProcessingAllSteps(false);
                pendingAutoProgressStepRef.current = null;
                
                toast({
                    variant: "destructive",
                    title: t("Auto-processing stopped"),
                    description: t(`Failed to process step ${stepToProcess}. Please try again manually.`),
                });
            }
        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.error(`Error auto-processing step ${stepToProcess}:`, errorMsg);
            setProcessingAllSteps(false);
            pendingAutoProgressStepRef.current = null;
            
            toast({
                variant: "destructive",
                title: t("Auto-processing stopped"),
                description: t(`Failed to process step ${stepToProcess}. Please try again manually.`),
            });
        }
    };

    // Reset processing
    const resetState = async () => {
        // Disable immediately to prevent double clicks
        setIsLoading(true);
        
        try {
            // Check if processing is already happening
            if (backendState?.processing) {
                toast({
                    variant: "destructive",
                    title: t("Processing in progress"),
                    description: t("Stopping current processing and resetting..."),
                });
            }
            
            setError(null);
            
            // Clear flags to prevent auto-processing
            setProcessingAllSteps(false);
            
            // Clear any pending auto-progress
            if (autoProgressTimeoutRef.current) {
                clearTimeout(autoProgressTimeoutRef.current);
                autoProgressTimeoutRef.current = null;
            }
            pendingAutoProgressStepRef.current = null;
            
            console.log(`Stopping any active processing for collection '${selectedCollection}'`);
            
            // First stop any active processing
            const bearer = cookies.get(CookieName.API_TOKEN);
            const stopUrl = `stop_processing?collection=${encodeURIComponent(selectedCollection)}`;
            
            const stopResponse = await ApiRequest<{stopped: boolean}>(
                stopUrl, 
                {
                    bearer,
                    init: {
                        method: "POST",
                        headers: {
                            "Accept": "application/json"
                        }
                    }
                }
            );
            
            // Log whether we successfully stopped processing
            if (stopResponse.data?.stopped) {
                console.log("Successfully stopped processing");
            } else {
                console.log("No active processing to stop");
            }
            
            // Small delay to ensure processing stops
            await new Promise(r => setTimeout(r, 1000));
            
            // Then reset processing state
            const resetUrl = `reset_processing?step=0&collection=${encodeURIComponent(selectedCollection)}`;
            
            const { data, response } = await ApiRequest<ProcessStepResponse>(
                resetUrl, 
                {
                    bearer,
                    init: {
                        method: "POST",
                        headers: {
                            "Accept": "application/json"
                        }
                    }
                }
            );
            
            if (response?.ok && data && data.success) {
                console.log(`Successfully reset processing state for collection '${selectedCollection}'`);
                
                toast({
                    title: t("Reset complete"),
                    description: t("The processing state has been reset to the beginning."),
                });
                
                // Reset last notified step
                lastNotifiedStepRef.current = 0;
                
                // Refresh status to get updated state from backend
                await fetchProcessingStatus();
                
                // Clear the graph visualization
                if (wasmRef?.current?.contentWindow?.wasm_app) {
                    wasmRef.current.contentWindow.wasm_app.new_graph_data(JSON.stringify({nodes: [], edges: []}));
                }
            } else {
                const errorMsg = data?.error || response?.statusText || t('Reset failed');
                console.error("Failed to reset processing:", errorMsg);
                setError(errorMsg);
                
                toast({
                    variant: "destructive",
                    title: t("Reset failed"),
                    description: t("Failed to reset processing state. Please try again."),
                });
            }
        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.error('Error in resetState:', errorMsg);
            
            captureError(error, {
                action: 'resetState',
                component: 'GraphProcessingComponent'
            });
            
            setError(`${t('Error resetting state')}: ${errorMsg}`);
            
            toast({
                variant: "destructive",
                title: t("Reset failed"),
                description: t("Failed to reset processing state. Please try again."),
            });
        } finally {
            setIsLoading(false);
        }
    };

    // Handle collection change
    const handleCollectionChange = async (newCollection: string) => {
        if (newCollection === selectedCollection) return;
        
        // Update selected collection
        setSelectedCollection(newCollection);
        
        // Reset state and load new collection data
        setBackendState(null);
        setIsLoading(true);
        setError(null);
        lastNotifiedStepRef.current = 0;
        
        // Fetch data for the new collection
        await fetchProcessingStatus(newCollection);        
        // Update graph visualization for the new collection
        await fetchAndUpdateGraph(newCollection);
    };

    // Set up polling with proper cleanup
    useEffect(() => {
        // Mark component as mounted
        isComponentMountedRef.current = true;
        
        // Initial data fetch
        fetchCollections();
        fetchProcessingStatus();
        
        // Define function to start/update polling
        const updatePolling = () => {
            // Clear any existing interval
            if (pollingIntervalRef.current) {
                clearInterval(pollingIntervalRef.current);
            }
            
            // Determine appropriate interval
            const isActive = backendState?.processing || processingAllSteps;
            const interval = isActive ? POLLING_INTERVAL_ACTIVE : POLLING_INTERVAL_IDLE;
            
            // Create new interval
            pollingIntervalRef.current = setInterval(() => {
                if (isComponentMountedRef.current) {  // Only fetch if component is mounted
                    fetchProcessingStatus();
                    
                    // Only fetch collection stats occasionally
                    if (Math.random() < 0.2) { // 20% chance each poll
                    }
                }
            }, interval);
        };
        
        // Set up initial polling
        updatePolling();
        
        // Clean up on unmount
        return () => {
            isComponentMountedRef.current = false;
            
            if (pollingIntervalRef.current) {
                clearInterval(pollingIntervalRef.current);
                pollingIntervalRef.current = null;
            }
            
            if (autoProgressTimeoutRef.current) {
                clearTimeout(autoProgressTimeoutRef.current);
                autoProgressTimeoutRef.current = null;
            }
            
            pendingAutoProgressStepRef.current = null;
        };
    }, []);

    // Update polling interval when relevant state changes
    useEffect(() => {
        // Skip if component is unmounted
        if (!isComponentMountedRef.current) return;
        
        // Clear existing interval
        if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
        }
        
        // Determine appropriate interval
        const isActive = backendState?.processing || processingAllSteps;
        const interval = isActive ? POLLING_INTERVAL_ACTIVE : POLLING_INTERVAL_IDLE;
        
        // Create new interval
        pollingIntervalRef.current = setInterval(() => {
            if (isComponentMountedRef.current) {  // Only fetch if component is mounted
                fetchProcessingStatus();
            }
        }, interval);
        
        // Clean up on change
        return () => {
            if (pollingIntervalRef.current) {
                clearInterval(pollingIntervalRef.current);
            }
        };
    }, [backendState?.processing, processingAllSteps, selectedCollection]);

    // Derived values
    const isProcessing = backendState?.processing || false;
    const currentStep = backendState?.current_step || 0;
    const completedSteps = backendState?.completed_steps || [];
    const nextStep = currentStep < TOTAL_STEPS ? currentStep + 1 : currentStep;
    const progress = (completedSteps.length / TOTAL_STEPS) * 100;
    const isInitialized = currentStep > 0 || completedSteps.length > 0;
    const isCompleted = completedSteps.length === TOTAL_STEPS || currentStep >= TOTAL_STEPS;

    // UI Flags
    const isStepButtonDisabled = isLoading || isProcessing || isCompleted || processingAllSteps;
    const isAllButtonDisabled = isLoading || isProcessing || isCompleted || currentStep >= TOTAL_STEPS || processingAllSteps;
    const isResetButtonDisabled = isLoading || isProcessing || processingAllSteps;

    return (
        <div className="space-y-4 border rounded-md p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <h3 className="text-lg font-medium">{t("Graph Processing")}</h3>
              {(isProcessing || isLoading || processingAllSteps) && (
                <div className="flex items-center">
                  <svg className="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              )}
            </div>
            
            <div className="flex justify-end items-center gap-2">
              <CustomDropdown
                value={selectedCollection}
                onChange={handleCollectionChange}
                options={collections}
                disabled={isProcessing || processingAllSteps}
              />
              
              <Button
                variant="outline"
                size="sm"
                onClick={resetState}
                disabled={isResetButtonDisabled}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                <span>{t("Reset processing")}</span>
              </Button>
            </div>
          </div>
      

            <div className="space-y-2">
                <div className="flex justify-between items-center">
                    <p className="text-sm text-muted-foreground">
                        {t("Progress")}: {completedSteps.length}/{TOTAL_STEPS} {t("steps")} ({progress.toFixed(1)}%)
                    </p>
                    <p className="text-sm font-medium">
                        {isCompleted ? 
                            t("Completed") : 
                            isInitialized ? 
                                isProcessing ?
                                    `${t("Processing")}: ${getStepDescription(backendState?.active_step || nextStep)}` :
                                    `${t("Next Step")}: ${getStepDescription(nextStep)}` :
                                t("Not started - click Process Step to begin")}
                    </p>
                </div>
                
                {/* Progress bar with natural light-to-dark pulsing effect */}
                <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                    <div 
                        className="relative h-2 transition-all duration-500 ease-in-out bg-blue-500"
                        style={{ width: `${progress}%` }}
                    >
                        {(isProcessing || processingAllSteps) && (
                            <div className="absolute inset-0 animate-bar-pulse"></div>
                        )}
                    </div>
                </div>
                
                {/* Add styles for the natural pulsing animation */}
                <style jsx>{`
                    @keyframes bar-pulse {
                        0% { background-color: rgba(59, 130, 246, 0.7); }
                        50% { background-color: rgba(37, 99, 235, 1); }
                        100% { background-color: rgba(59, 130, 246, 0.7); }
                    }
                    .animate-bar-pulse {
                        animation: bar-pulse 2s ease-in-out infinite;
                    }
                `}</style>
            </div>

            {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 p-3 rounded-md text-sm">
                    <strong>{t("Error")}:</strong> {error}
                </div>
            )}

            <div className="flex justify-between items-center">
                <div className="flex space-x-4">
                    <Button
                        onClick={handleProcessStep}
                        disabled={isStepButtonDisabled}
                        className="flex items-center gap-2"
                        type="button"
                    >
                        <Play className="h-4 w-4" />
                        {isProcessing ? 
                            t("Processing...") : 
                            currentStep === 0 ?
                                t("Process Step 1") :
                                `${t("Process Step")} ${nextStep}`}
                    </Button>
                    
                    <Button
                        onClick={processAllSteps}
                        disabled={isAllButtonDisabled}
                        className="flex items-center gap-2"
                        type="button"
                    >
                        <FastForward className="h-4 w-4" />
                        {processingAllSteps ? 
                            t("Processing...") : 
                            currentStep === 0 ?
                                t("Process All Steps") :
                                t("Process Remaining Steps")}
                    </Button>
                </div>
                
                <Button
                    onClick={handleDownloadGraph}
                    disabled={!isInitialized}
                    className="flex items-center gap-2"
                    variant="outline"
                    type="button"
                >
                    <Download className="h-4 w-4 mr-2" />
                    <span>{t("Download Graph")}</span>
                </Button>
            </div>

            {!isCompleted && (
                <div className={`bg-blue-50 border border-blue-200 p-3 rounded-md ${(isProcessing || processingAllSteps) ? 'shadow-sm' : ''}`}>
                    <h4 className="text-sm font-medium text-blue-700 mb-1">
                        {currentStep === 0 ? 
                            t("Start Processing") : 
                            isProcessing ?
                                `${t("Processing")}: ${getStepDescription(backendState?.active_step || nextStep)}` :
                                `${t("Next")}: ${getStepDescription(nextStep)}`}
                    </h4>
                    <p className="text-xs text-blue-600">
                        {currentStep === 0 ? 
                            getStepDetails(0) : 
                            getStepDetails(nextStep)}
                    </p>
                </div>
            )}
            
            {/* Create collection dialog */}
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>{t("Create New Collection")}</DialogTitle>
                        <DialogDescription>
                            {t("Enter a name for your new collection.")}
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                        <Input
                            value={newCollectionName}
                            onChange={(e) => setNewCollectionName(e.target.value)}
                            placeholder={t("Collection name")}
                            className="w-full"
                        />
                    </div>
                    <DialogFooter>
                        <Button 
                            variant="outline" 
                            onClick={() => setIsCreateDialogOpen(false)}
                        >
                            {t("Cancel")}
                        </Button>
                        <Button 
                            onClick={handleCreateCollection}
                            disabled={!newCollectionName.trim()}
                        >
                            {t("Create")}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
            
            {/* Copy collection dialog */}
            <Dialog open={isCopyDialogOpen} onOpenChange={setIsCopyDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>{t("Copy Collection")}</DialogTitle>
                        <DialogDescription>
                            {t(`Copy collection '${selectedCollection}' to a new collection.`)}
                        </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                        <Input
                            value={copyTargetCollection}
                            onChange={(e) => setCopyTargetCollection(e.target.value)}
                            placeholder={t("Target collection name")}
                            className="w-full"
                        />
                    </div>
                    <DialogFooter>
                        <Button 
                            variant="outline" 
                            onClick={() => setIsCopyDialogOpen(false)}
                        >
                            {t("Cancel")}
                        </Button>
                        <Button 
                            onClick={handleCopyCollection}
                            disabled={!copyTargetCollection.trim()}
                        >
                            {t("Copy")}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
            
            {/* Delete collection confirmation dialog */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>{t("Delete Collection")}</DialogTitle>
                        <DialogDescription>
                            {t(`Are you sure you want to delete collection '${selectedCollection}'? This action cannot be undone.`)}
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button 
                            variant="outline" 
                            onClick={() => setIsDeleteDialogOpen(false)}
                        >
                            {t("Cancel")}
                        </Button>
                        <Button 
                            variant="destructive"
                            onClick={handleDeleteCollection}
                        >
                            {t("Delete")}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}