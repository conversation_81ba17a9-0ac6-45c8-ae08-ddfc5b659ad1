"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Download } from "lucide-react";
import { t } from "@/i18n";
import { Document, Page, Text, View, StyleSheet, PDFDownloadLink, BlobProviderParams } from "@react-pdf/renderer";
import { marked } from "marked";
import { useCallback, useEffect, useMemo, useState, useRef } from "react";

// Define pattern types
type StylePattern = {
    regex: RegExp;
    style: keyof typeof styles;
};

type LinkPattern = {
    regex: RegExp;
    type: "link";
};

type Pattern = StylePattern | LinkPattern;

// Compile regex patterns once
const PATTERNS: Pattern[] = [
    { regex: /\*\*(.+?)\*\*/g, style: "bold" },
    { regex: /_(.+?)_/g, style: "italic" },
    { regex: /`(.+?)`/g, style: "inlineCode" },
    { regex: /\[(.+?)\]\((.+?)\)/g, type: "link" },
] as const;

const styles = StyleSheet.create({
    page: {
        flexDirection: "column",
        backgroundColor: "#fff",
        padding: 30,
    },
    section: {
        margin: 10,
        padding: 10,
    },
    heading: {
        fontSize: 16,
        fontWeight: "bold",
        marginBottom: 10,
        color: "#2563eb",
    },
    text: {
        fontSize: 12,
        marginBottom: 10,
        lineHeight: 1.5,
    },
    bold: {
        fontWeight: "bold",
    },
    italic: {
        fontStyle: "italic",
    },
    inlineCode: {
        fontFamily: "Courier",
        backgroundColor: "#f3f4f6",
        padding: "0 4",
        fontSize: 11,
        color: "#ef4444",
    },
    code: {
        fontFamily: "Courier",
        backgroundColor: "#f3f4f6",
        padding: 8,
        fontSize: 11,
        marginVertical: 8,
        borderRadius: 4,
    },
    blockquote: {
        borderLeftWidth: 3,
        borderLeftColor: "#3b82f6",
        paddingLeft: 10,
        marginLeft: 5,
        marginVertical: 8,
        backgroundColor: "#f8fafc",
    },
    blockquoteText: {
        color: "#4b5563",
        fontStyle: "italic",
    },
    listItem: {
        flexDirection: "row",
        marginBottom: 5,
        alignItems: "flex-start",
    },
    bullet: {
        width: 15,
        marginRight: 5,
    },
    link: {
        color: "#2563eb",
        textDecoration: "underline",
    },
    hr: {
        borderBottomWidth: 1,
        borderBottomColor: "#e5e7eb",
        marginVertical: 15,
    },
});

const renderInlineContent = (text: string) => {
    if (!text || text.length > 10000) {
        // Prevent processing extremely long text
        return [<Text key={0}>{text.slice(0, 10000) + (text.length > 10000 ? "..." : "")}</Text>];
    }

    const parts = [];
    let currentIndex = 0;

    while (currentIndex < text.length) {
        let earliestMatch: {
            index: number;
            length: number;
            content: string;
            style?: (typeof styles)[keyof typeof styles];
            type?: string;
            url?: string;
        } = {
            index: text.length,
            length: 0,
            content: "",
        };

        // Find the earliest match among all patterns
        for (const pattern of PATTERNS) {
            pattern.regex.lastIndex = currentIndex;
            const match = pattern.regex.exec(text);
            if (match && match.index < earliestMatch.index) {
                earliestMatch = {
                    index: match.index,
                    length: match[0].length,
                    content: match[1],
                    ...("style" in pattern ? { style: styles[pattern.style] } : {}),
                    ...("type" in pattern ? { type: pattern.type } : {}),

                    ...(match[2] ? { url: match[2] } : {}),
                };
            }
        }

        // Add text before the match
        if (earliestMatch.index > currentIndex) {
            parts.push(<Text key={parts.length}>{text.slice(currentIndex, earliestMatch.index)}</Text>);
        }

        // Add the matched content with appropriate styling
        if (earliestMatch.content) {
            if (earliestMatch.type === "link") {
                parts.push(
                    <Text
                        key={parts.length}
                        style={styles.link}
                    >
                        {earliestMatch.content}
                    </Text>,
                );
            } else if (earliestMatch.style) {
                parts.push(
                    <Text
                        key={parts.length}
                        style={earliestMatch.style}
                    >
                        {earliestMatch.content}
                    </Text>,
                );
            }
        }

        currentIndex = earliestMatch.index + earliestMatch.length;
        if (currentIndex === text.length) break;
    }

    return parts.length > 0 ? parts : [<Text key={0}>{text}</Text>];
};

const parseMarkdownContent = (content: string) => {
    if (!content) return null;

    const tokens = marked.lexer(content);
    // Limit the number of tokens to prevent excessive processing
    const maxTokens = 1000;
    return tokens.slice(0, maxTokens).map((token, index) => {
        switch (token.type) {
            case "paragraph":
                return (
                    <Text
                        key={index}
                        style={styles.text}
                    >
                        {renderInlineContent(token.text)}
                    </Text>
                );
            case "code":
                return (
                    <View
                        key={index}
                        style={styles.code}
                    >
                        <Text style={{ color: "#374151" }}>{token.text.length > 5000 ? token.text.slice(0, 5000) + "..." : token.text}</Text>
                    </View>
                );
            case "blockquote":
                return (
                    <View
                        key={index}
                        style={styles.blockquote}
                    >
                        <Text style={[styles.text, styles.blockquoteText]}>{renderInlineContent(token.text)}</Text>
                    </View>
                );
            case "list":
                return (
                    <View key={index}>
                        {token.items.slice(0, 100).map((item: any, itemIndex: number) => (
                            <View
                                key={itemIndex}
                                style={styles.listItem}
                            >
                                <Text style={styles.bullet}>{token.ordered ? `${itemIndex + 1}.` : "•"}</Text>
                                <Text style={styles.text}>{renderInlineContent(item.text)}</Text>
                            </View>
                        ))}
                        {token.items.length > 100 && <Text style={styles.text}>... and {token.items.length - 100} more items</Text>}
                    </View>
                );
            case "hr":
                return (
                    <View
                        key={index}
                        style={styles.hr}
                    />
                );
            case "heading":
                return (
                    <Text
                        key={index}
                        style={[styles.heading, { fontSize: 20 - token.depth * 2, marginTop: 10 }]}
                    >
                        {renderInlineContent(token.text)}
                    </Text>
                );
            default:
                return (
                    <Text
                        key={index}
                        style={styles.text}
                    >
                        {renderInlineContent(token.raw)}
                    </Text>
                );
        }
    });
};

const ChatPDF = ({ markdown, onCleanup }: { markdown: string; onCleanup?: () => void }) => {
    // Use requestIdleCallback for heavy computations
    const sections = useMemo(() => {
        if (!markdown) return [];

        // Break down the processing into smaller chunks
        const rawSections = markdown.split("###").filter(Boolean);
        if (rawSections.length === 0) return [];

        return rawSections.slice(0, 50).map((section) => {
            const [title, ...content] = section.trim().split("\n\n");
            const processedContent = content.join("\n\n");
            return {
                title: title?.slice(0, 1000) || "", // Limit title length
                content: processedContent?.slice(0, 50000) || "", // Limit content length
            };
        });
    }, [markdown]);

    // Handle cleanup in a non-blocking way
    const handleCleanup = useCallback(() => {
        if (typeof window !== "undefined" && "requestIdleCallback" in window) {
            // @ts-ignore
            window.requestIdleCallback(
                () => {
                    onCleanup?.();
                },
                { timeout: 1000 },
            );
        } else {
            // Fallback for browsers that don't support requestIdleCallback
            setTimeout(() => {
                onCleanup?.();
            }, 100);
        }
    }, [onCleanup]);

    useEffect(() => {
        return () => {
            handleCleanup();
        };
    }, [handleCleanup]);

    return (
        <Document
            onRender={() => {
                handleCleanup();
            }}
        >
            <Page
                size="A4"
                style={styles.page}
                wrap={false}
            >
                {sections.map((section, index) => (
                    <View
                        key={index}
                        style={styles.section}
                    >
                        <Text style={styles.heading}>{section.title}</Text>
                        {parseMarkdownContent(section.content)}
                    </View>
                ))}
            </Page>
        </Document>
    );
};

const PDFExportButton = ({ markdown }: PDFExportButtonProps) => {
    const [key, setKey] = useState(0);
    const stateRef = useRef<BlobProviderParams>({
        blob: null,
        url: null,
        loading: false,
        error: null,
    });

    // Use requestIdleCallback for cleanup
    const cleanup = useCallback(() => {
        if (typeof window !== "undefined") {
            if ("requestIdleCallback" in window) {
                // @ts-ignore
                window.requestIdleCallback(
                    () => {
                        setKey((prev) => prev + 1);
                        // Clean up react-pdf's internal cache
                        // @ts-ignore
                        if (window.__REACT_PDF__) {
                            // @ts-ignore
                            window.__REACT_PDF__ = undefined;
                        }
                        URL.revokeObjectURL("");
                    },
                    { timeout: 1000 },
                );
            } else {
                // Fallback
                setTimeout(() => {
                    setKey((prev) => prev + 1);
                    // @ts-ignore
                    if (window.__REACT_PDF__) {
                        // @ts-ignore
                        window.__REACT_PDF__ = undefined;
                    }
                    URL.revokeObjectURL("");
                }, 100);
            }
        }
    }, []);

    if (!markdown) return null;

    return (
        <div className="mb-4">
            <PDFDownloadLink
                key={key}
                document={
                    <ChatPDF
                        markdown={markdown}
                        onCleanup={cleanup}
                    />
                }
                fileName={`chat-export-${new Date().toISOString().split("T")[0]}.pdf`}
            >
                {(state) => {
                    // Update ref without causing re-renders
                    stateRef.current = state;

                    return (
                        <Button
                            variant="outline"
                            size="sm"
                            className="w-full"
                            disabled={state.loading || !!state.error}
                            onClick={() => {
                                const currentState = stateRef.current;
                                if (!currentState.loading && !currentState.error && currentState.url) {
                                    // Use requestIdleCallback for cleanup after download
                                    if (typeof window !== "undefined" && "requestIdleCallback" in window) {
                                        // @ts-ignore
                                        window.requestIdleCallback(() => cleanup(), { timeout: 1000 });
                                    } else {
                                        setTimeout(cleanup, 100);
                                    }
                                }
                            }}
                        >
                            <Download className="h-4 w-4 mr-2" />
                            {state.loading ? t("Preparing PDF...") : state.error ? t("Error preparing PDF") : t("Export as PDF")}
                        </Button>
                    );
                }}
            </PDFDownloadLink>
        </div>
    );
};

interface PDFExportButtonProps {
    markdown: string;
}

export default PDFExportButton;
