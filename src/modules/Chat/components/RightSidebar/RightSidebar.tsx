"use client";

import "client-only";
import { t } from "@/i18n";
import { useCurrentMessage } from "../../hooks/ChatState";
import { useCurrentChat } from "../../hooks/ChatState";
import { useChatApi } from "../../hooks/ChatApi";
import { ChatRoles, ChatEntry, MessageKind } from "@/fetch/api/Chat";
import SourceDetails from "./SourceDetails";
import FieldValue from "./FieldValue";
import KnowledgeDetails from "./KnowledgeDetails";
import { KnowledgeBaseDocument } from "@/fetch/api/KnowledgeBase";
import { SimpleDocument } from "@/fetch/api/SimpleDocument";
import { ChevronDown, ChevronRight, ChevronLeft, ListTree, Download, Presentation, Upload, Loader2 } from "lucide-react";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { useState, useRef } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Chat, ChatMessage } from "@/modules/Chat/contexts/ChatContextProvider";
import { fillPPTXTemplate } from "@/fetch/api/Report";
import { toast } from "react-toastify";
import { marked } from "marked";

// Dynamically import PDF components with no SSR

interface RightSidebarProps {
    isKnowledge?: boolean;
    selectedDocument?: SimpleDocument | KnowledgeBaseDocument | null;
}

const exportChatToMarkdown = (messages?: Record<string, ChatMessage>) => {
    if (!messages) return;

    const markdown = Object.values(messages)
        .filter((msg): msg is ChatMessage => msg !== undefined)
        .map((msg: ChatMessage) => {
            const userQuery = msg.query;
            const assistantResponse = msg.result?.result || "";
            // Add some basic markdown formatting to the assistant's response
            const formattedResponse = assistantResponse
                .replace(/```([^`]+)```/g, "\n```\n$1\n```\n") // Format code blocks
                .replace(/`([^`]+)`/g, "`$1`"); // Format inline code
            return `### Answer: \n\n${formattedResponse}\n\n`;
        })
        .join("");

    return markdown;
};

const handleMarkdownExport = (messages?: Record<string, ChatMessage>) => {
    const markdown = exportChatToMarkdown(messages);
    if (!markdown) return;

    const blob = new Blob([markdown], { type: "text/markdown" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `chat-export-${new Date().toISOString().split("T")[0]}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
};

export default function RightSidebar({ isKnowledge, selectedDocument }: RightSidebarProps) {
    const currentMessage = useCurrentMessage();
    const currentChat = useCurrentChat();
    const { askMessageMutation } = useChatApi();
    const [isOpen, setIsOpen] = useState(false);
    const [isRightPanelOpen, setRightPanelOpen] = useState(false);
    const [showChunksModal, setShowChunksModal] = useState(false);
    const [showSummaryModal, setShowSummaryModal] = useState(false);
    const [summaryResult, setSummaryResult] = useState<string>("");
    const [isExportingPPTX, setIsExportingPPTX] = useState(false);
    const [selectedTemplate, setSelectedTemplate] = useState<File | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleTemplateSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file && file.type === "application/vnd.openxmlformats-officedocument.presentationml.presentation") {
            setSelectedTemplate(file);
        } else {
            toast.error(t("Please select a valid PPTX file"));
            if (fileInputRef.current) {
                fileInputRef.current.value = "";
            }
        }
    };

    const handlePPTXExport = async () => {
        if (!currentChat?.chatId || !selectedTemplate) {
            toast.error(t("Missing required information for export"));
            return;
        }

        const collectionId = "Changelog_80";
        if (!collectionId) {
            toast.error(t("No collection ID found"));
            return;
        }

        try {
            setIsExportingPPTX(true);
            const result = await fillPPTXTemplate({
                chatId: currentChat.chatId,
                templateFile: selectedTemplate,
                generalConfig: {
                    collection_id: collectionId,
                    context: {
                        custom_instructions: "Write short.",
                    },
                },
            });

            if (result.error) {
                toast.error(t("Failed to generate PPTX"));
                return;
            }

            toast.success(t("PPTX exported successfully"));
        } catch (error) {
            console.error("PPTX export error:", error);
            toast.error(t("Failed to generate PPTX"));
        } finally {
            setIsExportingPPTX(false);
        }
    };

    if (isKnowledge) {
        return (
            <KnowledgeDetails
                selectedDocument={selectedDocument}
                isRightPanelOpen={isRightPanelOpen}
                setRightPanelOpen={setRightPanelOpen}
            />
        );
    }

    const documents = currentMessage?.result?.relevant_documents?.collection_retriever_entries.map((documentEntry, index) => (
        <SourceDetails
            key={index}
            documentEntry={documentEntry}
        ></SourceDetails>
    ));

    const infoData = { ...currentMessage?.result?.relevant_documents };
    delete infoData.collection_retriever_entries;
    const infoPanelDetails = Object.entries(infoData || {}).map(([key, value], index) => (
        <FieldValue
            key={index}
            field={key}
            value={JSON.stringify(value)}
        ></FieldValue>
    ));

    return (
        <div className="relative flex">
            <aside
                className={`
                flex-shrink-0 bg-gray-50 h-screen overflow-hidden
                transition-[width] duration-300 ease-in-out
                ${isRightPanelOpen ? "w-[300px]" : "w-[40px]"}
            `}
            >
                <div className="px-1 py-1 flex items-center">
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setRightPanelOpen(!isRightPanelOpen)}
                        className="hover:bg-gray-100"
                    >
                        {isRightPanelOpen ? <ChevronRight /> : <ChevronLeft />}
                    </Button>

                    {isRightPanelOpen ? <h2 className="text-lg font-medium">Sources</h2> : null}
                </div>
                <div
                    className={`
                    transition-all duration-300 ease-in-out
                    ${isRightPanelOpen ? "opacity-100 translate-x-0" : "opacity-0 translate-x-4 invisible"}
                    overflow-y-auto h-[calc(100vh-48px)]
                `}
                >
                    <div className="px-6 pb-8">
                        <div className="space-y-0">
                            {currentMessage ? (
                                <>
                                    <div className="space-y-1">
                                        {documents}
                                        <div className="h-4" />
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="w-full text-xs"
                                            onClick={() => setShowChunksModal(true)}
                                        >
                                            <ListTree className="h-3 w-3 mr-1" />
                                            {t("View All Chunks")}
                                        </Button>
                                        <div className="h-4" />
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="mb-4 w-full"
                                            onClick={() => {
                                                if (!currentChat?.messages) return;
                                                const messages = Object.entries(currentChat.messages)
                                                    .filter((entry): entry is [string, ChatMessage] => entry[1] !== undefined)
                                                    .reduce((acc, [key, msg]) => ({ ...acc, [key]: msg }), {} as Record<string, ChatMessage>);
                                                handleMarkdownExport(messages);
                                            }}
                                        >
                                            <Download className="h-4 w-4 mr-2" />
                                            {t("Export as Markdown")}
                                        </Button>
                                        <div className="mb-4">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="mb-4 w-full"
                                                onClick={async () => {
                                                    try {
                                                        if (!currentChat?.messages) return;

                                                        const messages: ChatEntry[] = Object.values(currentChat.messages)
                                                            .filter((msg) => msg !== undefined)
                                                            .map((msg) => ({
                                                                role: ChatRoles.USER,
                                                                message: {
                                                                    content: msg.query,
                                                                    // type: "text",
                                                                    kind: MessageKind.TEXT,
                                                                },
                                                            }));

                                                        const result = await askMessageMutation.mutateAsync({
                                                            query: "Please provide me the following information of person asking for a quote 1. First Name, 2. Last Name, 3. Company name, 4. Email, 5. Phone number 6. Some summary of what are the needs/requirements of the client. Show it in a table",
                                                            chat_messages: { messages },
                                                        });

                                                        if (result?.result) {
                                                            setSummaryResult(result.result);
                                                            setShowSummaryModal(true);
                                                        }
                                                    } catch (error) {
                                                        console.error("Error getting summary:", error);
                                                        toast.error(t("Failed to get summary"));
                                                    }
                                                }}
                                            >
                                                {askMessageMutation.isPending ? (
                                                    <>
                                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                        {t("Get event summary")}
                                                    </>
                                                ) : (
                                                    t("Get event summary")
                                                )}
                                            </Button>
                                        </div>
                                        <div className="mb-4">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="mb-4 w-full"
                                                onClick={async () => {
                                                    try {
                                                        if (!currentChat?.messages) return;

                                                        const messages: ChatEntry[] = Object.values(currentChat.messages)
                                                            .filter((msg) => msg !== undefined)
                                                            .map((msg) => ({
                                                                role: ChatRoles.USER,
                                                                message: {
                                                                    content: msg.query,
                                                                    // type: "text",
                                                                    kind: MessageKind.TEXT,
                                                                },
                                                            }));

                                                        const result = await askMessageMutation.mutateAsync({
                                                            query: "Please provide me the equipment needed for the event  split in section for each hall and room. Please also estimate the cost for each one of them. Show it in markdown table.",
                                                            chat_messages: { messages },
                                                        });

                                                        if (result?.result) {
                                                            setSummaryResult(result.result);
                                                            setShowSummaryModal(true);
                                                        }
                                                    } catch (error) {
                                                        console.error("Error getting summary:", error);
                                                        toast.error(t("Failed to get summary"));
                                                    }
                                                }}
                                            >
                                                {askMessageMutation.isPending ? (
                                                    <>
                                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                        {t("Calculate cost")}
                                                    </>
                                                ) : (
                                                    t("Calculate cost")
                                                )}
                                            </Button>
                                        </div>
                                        <div className="mb-4">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="mb-4 w-full"
                                                onClick={async () => {
                                                    try {
                                                        if (!currentChat?.messages) return;

                                                        const messages: ChatEntry[] = Object.values(currentChat.messages)
                                                            .filter((msg) => msg !== undefined)
                                                            .map((msg) => ({
                                                                role: ChatRoles.USER,
                                                                message: {
                                                                    content: msg.query,
                                                                    kind: MessageKind.TEXT,
                                                                },
                                                            }));

                                                        const result = await askMessageMutation.mutateAsync({
                                                            query: "Please create a detailed event proposal document that includes (as rows): 1. Client information (name, company, contact details) 2. Event requirements and scope 3. Equipment list with costs 4. Venue details and setup 5. Total estimated budget. Format this as a professional document with proper sections and markdown formatting.",
                                                            chat_messages: { messages },
                                                        });

                                                        if (result?.result) {
                                                            // Open in new window
                                                            const newWindow = window.open("", "_blank");
                                                            if (newWindow) {
                                                                newWindow.document.write(`
                                                                    <!DOCTYPE html>
                                                                    <html>
                                                                        <head>
                                                                            <title>Event Proposal</title>
                                                                            <style>
                                                                                ${document.querySelector("style")?.innerHTML || ""}
                                                                            </style>
                                                                            <link href="${window.location.origin}/A4DocumentView.css" rel="stylesheet">
                                                                        </head>
                                                                        <body>
                                                                            <div class="a4-container">
                                                                                <div class="a4-page">
                                                                                    <div class="document-header">
                                                                                        <img src="${window.location.origin}/meeting-tomorrow-logo.png" alt="Meeting Tomorrow Logo" />
                                                                                    </div>
                                                                                    <div class="content">
                                                                                        ${marked.parse(result.result)}
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </body>
                                                                    </html>
                                                                `);
                                                                newWindow.document.close();
                                                            }
                                                        }
                                                    } catch (error) {
                                                        console.error("Error getting proposal:", error);
                                                        toast.error(t("Failed to generate proposal"));
                                                    }
                                                }}
                                            >
                                                {askMessageMutation.isPending ? (
                                                    <>
                                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                        {t("Generate Proposal Document")}
                                                    </>
                                                ) : (
                                                    t("Generate Proposal Document")
                                                )}
                                            </Button>
                                        </div>
                                        <div className="mb-4">
                                            <input
                                                type="file"
                                                accept=".pptx,application/vnd.openxmlformats-officedocument.presentationml.presentation"
                                                onChange={handleTemplateSelect}
                                                className="hidden"
                                                ref={fileInputRef}
                                            />
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                className="w-full mb-2"
                                                onClick={() => fileInputRef.current?.click()}
                                            >
                                                <Upload className="h-4 w-4 mr-2" />
                                                {selectedTemplate ? t("Change Template") : t("Upload Template")}
                                            </Button>
                                            {selectedTemplate && <div className="text-xs text-gray-500 truncate px-2">{selectedTemplate.name}</div>}
                                        </div>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="mb-4 w-full"
                                            onClick={handlePPTXExport}
                                            disabled={isExportingPPTX || !currentChat?.chatId || !selectedTemplate}
                                        >
                                            <Presentation className="h-4 w-4 mr-2" />
                                            {isExportingPPTX ? t("Generating PPTX...") : t("Export as PPTX")}
                                        </Button>
                                        <div className="flex flex-col flex-auto">
                                            <Collapsible
                                                open={isOpen}
                                                onOpenChange={setIsOpen}
                                                className="space-y-2"
                                            >
                                                <div className="flex flex-auto items-center justify-between">
                                                    <CollapsibleTrigger asChild>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="flex items-center gap-1 p-0 h-auto hover:bg-transparent"
                                                        >
                                                            <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? "transform rotate-180" : ""}`} />
                                                            <span className="text-sm font-medium text-gray-500">Details</span>
                                                        </Button>
                                                    </CollapsibleTrigger>
                                                </div>
                                                <CollapsibleContent className="space-y-2">
                                                    <div className="text-sm text-gray-500 rounded-md border border-gray-200 bg-white p-4">{infoPanelDetails}</div>
                                                </CollapsibleContent>
                                            </Collapsible>
                                        </div>
                                    </div>
                                </>
                            ) : (
                                <i className="text-gray-500">{t("Please select a message")}</i>
                            )}
                        </div>
                    </div>
                </div>
            </aside>

            <Dialog
                open={showChunksModal}
                onOpenChange={setShowChunksModal}
            >
                <DialogContent className="max-w-[90vw] md:max-w-[80vw] lg:max-w-[70vw] max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>{t("Document Chunks")}</DialogTitle>
                        <DialogDescription>{t("All chunks from relevant documents")}</DialogDescription>
                    </DialogHeader>
                    <div className="space-y-2">
                        {currentMessage?.result?.relevant_documents?.collection_retriever_entries.map((entry) => (
                            <Collapsible
                                key={entry.chunk_id}
                                className="border rounded-lg"
                            >
                                <CollapsibleTrigger asChild>
                                    <div className="p-4 flex items-center justify-between cursor-pointer hover:bg-gray-50">
                                        <div className="font-medium text-sm">{entry.document_metadata?.source}</div>
                                        <ChevronDown className="h-4 w-4 text-gray-500 transition-transform ui-expanded:rotate-180" />
                                    </div>
                                </CollapsibleTrigger>
                                <CollapsibleContent>
                                    <div className="px-4 pb-4">
                                        <div
                                            className="text-sm text-gray-600 font-mono break-words whitespace-pre-wrap overflow-x-hidden border-t pt-2"
                                            style={{ overflowWrap: "break-word", wordWrap: "break-word", wordBreak: "break-word" }}
                                        >
                                            {entry.chunk}
                                        </div>
                                    </div>
                                </CollapsibleContent>
                            </Collapsible>
                        ))}
                    </div>
                </DialogContent>
            </Dialog>

            <Dialog
                open={showSummaryModal}
                onOpenChange={setShowSummaryModal}
            >
                <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>{t("Summary")}</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        {summaryResult && (
                            <div className="chatbot-message-content">
                                {" "}
                                <div dangerouslySetInnerHTML={{ __html: marked.parse(summaryResult) }} />
                            </div>
                        )}
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
}
