"use client";

import "client-only";
import { t } from "@/i18n";
import { But<PERSON> } from "@/components/ui/button";
import { Upload, ListTree, MessageSquare, Ampersand, FlaskConical, MessageCircleQuestion, ChevronLeft, BookOpen } from "lucide-react";
import { SimpleDocument, GetSimpleDocument, ChunkModel } from "@/fetch/api/SimpleDocument";
import { KnowledgeBaseDocument, getDownloadFileUrl } from "@/fetch/api/KnowledgeBase";
import { askDocument } from "@/fetch/api/Chat";
import { useState } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { marked } from "marked";

interface KnowledgeDetailsProps {
    selectedDocument?: SimpleDocument | KnowledgeBaseDocument | null;
    isRightPanelOpen?: boolean;
    setRightPanelOpen?: (isOpen: boolean) => void;
}

export default function KnowledgeDetails({ selectedDocument, isRightPanelOpen = true, setRightPanelOpen }: KnowledgeDetailsProps) {
    const [showChunksModal, setShowChunksModal] = useState(false);
    const [showResponseModal, setShowResponseModal] = useState(false);
    const [showManualModal, setShowManualModal] = useState(false);
    const [query, setQuery] = useState("");
    const [response, setResponse] = useState<string | null>(null);
    const [documentWithChunks, setDocumentWithChunks] = useState<SimpleDocument | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isQuerying, setIsQuerying] = useState(false);
    
    const togglePanel = () => {
        if (setRightPanelOpen) {
            setRightPanelOpen(!isRightPanelOpen);
        }
    };

    if (!selectedDocument) {
        return (
            <aside
                className={`
                flex-shrink-0 bg-gray-50 h-screen overflow-hidden
                transition-[width] duration-300 ease-in-out
                ${isRightPanelOpen ? "w-[300px]" : "w-[40px]"}
            `}
            >
                <div className="px-1 py-1 flex items-center">
                    <Button
                        variant="ghost"
                        size="icon"
                        onClick={togglePanel}
                        className="hover:bg-gray-100"
                    >
                        <ChevronLeft className={`h-4 w-4 transition-transform duration-300 ${isRightPanelOpen ? "rotate-180" : ""}`} />
                        <span className="sr-only">{isRightPanelOpen ? "Close panel" : "Open panel"}</span>
                    </Button>
                    {isRightPanelOpen ? <h2 className="text-lg font-medium">{t("Document Details")}</h2> : null}
                </div>
                <div
                    className={`
                    transition-all duration-300 ease-in-out
                    ${isRightPanelOpen ? "opacity-100 translate-x-0" : "opacity-0 translate-x-4 invisible"}
                    overflow-y-auto flex flex-col h-[calc(100vh-48px)]
                `}
                >
                    <div className="p-4 flex-grow">
                        <div className="space-y-4">
                            <i className="text-gray-500">{t("Select a document to view details")}</i>
                        </div>
                    </div>
                    
                    {/* GraphView manual button at the bottom */}
                    <div className="p-4 mt-auto border-t">
                        <Button
                            variant="outline"
                            size="sm"
                            className="w-full text-xs"
                            onClick={() => setShowManualModal(true)}
                        >
                            <BookOpen className="h-3 w-3 mr-1" />
                            {t("GraphView manual")}
                        </Button>
                    </div>
                </div>
                
                {/* GraphView Manual Dialog */}
                <Dialog
                open={showManualModal}
                onOpenChange={setShowManualModal}
            >
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>{t("GraphView Manual")}</DialogTitle>
                        <DialogDescription>{t("Instructions for using the Graph View")}</DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">Getting Started with Collections</h3>
                        <p>To process files, you first need to organize them into collections:</p>
                        <ul className="list-disc pl-5 space-y-2">
                            <li>Create a new collection using the "New Collection" button</li>
                            <li>Add files to existing collections</li>
                            <li>Manage collections (delete or download contents) by clicking the three dots menu next to each collection</li>
                            <li>Toggle "Graph View" switch in the top right to access the graph visualization interface</li>
                        </ul>
                        
                        <h3 className="text-lg font-medium">Graph Processing</h3>
                        <p>Before visualizing relationships, you need to process your collection:</p>
                        <ul className="list-disc pl-5 space-y-2">
                            <li>Select the collection to view or process from the dropdown</li>
                            <li>Use "Reset processing" to delete the graph for the selected collection from the database</li>
                            <li>"Download Graph" exports a JSON file containing the graph data for the selected collection</li>
                            <li>Click "Process Step 1" to process files individually, or "Process All Steps" to run the entire sequence automatically</li>
                            <li>Each processing step is described in the info box when started</li>
                            <li><strong>Important:</strong> All files needed for processing must be uploaded first to the collection</li>
                        </ul>
                        
                        <h3 className="text-lg font-medium">Filtering Node Types</h3>
                        <p>Control which elements appear in your graph visualization:</p>
                        <ul className="list-disc pl-5 space-y-2">
                            <li><strong>File:</strong> Nodes representing uploaded files</li>
                            <li><strong>Table:</strong> Tables extracted from files</li>
                            <li><strong>Object:</strong> Representing mechanical and electrical parts from the files</li>
                            <li><strong>Artifact/Entity/Identifier/Instance:</strong> Additional processing artifacts</li>
                            <li>Use "Select All" or "Clear All" buttons to quickly manage filters</li>
                            <li>Click "Apply Filter" to update the visualization</li>
                        </ul>
                        
                        <h3 className="text-lg font-medium">Graph Visualization & Navigation</h3>
                        <p>Navigate and explore relationships in the graph:</p>
                        <ul className="list-disc pl-5 space-y-2">
                            <li>Zoom: Use mouse wheel or pinch gesture to zoom in/out</li>
                            <li>Pan: Click and drag to move around the graph</li>
                            <li>Expand nodes: Click the white star on blue background icon to show children of a node</li>
                            <li>Browse the hierarchy: Use the left panel to navigate through the graph structure</li>
                            <li>Select: Click on a node to view its details in the "Selected Entities" panel</li>
                            <li>Color coding: In the "Dependency graph" sidebar, locate the settings text box at the top and type "attributes_created_from_model_enrichment" - mechanical nodes will change to orange while electrical nodes remain purple</li>
                        </ul>
                        
                        <h3 className="text-lg font-medium">Viewing Node Details</h3>
                        <p>Examine properties and relationships of selected nodes:</p>
                        <ul className="list-disc pl-5 space-y-2">
                            <li>When a node is selected (by clicking directly on the graph, not in the left panel), its data appears in the "Selected Entities" panel</li>
                            <li>View basic information like type, collection, and ID</li>
                            <li>Examine detailed node properties including name, size, data type, relations, and other attributes</li>
                            <li>Use the "Enhance with AI" option to generate additional insights about the selected node</li>
                        </ul>
                        
                        <h3 className="text-lg font-medium">Adding Nodes & Connections</h3>
                        <p>Customize your graph by adding new elements:</p>
                        <ul className="list-disc pl-5 space-y-2">
                            <li>Use "Add Node" to create new nodes in the graph</li>
                            <li>"Create Connection" allows you to establish relationships between existing nodes</li>
                            <li>"Change to Position View" toggles between different visualization layouts</li>
                        </ul>
                    </div>
                    <DialogFooter>
                        <Button onClick={() => setShowManualModal(false)}>Close</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
            </aside>
        );
    }

    const handleViewChunks = async () => {
        if (!selectedDocument?.id) return;

        setIsLoading(true);
        try {
            const document = await GetSimpleDocument(selectedDocument.id);
            console.log("[handleViewChunks] Full document data:", document);
            console.log("[handleViewChunks] Chunks data:", {
                chunksType: typeof document.chunks,
                isArray: Array.isArray(document.chunks),
                length: document.chunks?.length,
                firstChunk: document.chunks?.[0],
            });
            setDocumentWithChunks(document);
            setShowChunksModal(true);
        } catch (error) {
            console.error("Failed to fetch document chunks:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleAskDocument = async (userQuery: string) => {
        if (!selectedDocument?.id || !userQuery.trim()) return;

        setIsQuerying(true);
        setResponse(null);
        try {
            const result = await askDocument({
                document_ids: [selectedDocument.id],
                query: userQuery,
            });
            console.log("Got response:", result);
            setResponse(result);
            setShowResponseModal(true);
        } catch (error) {
            console.error("Failed to query document:", error);
            setResponse(t("Failed to get response. Please try again."));
            setShowResponseModal(true);
        } finally {
            setIsQuerying(false);
        }
    };

    return (
        <aside className="w-[300px] flex-shrink-0 bg-gray-50 h-screen overflow-y-auto">
            <div className="p-4">
                <h2 className="text-lg font-medium mb-4">{t("Document Details")}</h2>
                <div className="space-y-4">
                    <div className="bg-white p-4 rounded-lg shadow-sm">
                        <h3 className="font-medium text-sm text-gray-900 mb-2">{selectedDocument.name}</h3>
                        <div className="space-y-2">
                            <div className="text-xs">
                                <span className="text-gray-500">{t("Status")}: </span>
                                <span className="text-gray-900">{selectedDocument.status}</span>
                            </div>
                            <div className="text-xs">
                                <span className="text-gray-500">{t("Type")}: </span>
                                <span className="text-gray-900">{selectedDocument.document_type}</span>
                            </div>
                            {selectedDocument.collections?.length > 0 && (
                                <div className="text-xs">
                                    <span className="text-gray-500">{t("Collections")}: </span>
                                    <div className="mt-1">
                                        {selectedDocument.collections.map((collection, index) => (
                                            <span
                                                key={index}
                                                className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 mr-1 mb-1"
                                            >
                                                {collection}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            )}
                            {selectedDocument.keywords?.length > 0 && (
                                <div className="text-xs">
                                    <span className="text-gray-500">{t("Keywords")}: </span>
                                    <div className="mt-1">
                                        {selectedDocument.keywords.map((keyword, index) => (
                                            <span
                                                key={index}
                                                className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 mr-1 mb-1"
                                            >
                                                {keyword}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>

                    {selectedDocument.source_file && (
                        <div className="mt-4 space-y-2">
                            <Button
                                variant="outline"
                                size="sm"
                                className="w-full text-xs"
                                onClick={handleViewChunks}
                                disabled={isLoading}
                            >
                                <ListTree className="h-3 w-3 mr-1" />
                                {isLoading ? t("Loading...") : t("View Document Chunks")}
                            </Button>
                            <div className="space-y-2 pt-2">
                                <div className="text-xs font-medium text-gray-700">{t("Actions:")}:</div>
                                <div className="flex space-x-2 hidden">
                                    <Input
                                        value={query}
                                        onChange={(e) => setQuery(e.target.value)}
                                        placeholder={t("Type your question...")}
                                        className="text-xs h-8 px-2"
                                        onKeyDown={(e) => {
                                            if (e.key === "Enter" && !e.shiftKey) {
                                                e.preventDefault();
                                                handleAskDocument(query);
                                            }
                                        }}
                                    />
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="shrink-0"
                                        disabled={isQuerying || !query.trim()}
                                        onClick={() => handleAskDocument(query)}
                                    >
                                        {isQuerying ? <span className="animate-spin">...</span> : <MessageSquare className="h-3 w-3" />}
                                    </Button>
                                </div>

                                <div className="flex space-x-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="w-full text-xs"
                                        disabled={isQuerying}
                                        onClick={() =>
                                            handleAskDocument(
                                                "You will be given a lease document. Extract the most important information about lease. Answer only with the extracted information, no additional comments.",
                                            )
                                        }
                                    >
                                        {isQuerying ? (
                                            <span className="animate-spin">...</span>
                                        ) : (
                                            <>
                                                <FlaskConical className="h-3 w-3 mr-1" /> {t("Extract information...")}
                                            </>
                                        )}
                                    </Button>
                                </div>
                                <div className="flex space-x-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="w-full text-xs"
                                        disabled={isQuerying}
                                        onClick={() =>
                                            handleAskDocument(
                                                "Create markdown table with Name, address, street, city, state, zip code, country of the customer. Give just a table, no additional comments.",
                                            )
                                        }
                                    >
                                        {isQuerying ? (
                                            <span className="animate-spin">...</span>
                                        ) : (
                                            <>
                                                <FlaskConical className="h-3 w-3 mr-1" /> {t("Extract address...")}
                                            </>
                                        )}
                                    </Button>
                                </div>
                                <div className="flex space-x-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="w-full text-xs"
                                        disabled={isQuerying}
                                        onClick={() => handleAskDocument("Explain this document.")}
                                    >
                                        {isQuerying ? (
                                            <span className="animate-spin">...</span>
                                        ) : (
                                            <>
                                                <MessageCircleQuestion className="h-3 w-3 mr-1" /> {t("Summarize")}
                                            </>
                                        )}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            <Dialog
                open={showResponseModal}
                onOpenChange={(open) => {
                    setShowResponseModal(open);
                    if (!open) {
                        setQuery("");
                        setResponse(null);
                    }
                }}
            >
                <DialogContent className="sm:max-w-6xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>{t("Response:")}</DialogTitle>
                        <DialogDescription>{t(":")}</DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                        {response && (
                            <div className="chatbot-message-content">
                                {" "}
                                <div dangerouslySetInnerHTML={{ __html: marked.parse(response) }} />
                            </div>
                        )}
                    </div>
                </DialogContent>
            </Dialog>

            <Dialog
                open={showChunksModal}
                onOpenChange={setShowChunksModal}
            >
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>{t("Document Chunks")}</DialogTitle>
                        <DialogDescription>{t("View all chunks of the selected document")}</DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                        {documentWithChunks?.chunks?.map((chunk, index) => {
                            console.log("[Chunk Render]", {
                                chunk,
                                type: typeof chunk,
                                isString: typeof chunk === "string",
                                isObject: typeof chunk === "object" && chunk !== null,
                            });

                            // First check if it's an object, otherwise treat as string
                            if (typeof chunk !== "object" || chunk === null) {
                                return (
                                    <div
                                        key={index}
                                        className="p-4 border rounded-lg"
                                    >
                                        <div className="text-sm font-medium mb-2">Chunk {index + 1}</div>
                                        <div className="text-sm text-gray-600 font-mono whitespace-pre-wrap break-words">{String(chunk)}</div>
                                    </div>
                                );
                            }

                            // Now we know it's an object, check if it has the ChunkModel properties
                            const typedChunk = chunk as ChunkModel;
                            if (!("text" in typedChunk)) {
                                return (
                                    <div
                                        key={index}
                                        className="p-4 border rounded-lg"
                                    >
                                        <div className="text-sm font-medium mb-2">Chunk {index + 1}</div>
                                        <div className="text-sm text-gray-600 font-mono whitespace-pre-wrap break-words">{JSON.stringify(chunk, null, 2)}</div>
                                    </div>
                                );
                            }

                            return (
                                <div
                                    key={index}
                                    className="p-4 border rounded-lg space-y-3"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm font-medium">Chunk {index + 1}</div>
                                        <div className="text-xs text-gray-500">ID: {typedChunk.id}</div>
                                    </div>

                                    <div className="text-sm text-gray-600 font-mono whitespace-pre-wrap break-words">{typedChunk.text}</div>

                                    {typedChunk.keywords.length > 0 && (
                                        <div className="space-y-1">
                                            <div className="text-xs font-medium text-gray-700">{t("Keywords")}:</div>
                                            <div className="flex flex-wrap gap-1">
                                                {typedChunk.keywords.map((keyword, idx) => (
                                                    <span
                                                        key={idx}
                                                        className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                                                    >
                                                        {keyword}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                    )}

                                    {Object.keys(typedChunk.chunk_metadata).length > 0 && (
                                        <div className="space-y-1">
                                            <div className="text-xs font-medium text-gray-700">{t("Metadata")}:</div>
                                            <div className="bg-gray-50 rounded p-2">
                                                <pre className="text-xs text-gray-600 overflow-auto">{JSON.stringify(typedChunk.chunk_metadata, null, 2)}</pre>
                                            </div>
                                        </div>
                                    )}

                                    {typedChunk.text_hash && (
                                        <div className="text-xs text-gray-500">
                                            {t("Hash")}: {typedChunk.text_hash}
                                        </div>
                                    )}
                                </div>
                            );
                        })}
                        {(!documentWithChunks?.chunks || documentWithChunks.chunks.length === 0) && (
                            <div className="text-sm text-gray-500 text-center py-4">{t("No chunks available for this document")}</div>
                        )}
                    </div>
                </DialogContent>
            </Dialog>
            
            {/* Manual dialog needs to be available even when a document is selected */}
            <Dialog
                open={showManualModal}
                onOpenChange={setShowManualModal}
            >
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>{t("GraphView Manual")}</DialogTitle>
                        <DialogDescription>{t("Instructions for using the Graph View")}</DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                        <h3 className="text-lg font-medium">Getting Started with Collections</h3>
                        <p>To process files, you first need to organize them into collections:</p>
                        <ul className="list-disc pl-5 space-y-2">
                            <li>Create a new collection using the "New Collection" button</li>
                            <li>Add files to existing collections</li>
                            <li>Manage collections (delete or download contents) by clicking the three dots menu next to each collection</li>
                            <li>Toggle "Graph View" switch in the top right to access the graph visualization interface</li>
                        </ul>
                        
                        <h3 className="text-lg font-medium">Graph Processing</h3>
                        <p>Before visualizing relationships, you need to process your collection:</p>
                        <ul className="list-disc pl-5 space-y-2">
                            <li>Select the collection to view or process from the dropdown</li>
                            <li>Use "Reset processing" to delete the graph for the selected collection from the database</li>
                            <li>"Download Graph" exports a JSON file containing the graph data for the selected collection</li>
                            <li>Click "Process Step 1" to process files individually, or "Process All Steps" to run the entire sequence automatically</li>
                            <li>Each processing step is described in the info box when started</li>
                            <li><strong>Important:</strong> All files needed for processing must be uploaded first to the collection</li>
                        </ul>
                        
                        <h3 className="text-lg font-medium">Filtering Node Types</h3>
                        <p>Control which elements appear in your graph visualization:</p>
                        <ul className="list-disc pl-5 space-y-2">
                            <li><strong>File:</strong> Nodes representing uploaded files</li>
                            <li><strong>Table:</strong> Tables extracted from files</li>
                            <li><strong>Object:</strong> Representing mechanical and electrical parts from the files</li>
                            <li><strong>Artifact/Entity/Identifier/Instance:</strong> Additional processing artifacts</li>
                            <li>Use "Select All" or "Clear All" buttons to quickly manage filters</li>
                            <li>Click "Apply Filter" to update the visualization</li>
                        </ul>
                        
                        <h3 className="text-lg font-medium">Graph Visualization & Navigation</h3>
                        <p>Navigate and explore relationships in the graph:</p>
                        <ul className="list-disc pl-5 space-y-2">
                            <li>Zoom: Use mouse wheel or pinch gesture to zoom in/out</li>
                            <li>Pan: Click and drag to move around the graph</li>
                            <li>Expand nodes: Click the white star on blue background icon to show children of a node</li>
                            <li>Browse the hierarchy: Use the left panel to navigate through the graph structure</li>
                            <li>Select: Click on a node to view its details in the "Selected Entities" panel</li>
                            <li>Color coding: In the "Dependency graph" sidebar, locate the settings text box at the top and type "attributes_created_from_model_enrichment" - mechanical nodes will change to orange while electrical nodes remain purple</li>
                        </ul>
                        
                        <h3 className="text-lg font-medium">Viewing Node Details</h3>
                        <p>Examine properties and relationships of selected nodes:</p>
                        <ul className="list-disc pl-5 space-y-2">
                            <li>When a node is selected (by clicking directly on the graph, not in the left panel), its data appears in the "Selected Entities" panel</li>
                            <li>View basic information like type, collection, and ID</li>
                            <li>Examine detailed node properties including name, size, data type, relations, and other attributes</li>
                            <li>Use the "Enhance with AI" option to generate additional insights about the selected node</li>
                        </ul>
                        
                        <h3 className="text-lg font-medium">Adding Nodes & Connections</h3>
                        <p>Customize your graph by adding new elements:</p>
                        <ul className="list-disc pl-5 space-y-2">
                            <li>Use "Add Node" to create new nodes in the graph</li>
                            <li>"Create Connection" allows you to establish relationships between existing nodes</li>
                            <li>"Change to Position View" toggles between different visualization layouts</li>
                        </ul>
                    </div>
                    <DialogFooter>
                        <Button onClick={() => setShowManualModal(false)}>Close</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </aside>
    );
}