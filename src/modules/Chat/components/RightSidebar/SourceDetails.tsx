"use client";

import "client-only";
import { Card, CardContent, CardDescription, CardHeader } from "@/components/ui/card";
import { DocumentEntryDetails } from "@/fetch/api/Chat";
import { FileText } from "lucide-react";
import { useState } from "react";
import FieldValue from "./FieldValue";

interface SourceDetailsProps {
    documentEntry: DocumentEntryDetails;
}

export default function SourceDetails({ documentEntry }: SourceDetailsProps) {
    const [isCollapsed, setIsCollapsed] = useState(true);
    const toggleCollapse = () => {
        setIsCollapsed((prev) => !prev);
    };
    const entryMetadata = Object.entries(documentEntry.document_metadata || {}).map(([key, value], index) => (
        <FieldValue
            key={index}
            field={key}
            value={value}
        ></FieldValue>
    ));

    return (
        <Card className="first:mt-0 mt-1 border-0">
            <CardHeader
                className="p-1.5 cursor-pointer hover:bg-gray-100 rounded-md"
                onClick={toggleCollapse}
            >
                <CardDescription className="flex gap-2 items-center select-none">
                    <div className="text-gray-500">
                        <FileText className="h-4 w-4" />
                    </div>
                    <div className="truncate text-xs">{documentEntry.document_metadata?.source}</div>
                </CardDescription>
            </CardHeader>
            {!isCollapsed && (
                <CardContent className="p-2 pt-0">
                    {entryMetadata}
                </CardContent>
            )}
        </Card>
    );
}
