.document-header {
    width: 100%;
    padding-bottom: 2rem;
    border-bottom: 2px solid #e0e0e0;
    margin-bottom: 2rem;
    text-align: center;
}

.document-header img {
    width: 800px;
    height: auto;
}

.a4-container {
    background: #e0e0e0;
    min-height: 100vh;
    padding: 2rem;
    display: flex;
    justify-content: center;
}

.a4-page {
    background: white;
    width: 21cm;
    min-height: 29.7cm;
    padding: 2.54cm;
    margin: 0 auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.content {
    font-family: "Times New Roman", Times, serif;
    font-size: 12pt;
    line-height: 1.5;
}

/* Table styles */
.content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
}

.content th,
.content td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.content th {
    background-color: #f5f5f5;
}

/* Print styles */
@media print {
    .a4-container {
        padding: 0;
        background: none;
    }

    .a4-page {
        box-shadow: none;
        margin: 0;
        padding: 0;
    }
}
