"use client";

import ChatList from "@/modules/Chat/components/LeftSidebar/ChatList";
import UserDetails from "@/modules/Chat/components/LeftSidebar/UserDetails";
import Link from "next/link";
import Image from "next/image";
import { FolderClosed } from "lucide-react";
import { t } from "@/i18n";
import { cn } from "@/utils";
import { AppRoutes } from "@/enums/AppRoutes";
import { useChats } from "@/modules/Chat/hooks/ChatState";
import { ChatAction } from "@/modules/Chat/contexts/ChatContextProvider";
import { useDefaultNavigation } from "@/hooks/useDefaultNavigation";

type LeftSidebarProps = Readonly<{
    isKnowledge: boolean;
}>;

export default function LeftSidebar({ isKnowledge }: LeftSidebarProps) {
    const { dispatch } = useChats();
    const { navigateToDefault } = useDefaultNavigation();

    const showWelcomePage = () => {
        dispatch({ type: ChatAction.TOGGLE_WELCOME_PAGE, payload: { show: true } });
        dispatch({ type: ChatAction.SET_CURRENT_CHAT, payload: { chatId: null } });
    };

    const handleLogoClick = (e: React.MouseEvent) => {
        e.preventDefault();
        showWelcomePage();
        navigateToDefault();
    };

    return (
        <div className="h-full flex flex-col">
            <div className="flex-none">
                <Link 
                    href="#"
                    className="flex items-center justify-center hover:opacity-80 transition-opacity p-4"
                    onClick={handleLogoClick}
                >
                    <div className="bg-white rounded-2xl px-4 py-2">
                        <Image
                            src="/contextclue.png"
                            alt="Context Clue"
                            width={140}
                            height={32}
                            priority
                        />
                    </div>
                </Link>
            </div>
            <div className="flex-1 overflow-y-auto min-h-0">
                <ChatList />
            </div>
            <div className="flex-none">
                <Link
                    className={cn(
                        "flex gap-2 p-2 px-3 rounded-lg font-medium text-white/80 hover:bg-white/10 transition-colors w-full",
                        isKnowledge ? "bg-white/10" : ""
                    )}
                    href={AppRoutes.KNOWLEDGE}
                >
                    <FolderClosed className="self-center" size={20} strokeWidth={2} />
                    <span className="self-center">{t("Knowledge Base")}</span>
                </Link>
                <div className="p-4 border-t border-white/10">
                    <UserDetails />
                </div>
            </div>
        </div>
    );
}
