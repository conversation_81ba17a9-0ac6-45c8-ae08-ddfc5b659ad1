import { t } from "@/i18n";
import { Plus } from "lucide-react";
import { useAppConfig } from "@/hooks/useAppConfig";

interface NewChatProps {
    createChat: () => void;
}

export default function NewChat({ createChat }: NewChatProps) {
    const { appConfig } = useAppConfig();

    const isDisabled = !appConfig?.features.enable_chats;

    // if (isDisabled) {
    //     return null;
    // }

    return (
        <button
            onClick={() => createChat()}
            className="flex items-center gap-3 w-full rounded-lg p-3 text-white hover:bg-white/5 transition-colors"
        >
            <Plus className="w-4 h-4" />
            <span className="text-sm font-medium">{t("New chat")}</span>
        </button>
    );
}
