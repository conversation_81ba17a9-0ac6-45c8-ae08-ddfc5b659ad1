"use client";

import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Activity, Settings } from "lucide-react";
import { useAppConfig } from "@/hooks/useAppConfig";
import { apiConfig } from "@/config/api";
import { useState } from "react";
import { checkApiHealth, checkAuthHealth } from "@/fetch/FetchRequest";
import { cn } from "@/utils";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

type HealthStatus = "idle" | "checking" | "healthy" | "error";

interface HealthCheckButtonProps {
    status: HealthStatus;
    onClick: () => void;
}

function HealthCheckButton({ status, onClick }: HealthCheckButtonProps) {
    return (
        <button
            onClick={onClick}
            disabled={status === "checking"}
            className={cn("flex items-center justify-center h-5 w-5 rounded transition-colors", {
                "text-blue/60 hover:text-blue-400/80": status === "idle",
                "text-yellow-400": status === "checking",
                "text-green-400": status === "healthy",
                "text-red-400": status === "error",
            })}
        >
            <Activity
                size={16}
                className={status === "checking" ? "animate-pulse" : ""}
            />
        </button>
    );
}

export function AppConfigPopover() {
    const { appConfig, updateAppConfig } = useAppConfig();
    const [apiHealthStatus, setApiHealthStatus] = useState<HealthStatus>("idle");
    const [authHealthStatus, setAuthHealthStatus] = useState<HealthStatus>("idle");

    const checkHealth = async (type: "api" | "auth") => {
        const setStatus = type === "api" ? setApiHealthStatus : setAuthHealthStatus;
        setStatus("checking");
        try {
            if (type === "api") {
                await checkApiHealth();
            } else {
                await checkAuthHealth();
            }
            setStatus("healthy");
            setTimeout(() => setStatus("idle"), 3000);
        } catch (error) {
            setStatus("error");
            setTimeout(() => setStatus("idle"), 3000);
        }
    };

    return (
        <Popover>
            <PopoverTrigger asChild>
                <button
                    className="p-2 hover:bg-white/10 rounded-lg transition-colors mr-2"
                    title="Configuration"
                >
                    <Settings className="w-5 h-5 text-white/60" />
                </button>
            </PopoverTrigger>
            <PopoverContent
                className="w-80"
                align="start"
            >
                <div className="space-y-4">
                    <div>
                        <h3 className="text-sm font-medium mb-2">Default Landing Page</h3>
                        <div className="font-medium">{appConfig.ui.default_landing_page} </div>
                    </div>
                    <div>
                        <h3 className="text-sm font-medium mb-2">API URLs</h3>
                        <div className="pl-2 space-y-2 text-xs">
                            <div className="flex space-x-8 items-center">
                                <HealthCheckButton
                                    status={apiHealthStatus}
                                    onClick={() => checkHealth("api")}
                                />
                                <div className="space-y-1">
                                    <div className="flex items-center justify-between">
                                        <div className="font-medium">App API:</div>
                                    </div>
                                    <div className="font-mono text-muted-foreground break-all">{apiConfig.getApiUrl("")}</div>
                                </div>
                            </div>
                            <div className="flex space-x-8 items-center">
                                <HealthCheckButton
                                    status={authHealthStatus}
                                    onClick={() => checkHealth("auth")}
                                />
                                <div className="flex items-center justify-between">
                                    <div className="font-medium">Auth API:</div>
                                </div>
                                <div className="font-mono text-muted-foreground break-all">{apiConfig.authUrl}</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 className="text-sm font-medium mb-2">UI Configuration</h3>
                        <div className="pl-2 space-y-1">
                            {Object.entries(appConfig.ui)
                                .filter(([key]) => key !== "default_landing_page")
                                .map(([key, value]) => (
                                    <div
                                        key={key}
                                        className="flex justify-between items-center text-xs"
                                    >
                                        <span>{key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}</span>
                                        <span className="font-mono">{String(value)}</span>
                                    </div>
                                ))}
                        </div>
                    </div>
                    <div>
                        <h3 className="text-sm font-medium mb-2">Feature Flags</h3>
                        <div className="pl-2 space-y-1">
                            {Object.entries(appConfig.features).map(([key, value]) => (
                                <div
                                    key={key}
                                    className="flex justify-between items-center text-xs"
                                >
                                    <span>{key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}</span>
                                    <span className="font-mono">{String(value)}</span>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </PopoverContent>
        </Popover>
    );
}
