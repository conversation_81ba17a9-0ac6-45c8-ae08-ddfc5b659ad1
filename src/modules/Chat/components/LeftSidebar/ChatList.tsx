"use client";

import "client-only";
import { MoreVertical } from "lucide-react";
import { t } from "@/i18n";
import { cn } from "@/utils";
import Link from "next/link";
import { useChats } from "@/modules/Chat/hooks/ChatState";
import { useCallback, useMemo } from "react";
import { Chat, ChatAction } from "@/modules/Chat/contexts/ChatContextProvider";
import NewChat from "./NewChat";
import { v4 as uuid } from "uuid";
import { AppRoutes } from "@/enums/AppRoutes";
import { formatDistance } from "date-fns";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useQuery } from "@tanstack/react-query";
import { getMe } from "@/fetch/api/Auth";
import { useAppConfig } from "@/hooks/useAppConfig";

export default function ChatList() {
    const {
        dispatch,
        state: { chats, currentChatId: current },
    } = useChats();

    const { data: user } = useQuery({
        queryKey: ["user"],
        queryFn: getMe,
    });

    const { appConfig } = useAppConfig();

    // // If chats are disabled, don't render anything
    // if (!appConfig?.features.enable_chats) {
    //     return null;
    // }

    const newChat = useCallback(() => {
        const chatId = uuid();
        const currentUserChats = Object.values(chats).filter((chat) => chat !== undefined && (chat.userId === user?.id || chat.userId === null)) as Chat[];
        const chatNumber = currentUserChats.length + 1;

        dispatch({
            type: ChatAction.CREATE_CHAT,
            payload: {
                chatId,
                name: `Chat ${chatNumber}`,
                userId: user?.id || null,
            },
        });
        dispatch({ type: ChatAction.SET_CURRENT_CHAT, payload: { chatId } });
    }, [dispatch, chats, user?.id]);

    const setCurrentChat = useCallback(
        (chatId: string) => {
            dispatch({ type: ChatAction.SET_CURRENT_CHAT, payload: { chatId } });
        },
        [dispatch],
    );

    const deleteCurrentChat = useCallback(
        (chatId: string) => {
            dispatch({ type: ChatAction.DELETE_CURRENT_CHAT, payload: { chatId } });
        },
        [dispatch],
    );

    const chatTiles = useMemo(() => {
        const allChats = Object.values(chats).filter((chat) => chat !== undefined) as Chat[];
        const currentUserChats = allChats.filter((chat) => chat.userId === user?.id || chat.userId === null);

        console.log(`Showing ${currentUserChats.length} of ${allChats.length} chats for user: ${user?.id || "anonymous"}`);

        return currentUserChats.reverse().map((chat) => {
            if (!chat) {
                return <></>;
            }

            const { chatId, name, createdAt } = chat;

            const createdAtText = createdAt ? formatDistance(new Date(createdAt), new Date(), { addSuffix: true, includeSeconds: true }) : "";

            return (
                <Link
                    onClick={() => setCurrentChat(chatId)}
                    href={AppRoutes.CHAT}
                    key={chatId}
                    className={cn("flex items-center justify-between gap-2 p-2 px-3 rounded-lg font-medium text-white/80 hover:bg-white/10 transition-colors", current === chatId ? "bg-white/10" : "")}
                >
                    <div className="flex flex-col">
                        <span className="text-sm">{name}</span>
                        <span className="text-xs text-white/60">{createdAtText}</span>
                    </div>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <button
                                className="p-1 rounded-lg hover:bg-white/10 transition-colors"
                                onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                }}
                            >
                                <MoreVertical className="w-4 h-4 text-white/80" />
                            </button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem
                                className="text-red-600 hover:!bg-blue-50 hover:!text-blue-600 data-[highlighted]:!bg-blue-50 data-[highlighted]:!text-blue-600"
                                onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    deleteCurrentChat(chatId);
                                }}
                            >
                                {t("Delete chat")}
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </Link>
            );
        });
    }, [chats, current, deleteCurrentChat, setCurrentChat, user?.id]);

    return (
        <div className="flex flex-col h-full">
            <div className="mb-2">
                <NewChat createChat={newChat} />
            </div>

            <div className="relative">
                <div className="absolute inset-x-4 h-px bg-gradient-to-r from-transparent via-gray-700 to-transparent" />
            </div>

            <div className="flex-1 overflow-y-auto my-4">{chatTiles}</div>

            <div className="relative">
                <div className="absolute inset-x-4 h-px bg-gradient-to-r from-transparent via-gray-700 to-transparent" />
            </div>

            <div className="mt-2"></div>
        </div>
    );
}
