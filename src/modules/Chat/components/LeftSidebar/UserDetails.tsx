"use client";
import { cn } from "@/utils";

import { LogOut, RefreshCw } from "lucide-react";
import { signOut, useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";
import { getMe } from "@/fetch/api/Auth";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { AppConfigPopover } from "./AppConfigDialog";

export default function UserDetails() {
    const router = useRouter();
    const { status } = useSession();

    const {
        data: user,
        error,
        isLoading,
        refetch,
    } = useQuery({
        queryKey: ["user"],
        queryFn: async () => {
            console.log("Fetching user data...");
            try {
                const data = await getMe();
                console.log("User data fetched successfully:", data);
                return data;
            } catch (error) {
                console.error("Error fetching user data:", error);
                throw error;
            }
        },
        enabled: status === "authenticated",
        retry: 2,
        staleTime: 2 * 60 * 1000,
    });

    return (
        <div className="flex-row items-center justify-between">
            <div className="flex items-center gap-2 p-4">
                {user?.image && (
                    <img
                        src={user.image}
                        alt={user.name}
                        className="w-8 h-8 rounded-full"
                    />
                )}
                <div className="flex flex-col">
                    <span className="text-sm font-medium text-white/80">{isLoading ? "Loading..." : user?.name || "Unknown User"}</span>
                    <span className="text-xs text-white/60">{isLoading ? "Loading..." : user?.email || "No email"}</span>
                    <span className="text-xs text-white/60">{isLoading ? "Loading..." : user?.assigned_tenant?.tenant_description || ""}</span>
                </div>
            </div>
            <div className="flex items-center justify-between">
                <div className="flex">
                    {error && (
                        <button
                            className="p-2 hover:bg-white/10 rounded-lg transition-colors mr-2"
                            onClick={() => refetch()}
                            title="Refresh user data"
                        >
                            <RefreshCw className="w-5 h-5 text-white/60" />
                        </button>
                    )}
                </div>
                <div className="flex">
                    <AppConfigPopover />
                    <button
                        className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                        onClick={() => signOut()}
                        title="Sign out"
                    >
                        <LogOut className="w-5 h-5 text-white/60" />
                    </button>
                </div>
            </div>
        </div>
    );
}
