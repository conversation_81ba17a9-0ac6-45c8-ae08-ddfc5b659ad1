"use client";

import { Plus } from 'lucide-react';
import { useChats } from '../hooks/ChatState';
import { ChatAction } from '../contexts/ChatContextProvider';
import { v4 as uuid } from 'uuid';
import { useQuery } from '@tanstack/react-query';
import { getMe } from '@/fetch/api/Auth';

export function EmptyState() {
    const { dispatch } = useChats();
    const { data: user } = useQuery({
        queryKey: ['user'],
        queryFn: getMe
    });

    const firstName = user?.name?.split(' ')[0] || 'there';
    
    const createNewChat = () => {
        const chatId = uuid();
        const userId = user?.id || null;
        
        console.log(`Creating new chat from EmptyState: ${chatId} for user: ${userId || 'anonymous'}`);
        
        dispatch({ 
            type: ChatAction.CREATE_CHAT, 
            payload: { 
                chatId, 
                name: "New Chat",
                userId
            } 
        });
    };

    return (
        <div className="flex flex-col items-center justify-center h-full w-full p-8 bg-white">
            <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                    Welcome, {firstName}!
                </h1>
                <p className="text-gray-600 text-lg max-w-md">
                    Start a new chat to explore your knowledge base with AI assistance
                </p>
            </div>
            <button
                onClick={createNewChat}
                className="flex items-center gap-2 px-6 py-4 bg-[#1A1A40] text-white rounded-xl hover:bg-[#2A2A50] transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-200"
            >
                <Plus className="w-6 h-6" />
                <span className="text-lg font-semibold">Start New Chat</span>
            </button>
        </div>
    );
}
