"use client";

import "client-only";
import React, { useCallback, useEffect, useRef, useState, useMemo } from "react";
import { useChatApi } from "../../hooks/ChatApi";
import { useCurrentChat } from "../../hooks/ChatState";
import { useMessageInput } from "../../hooks/MessageInput";
import FileUpload from "@/modules/Chat/components/Messages/FileUpload";
import { Button } from "@/components/ui/button";
import { useChatActions } from "../../hooks/ChatActions";
import { ChevronRight, Plus, Settings, Database, Upload, Delete, Bot } from "lucide-react";
import { t } from "@/i18n";
import { Textarea } from "@/components/ui/textarea";
import { AppComboboxOption } from "@/components/AppCombobox";
import { useQuery } from "@tanstack/react-query";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useConfiguration } from "../../hooks/useConfiguration";
import { useCollectionValidation } from "../../hooks/useCollectionValidation";
import { useLLMs } from "../../hooks/useLLMs";
import "./MessagesInput.css";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useApiVersion } from '../../contexts/ApiVersionContext';

enum MessageTypes {
    Ask = "Ask",
    Upload = "Upload",
}

export function MessagesInput() {
    const inputRef = useRef<HTMLTextAreaElement | null>(null);
    const uploadContainerRef = useRef<HTMLDivElement>(null);
    const currentChat = useCurrentChat();
    const { message, setMessage } = useMessageInput();
    const { askMessageMutation, fileUploadMutation } = useChatApi();
    const { sendAskMessage } = useChatActions();
    const { collections } = useCollectionValidation();
    const { llms, isLoading: isLoadingLLMs } = useLLMs();
    const { useExperimentalApi, toggleApiVersion } = useApiVersion();
    const { updateConfigPath } = useConfiguration();

    // Query for user data
    const { data: userData } = useQuery({
        queryKey: ["userData"],
        queryFn: () => {
            const data = localStorage.getItem("userData");
            return data ? JSON.parse(data) : null;
        },
    });

    // Memoize filtered embeddings based on user data and collections
    const filteredEmbeddings = useMemo(() => {
        if (!collections) return [];

        const tenantDescription = userData?.assigned_tenant?.tenant_description?.toLowerCase();

        return collections
            .filter((collection) => (tenantDescription ? collection.id.toLowerCase().startsWith(tenantDescription) : true))
            .map(
                (collection) =>
                    ({
                        label: collection.name,
                        value: collection.id,
                    }) as AppComboboxOption,
            );
    }, [userData, collections]);

    const [messageType, setMessageType] = useState<MessageTypes>(MessageTypes.Ask);
    const [showSources, setShowSources] = useState(false);
    const [showModels, setShowModels] = useState(false);
    const [selectedEmbedding, setSelectedEmbedding] = useState<AppComboboxOption | null>(null);
    const [selectedModel, setSelectedModel] = useState<AppComboboxOption | null>(null);
    const [showSettings, setShowSettings] = useState(false);

    useEffect(() => {
        if (filteredEmbeddings.length && !selectedEmbedding) {
            setSelectedEmbedding(filteredEmbeddings[0]);
            updateConfigPath(["queryParams", "collection_id"] as const, filteredEmbeddings[0].value);
        }

        if (llms.length && !selectedModel) {
            const defaultModel = llms.find((model) => model.value === "gpt-4o-mini");
            if (defaultModel) {
                setSelectedModel(defaultModel);
                updateConfigPath(["rag_config", "llm"] as const, defaultModel.value);
            }
        }
    }, [filteredEmbeddings, selectedEmbedding, selectedModel, llms, updateConfigPath]);

    useEffect(() => {
        if (selectedModel?.value) {
            updateConfigPath(["rag_config", "llm"] as const, selectedModel.value);
        }
    }, [selectedModel, updateConfigPath]);

    useEffect(() => {
        if (selectedEmbedding?.value) {
            updateConfigPath(["queryParams", "collection_id"] as const, selectedEmbedding.value);
        }
    }, [selectedEmbedding, updateConfigPath]);




    const handleEmbeddingSelection = (embedding: AppComboboxOption) => {
        setSelectedEmbedding(embedding);
        setShowSources(false);
        updateConfigPath(["queryParams", "collection_id"] as const, embedding.value);
    };

    const handleModelSelection = (model: AppComboboxOption) => {
        setSelectedModel(model);
        setShowModels(false);
        updateConfigPath(["rag_config", "llm"] as const, model.value);
    };

    const sendMessage = useCallback(async () => {
        if (!message.trim() || !currentChat) return;

        const messageToSend = message;
        setMessage("");

        await sendAskMessage(messageToSend);
    }, [message, currentChat, sendAskMessage]);

    const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setMessage(e.target.value);
    };

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (messageType === MessageTypes.Upload && uploadContainerRef.current && !uploadContainerRef.current.contains(event.target as Node)) {
                setMessageType(MessageTypes.Ask);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [messageType]);

    const inputDisabled = askMessageMutation.isPending || !currentChat;

    const onKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
        if (event.shiftKey) {
            return;
        }

        if (event.key === "Enter") {
            sendMessage();
        }
    };

    const renderSourcesPopover = () => (
        <Popover
            open={showSources}
            onOpenChange={setShowSources}
        >
            <PopoverTrigger asChild>
                <button className={`flex items-center gap-1.5 px-2 py-0.5 rounded transition-colors hover:text-gray-700 ${selectedEmbedding ? "text-gray-700" : "text-gray-500"}`}>
                    <Database size={12} />
                    <span>Collection:</span>
                    <span className="text-gray-700">{selectedEmbedding?.label || "Select"}</span>
                </button>
            </PopoverTrigger>
            <PopoverContent
                className="w-48 p-1 shadow-lg rounded-lg"
                align="start"
            >
                <div className="max-h-40 overflow-auto">
                    {!collections ? (
                        <div className="p-2 text-center text-gray-500 text-sm">Loading...</div>
                    ) : (
                        filteredEmbeddings.map((embedding) => (
                            <button
                                key={embedding.value}
                                onClick={() => handleEmbeddingSelection(embedding)}
                                className={`w-full px-3 py-1.5 text-left text-xs rounded transition-colors hover:bg-gray-50 ${selectedEmbedding?.value === embedding.value ? "bg-gray-100 text-gray-900" : "text-gray-700"
                                    }`}
                            >
                                {embedding.label}
                            </button>
                        ))
                    )}
                </div>
            </PopoverContent>
        </Popover>
    );

    const renderModelsPopover = () => (
        <Popover
            open={showModels}
            onOpenChange={setShowModels}
        >
            <PopoverTrigger asChild>
                <button className={`flex items-center gap-1.5 px-2 py-0.5 rounded transition-colors hover:text-gray-700 ${selectedModel ? "text-gray-700" : "text-gray-500"}`}>
                    <Bot size={15} />
                    <span>Model:</span>
                    <span className="text-gray-700">AWS Bedrock</span>
                </button>
            </PopoverTrigger>
            <PopoverContent
                className="w-48 p-1 shadow-lg rounded-lg"
                align="start"
            >
                <div className="max-h-40 overflow-auto">
                    {isLoadingLLMs ? (
                        <div className="p-2 text-center text-gray-500 text-sm">Loading...</div>
                    ) : (
                        llms.map((model) => (
                            <button
                                key={model.value}
                                onClick={() => handleModelSelection(model)}
                                className={`w-full px-3 py-1.5 text-left text-xs rounded transition-colors hover:bg-gray-50 ${selectedModel?.value === model.value ? "bg-gray-100 text-gray-900" : "text-gray-700"
                                    }`}
                                disabled={model.disabled}
                            >
                                {model.label}
                            </button>
                        ))
                    )}
                </div>
            </PopoverContent>
        </Popover>
    );

    const renderSettingsPopover = () => (
        <Popover
            open={showSettings}
            onOpenChange={setShowSettings}
        >
            <PopoverTrigger asChild>
                <button className="flex items-center gap-1.5 px-2 py-0.5 rounded transition-colors hover:text-gray-700 text-gray-500">
                    <Settings size={12} />
                    <span>Settings</span>
                </button>
            </PopoverTrigger>
            <PopoverContent
                className="w-64 p-3 shadow-lg rounded-lg"
                align="end"
            >
                <div className="space-y-4">
                    <h3 className="text-sm font-medium">Settings</h3>

                    {/* API Version Toggle - Using context for immediate response */}
                    <div className="flex items-center justify-between">
                        <div className="space-y-0.5">
                            <Label htmlFor="api-version">API Version</Label>
                            <p className="text-xs text-gray-500">
                                {useExperimentalApi ? "Experimental" : "Stable"}
                            </p>
                        </div>
                        <Switch
                            id="api-version"
                            checked={useExperimentalApi}
                            onCheckedChange={toggleApiVersion}
                        />
                    </div>

                    {/* Add more settings here as needed */}
                </div>
            </PopoverContent>
        </Popover>
    );

    if (messageType === MessageTypes.Upload) {
        return (
            <div
                ref={uploadContainerRef}
                className="bg-white rounded-xl shadow-lg p-6"
            >
                <FileUpload
                    className="w-full"
                    renderUploadButton={(files) => (
                        <div className="flex flex-col gap-3">
                            <div className="flex justify-between items-center">
                                <Button
                                    variant="secondary"
                                    className={`flex items-center gap-2 px-3 py-1 ${files.length > 0 ? "bg-gray-700 hover:bg-gray-600" : ""}`}
                                    onClick={async (e) => {
                                        e.stopPropagation();
                                        await fileUploadMutation.mutateAsync(files.map(({ file }) => file));
                                        setMessageType(MessageTypes.Ask);
                                    }}
                                    disabled={!selectedEmbedding || files.length === 0 || fileUploadMutation.status === "pending"}
                                >
                                    <Upload
                                        size={14}
                                        className={files.length > 0 ? "text-white" : "text-gray-400"}
                                    />
                                    <span className={files.length > 0 ? "text-white" : "text-gray-400"}>
                                        {fileUploadMutation.status === "pending"
                                            ? t("Uploading...")
                                            : selectedEmbedding
                                                ? `${t("Upload to")} ${selectedEmbedding.label}`
                                                : t("Please select a collection")}
                                    </span>
                                </Button>
                                <button
                                    className="flex items-center gap-1 hover:text-gray-600 p-1 text-gray-400"
                                    onClick={() => setMessageType(MessageTypes.Ask)}
                                >
                                    <span className="text-lg">×</span>
                                </button>
                            </div>
                        </div>
                    )}
                />
            </div>
        );
    }

    return (
        <div className="messages-input-container bg-white border border-gray-200 rounded-xl shadow-lg p-6">
            <div className="flex gap-4 items-end">
                <Textarea
                    disabled={inputDisabled}
                    ref={inputRef}
                    onKeyDown={onKeyDown}
                    className="flex-1 resize-none border-0 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 rounded-xl bg-transparent focus:bg-gray-50 text-gray-800 text-base px-4 py-2 min-h-[40px] transition-colors"
                    placeholder={t("Message...")}
                    rows={1}
                    value={message}
                    onChange={handleMessageChange}
                />
                <button
                    className={`transition-colors flex items-center justify-center w-8 h-8 ${message.trim() ? "text-gray-600 hover:text-gray-800" : "text-gray-300 cursor-not-allowed"}`}
                    onClick={() => setMessage("")}
                    title="Clear message"
                    disabled={!message.trim()}
                >
                    <Delete size={20} />
                </button>
                <button
                    className={`send-button transition-all flex items-center justify-center w-8 h-8 rounded-full ${message.trim() && !inputDisabled ? "bg-[#1A1A40] text-white hover:bg-gray-500" : "bg-gray-100 text-gray-400 opacity-50 cursor-not-allowed"
                        }`}
                    disabled={!message.trim() || inputDisabled}
                    onClick={sendMessage}
                >
                    <ChevronRight size={20} />
                </button>
            </div>
            <div className="flex justify-between mt-3 text-xs text-gray-400">
                <div className="flex items-center gap-2 text-xs text-gray-500">
                    <button
                        onClick={() => setMessageType(MessageTypes.Upload)}
                        className={`flex items-center gap-1.5 px-2 py-0.5 rounded transition-colors hover:text-gray-700`}
                    >
                        <Plus size={12} />
                        <span>Add File</span>
                    </button>
                    {renderModelsPopover()}
                    {renderSourcesPopover()}
                </div>
                <div className="flex gap-4">{renderSettingsPopover()}</div>
            </div>
        </div>
    );
}
