"use client";

import "client-only";
import { t } from "@/i18n";
import { cn } from "@/utils";
import { X, File } from "lucide-react";
import { ChangeEvent, ReactNode, useCallback, useMemo, useRef, useState } from "react";
import { <PERSON>lt<PERSON>, TooltipContent, TooltipTrigger } from "../../../../components/ui/tooltip";

type FileUploadProps = Readonly<{
    className?: string;
    renderUploadButton: (files: FileItem[]) => ReactNode;
}>;

type FileItem = Readonly<{
    file: File;
    url: string;
    name: string;
    extension: string;
}>;

export default function FileUpload({ className, renderUploadButton }: FileUploadProps) {
    const [files, setFiles] = useState<FileItem[]>([]);
    const [fileEnter, setFileEnter] = useState(false);
    const inputRef = useRef<HTMLInputElement>(null);

    const onDrop = useCallback(
        (e: React.DragEvent<HTMLDivElement>) => {
            e.preventDefault();
            setFileEnter(false);

            if (!e.dataTransfer.items) {
                throw new Error("Not implemented");
            }

            [...e.dataTransfer.items].forEach((item) => {
                if (item.kind !== "file") {
                    return;
                }

                const file = item.getAsFile();

                if (file) {
                    const blobUrl = URL.createObjectURL(file);
                    const extension = file.name.split(".").pop() || "";
                    const newFile = {
                        file,
                        url: blobUrl,
                        name: file.name,
                        extension,
                    } as FileItem;

                    setFiles((allFiles) => allFiles?.concat(newFile) || [newFile]);
                }
            });
        },
        [setFiles],
    );

    const onChange = useCallback(
        (e: ChangeEvent<HTMLInputElement>) => {
            const files = e.target.files;
            if (files) {
                const fileItems = [...files].map((file) => {
                    const extension = file.name.split(".").pop() || "";
                    return {
                        file,
                        url: URL.createObjectURL(file),
                        name: file.name,
                        extension,
                    } as FileItem;
                });
                setFiles((allFiles) => allFiles?.concat(...fileItems) || fileItems);
            }
        },
        [setFiles],
    );

    const onDragOver = useCallback(
        (e: React.DragEvent<HTMLDivElement>) => {
            e.preventDefault();
            setFileEnter(true);
        },
        [setFileEnter],
    );

    const onDragLeave = useCallback(() => {
        setFileEnter(false);
    }, [setFileEnter]);

    const onDragEnd = useCallback(
        (e: React.DragEvent<HTMLDivElement>) => {
            e.preventDefault();
            setFileEnter(false);
        },
        [setFileEnter],
    );

    const removeFile = useCallback(
        (url: string) => {
            setFiles((allFiles) => allFiles.filter((file) => file.url !== url));
        },
        [setFiles],
    );

    const addFile = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        inputRef.current?.click();
    }, []);

    const fileTiles = useMemo(
        () =>
            files.map((file) => (
                <div
                    key={file.url}
                    className="relative flex items-start"
                >
                    <Tooltip>
                        <TooltipTrigger className="flex flex-col">
                            <File
                                className="cursor-help my-1 text-sidebar/90"
                                size={48}
                            />
                            <X
                                size={16}
                                className="border rounded-full border-red-500 bg-red-500 text-white absolute top-0 right-0"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    removeFile(file.url);
                                }}
                            />
                        </TooltipTrigger>
                        <TooltipContent
                            side="bottom"
                            sideOffset={-5}
                            className="text-xs text-secondary font-medium bg-accent border-none rounded-none p-1 px-4"
                        >
                            <span>
                                {file.name} ({file.extension})
                            </span>
                        </TooltipContent>
                    </Tooltip>
                </div>
            )),
        [files, removeFile],
    );

    return (
        <div className={className}>
            <div
                onDragOver={onDragOver}
                onDragLeave={onDragLeave}
                onDragEnd={onDragEnd}
                onDrop={onDrop}
                className={cn(
                    fileEnter ? "bg-primary" : "bg-white",
                    "transition-colors mx-auto w-full h-full flex relative items-start",
                    "border-2 rounded-xl border-dashed p-6",
                )}
            >
                <div className="flex-1 flex flex-wrap gap-2 pr-32">
                    {fileTiles}
                    <div className={cn("text-muted w-full h-full flex-col justify-center text-center", fileTiles.length === 0 ? "flex" : "hidden")}>
                        <span>{t("Drag & Drop to upload")}</span>
                        <button 
                            onClick={addFile}
                            className="text-sidebar font-medium hover:text-gray-600 transition-colors"
                        >
                            {t("or browse")}
                        </button>
                    </div>
                </div>
                <input
                    ref={inputRef}
                    id="file"
                    type="file"
                    className="hidden"
                    multiple={true}
                    onChange={onChange}
                />
                <div className="absolute right-6 top-6">
                    {renderUploadButton(files)}
                </div>
            </div>
        </div>
    );
}
