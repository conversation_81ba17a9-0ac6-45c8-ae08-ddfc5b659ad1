"use client";

import "client-only";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ChatAction, ChatMessage } from "@/modules/Chat/contexts/ChatContextProvider";
import { Skeleton } from "@/components/ui/skeleton";
import { useChats } from "../../hooks/ChatState";
import { useCallback } from "react";
import { t } from "@/i18n";
import { Copy } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { marked } from "marked";
import "./ChatbotMessage.css";
import { AnimatedMessageSkeleton } from "./AnimatedMessageSkeleton";

type MessageProps = Readonly<{
    message: ChatMessage;
    retry: () => void;
}>;

export default function Message({ message, retry }: MessageProps) {
    const { dispatch } = useChats();
    const { messageId, query, result, isError } = message;
    const { toast } = useToast();

    const selectChat = useCallback(() => dispatch({ type: ChatAction.SET_CURRENT_MESSAGE, payload: { messageId } }), [dispatch, messageId]);

    return (
        <div
            key={messageId}
            className="w-full flex flex-col gap-2 mt-16 first:mt-0"
        >
            <div className="flex gap-4 my-1 justify-end">
                <div className="break-words whitespace-pre-wrap bg-primary text-black rounded-3xl py-4 px-8 max-w-[500px]">{query}</div>
            </div>
            {isError ? (
                <div>
                    <ErroredResultSkeleton retry={retry} />
                </div>
            ) : result === null ? (
                <AnimatedMessageSkeleton />
            ) : result?.result ? (
                <div className="my-1 p-3 flex w-full last:mb-0 last:pb-0">
                    <Avatar className="self-start mt-1 flex-shrink-0">
                        <AvatarImage src="/chatbot-avatar.png" />
                        <AvatarFallback className="bg-primary">CN</AvatarFallback>
                    </Avatar>
                    <div className="chatbot-message">
                        <div
                            className="chatbot-message-content group"
                            onClick={selectChat}
                        >
                            <div dangerouslySetInnerHTML={{ __html: marked.parse(result?.result) }} />
                            <button
                                onClick={(e) => {
                                    e.stopPropagation();
                                    navigator.clipboard.writeText(result?.result);
                                    toast({
                                        description: t("Message copied to clipboard"),
                                    });
                                }}
                                className="absolute right-4 bottom-4 text-gray-400 hover:text-gray-600 transition-colors"
                            >
                                <Copy className="h-4 w-4" />
                            </button>
                        </div>
                    </div>
                </div>
            ) : (
                <MessageResultSkeleton />
            )}
        </div>
    );
}

function MessageResultSkeleton() {
    return (
        <div className="flex items-center self-start space-x-4 my-1 p-3 last:mb-0 pb-0">
            <Skeleton className="h-12 w-12 rounded-full bg-primary" />
            <div className="space-y-2">
                <Skeleton className="h-4 w-[180px] bg-primary" />
                <Skeleton className="h-4 w-[120px] bg-primary" />
            </div>
        </div>
    );
}

type ErroredResultSkeletonProps = Readonly<{
    retry: () => void;
}>;

function ErroredResultSkeleton({ retry }: ErroredResultSkeletonProps) {
    return (
        <div className="flex justify-end select-none items-center self-start space-x-2 rounded-lg p-1 pt-0 text-sm">
            <div className="text-end">
                <div
                    onClick={retry}
                    className="h-4 w-[200px] text-red-400 cursor-pointer"
                >
                    {t("Failed: Click to retry")}
                </div>
            </div>
        </div>
    );
}
