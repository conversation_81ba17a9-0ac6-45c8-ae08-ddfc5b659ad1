@keyframes pulse {
    0% {
        opacity: 0.4;
        transform: scale(0.8);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0.4;
        transform: scale(0.8);
    }
}

.animated-skeleton {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    gap: 1rem;
}

.animated-skeleton-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: #1A1A40;
    animation: pulse 2s ease-in-out infinite;
}

.typing-container {
    display: flex;
    align-items: center;
}

.typing-dots {
    display: flex;
    gap: 6px;
    padding: 12px 16px;
    background-color: #1A1A40;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(26, 26, 64, 0.2);
}

.typing-dot {
    width: 10px;
    height: 10px;
    background-color: white;
    border-radius: 50%;
    opacity: 0.4;
    transition: all 0.2s ease;
}

.typing-dot:nth-child(1) {
    animation: pulse 1s ease-in-out infinite;
}

.typing-dot:nth-child(2) {
    animation: pulse 1s ease-in-out infinite 0.33s;
}

.typing-dot:nth-child(3) {
    animation: pulse 1s ease-in-out infinite 0.66s;
}
