@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

:root {
    --accent-color: #2563EB;
}

.chatbot-message {
    @apply flex-1 ml-4 relative;
    font-family: 'Poppins', sans-serif;
    text-rendering: optimizeLegibility;
    color: #545454;
    line-height: 1.6em;
}

.chatbot-message-content {
    @apply cursor-pointer bg-gray-50 rounded-3xl py-4 px-8 shadow-sm relative;
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
}

.chatbot-message-content:hover {
    @apply bg-gray-100;
}

.chatbot-message-content h1 {
    font-weight: 700;
    color: #333;
    font-size: 1.6em;
    line-height: 1.3em;
    margin-bottom: .78571em;
}

.chatbot-message-content h2 {
    font-weight: 600;
    color: #333;
    font-size: 1.3em;
    line-height: 1em;
    margin-top: 2em;
    margin-bottom: .6em;
}

.chatbot-message-content h3 {
    font-weight: 600;
    color: #333;
    font-size: 1.15em;
    line-height: 1em;
    margin-top: 1em;
    margin-bottom: .6em;
}

.chatbot-message-content h4 {
    font-weight: 500;
    color: #333;
    font-size: 1em;
    line-height: 1em;
    margin-top: 1em;
    margin-bottom: .6em;
}

.chatbot-message-content p {
    @apply mb-4;
    max-width: 100%;
    overflow-wrap: break-word;
    hyphens: auto;
}

.chatbot-message-content strong,
.chatbot-message-content b {
    font-weight: 700;
}

.chatbot-message-content em,
.chatbot-message-content i {
    font-style: italic;
}

.chatbot-message-content u {
    text-decoration: none;
    background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0) 50%, var(--accent-color) 50%);
    background-repeat: repeat-x;
    background-size: 2px 2px;
    background-position: 0 1.05em;
}

.chatbot-message-content s {
    color: #878787;
}

.chatbot-message-content a {
    color: var(--accent-color);
    text-decoration: none;
}

.chatbot-message-content a:hover {
    text-decoration: underline;
}

.chatbot-message-content blockquote {
    display: block;
    margin-left: -1em;
    padding-left: 0.8em;
    border-left: 0.2em solid var(--accent-color);
    background-color: transparent;
}

.chatbot-message-content hr {
    height: 1px;
    border: 0;
    background-color: #dedede;
    margin: .7em auto;
}

.chatbot-message-content ul,
.chatbot-message-content ol {
    margin: 0.5em 0 1.5em;
    padding-left: 1.5em;
}

.chatbot-message-content ul {
    list-style: none;
}

.chatbot-message-content ul li {
    position: relative;
    padding-left: 0.5em;
    margin: 0.3em 0;
    line-height: 1.6;
}

.chatbot-message-content ul li:before {
    content: "•";
    color: var(--accent-color);
    position: absolute;
    left: -1em;
    top: -0.1em;
    font-size: 1.2em;
}

.chatbot-message-content ul ul {
    margin: 0.3em 0 0.3em 0.5em;
}

.chatbot-message-content ol {
    counter-reset: section;
    list-style: none;
}

.chatbot-message-content ol > li {
    position: relative;
    padding-left: 0.5em;
    margin: 0.3em 0;
    line-height: 1.6;
}

.chatbot-message-content ol > li:before {
    counter-increment: section;
    content: counter(section) ".";
    color: var(--accent-color);
    position: absolute;
    right: calc(100% - 0.3em);
    top: 0;
    text-align: right;
    font-weight: 500;
}

.chatbot-message-content ol ol {
    margin: 0.3em 0 0.3em 0.5em;
    counter-reset: subsection;
}

.chatbot-message-content ol ol > li {
    counter-increment: subsection;
}

.chatbot-message-content ol ol > li:before {
    content: counter(subsection) ".";
}

.chatbot-message-content ol ol ol {
    counter-reset: subsubsection;
}

.chatbot-message-content ol ol ol > li {
    counter-increment: subsubsection;
}

.chatbot-message-content ol ol ol > li:before {
    content: counter(subsubsection) ".";
}

.chatbot-message-content ol li p,
.chatbot-message-content ul li p {
    display: inline;
    margin: 0;
}

.chatbot-message-content code {
    @apply font-mono text-sm;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: "Fira Code Retina", monospace;
    color: var(--accent-color);
    background-color: #f8fafc;
    padding: 0.2em 0.4em;
    border-radius: 0.25em;
    border: 1px solid #dedede;
}

.chatbot-message-content pre {
    @apply my-4 p-4 bg-gray-50 rounded-lg overflow-x-auto;
    max-width: 100%;
    white-space: pre-wrap;
    word-wrap: break-word;
    background-color: #f8fafc;
    border-radius: 0.5em;
    overflow-x: auto;
    border: 1px solid #dedede;
}

.chatbot-message-content pre code {
    font-family: "Fira Code Retina", monospace;
    font-size: 0.9em;
    color: var(--accent-color);
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    display: block;
    line-height: 1.5;
    border: none;
}

.chatbot-message-content table {
    width: 100%;
    margin-bottom: 1.5em;
}

.chatbot-message-content table tr {
    border-bottom: 1px solid #dedede;
}

.chatbot-message-content table th {
    font-weight: 700;
}

.chatbot-message-content table td,
.chatbot-message-content table th {
    vertical-align: top;
    padding: .2em 1.5em;
    font-size: .95em;
}

.chatbot-message-content thead tr {
    border-bottom: 4px double #dedede;
}

.chatbot-message-copy {
    @apply relative self-end;
}

.chatbot-message-copy-button {
    @apply self-end cursor-pointer transition-colors;
    color: var(--accent-color);
}

.chatbot-message-copy-button:hover {
    opacity: 0.8;
}
