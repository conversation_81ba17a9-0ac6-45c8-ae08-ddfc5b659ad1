"use client";

import "client-only";
import { useCallback, useEffect, useMemo, useRef } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useCurrentChat } from "@/modules/Chat/hooks/ChatState";
import Message from "./Message";
import { useChatActions } from "../../hooks/ChatActions";
import { MessagesInput } from "./MessagesInput";
import ProductivitySection from "../ProductivitySection/ProductivitySection";

export default function Messages() {
    const chatScrollViewEnd = useRef<HTMLDivElement>(null);
    const currentChat = useCurrentChat();
    const { retry } = useChatActions();

    const messagesCount = currentChat ? Object.values(currentChat.messages).length : 0;
    const answerCount = currentChat ? Object.values(currentChat.messages).filter((message) => message?.result !== null).length : 0;

    const scrollEnd = useCallback(() => {
        chatScrollViewEnd.current?.scrollIntoView(false);
    }, [chatScrollViewEnd]);

    const currentChatMessages = useMemo(() => {
        return Object.values(currentChat?.messages || []).map((message) => {
            if (!message) {
                throw new Error(`Message is undefined`);
            }

            if (!currentChat) {
                throw new Error(`Current chat is not set`);
            }

            return (
                <Message
                    retry={() => retry(message.messageId, message.query)}
                    key={message.messageId}
                    message={message}
                />
            );
        });
    }, [currentChat, retry]);

    useEffect(() => {
        scrollEnd();
    }, [scrollEnd, messagesCount, answerCount]);

    return (
        <div className="flex flex-col h-full">
            <div className="flex-1 overflow-auto ">
                <ScrollArea className="bg-sidebar-accent-foreground-red w-full h-full relative px-4 pt-16 flex flex-col-reverse flex-1 [&_.scrollbar-thumb]:bg-gray-400 [&_.scrollbar-thumb:hover]:bg-gray-500 [&_.scrollbar-track]:bg-gray-200">
                    <div className="flex">
                        <div className="flex-auto basis-2/12 shrink"></div>
                        <div className="flex-auto max-w-6xl">
                            {currentChat ? (
                                messagesCount > 0 ? (
                                    <>
                                        {currentChatMessages}
                                        {/* <div className="flex-auto">{currentChatMessages}</div> */}
                                    </>
                                ) : (
                                    <div className="w-full h-full flex items-center">
                                        <ProductivitySection />
                                    </div>
                                )
                            ) : (
                                <div className="w-full h-full flex items-center">
                                    <div className="w-full">
                                        <ProductivitySection />
                                    </div>
                                </div>
                            )}
                            <div
                                className="h-6"
                                ref={chatScrollViewEnd}
                            />
                        </div>
                        <div className="flex-auto basis-2/12 shrink"></div>
                    </div>
                </ScrollArea>
            </div>
            <div className=" h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent mb-1" />
            <div className="px-2 py-2 border-0 flex flex-row">
                <div className="flex-auto basis-2/12 shrink" />
                <div className="flex-auto basis-8/12 max-w-4xl">
                    <MessagesInput />
                </div>
                <div className="flex-auto basis-2/12 shrink" />
            </div>
        </div>
    );
}
