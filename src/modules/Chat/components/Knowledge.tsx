"use client";

import "client-only";
import { LoadKnowlageBase, DeleteDocument, CreateCollection, listEmbeddingVectorCollections, DeleteCollection, downloadCollectionDocuments } from "@/fetch/api/KnowledgeBase";
import { filesUpload } from "@/fetch/api/Chat";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { t } from "@/i18n";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { Database, MoreVertical, ChevronDown, Plus, RefreshCw, File as FileIcon, X, GitBranch } from "lucide-react";
import { useState, useMemo, useCallback, useRef, ChangeEvent } from "react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Collapsible, CollapsibleContent } from "@/components/ui/collapsible";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/utils";
import { captureError } from "@/utils/errorHandling";
import GraphView from "./Knowledge/GraphView/index";
import GraphProcessingComponent from "./GraphProcessingComponent";

interface SimpleDocument {
    id: string;
    name: string;
    status: string;
    created_at: string;
    collections: string[];
    document_type: string;
    keywords: string[];
    source_file: {
        id: string;
        name: string;
        f_bytes: string;
    };
}

interface KnowledgeProps {
    onDocumentSelect: (document: SimpleDocument | null) => void;
}

interface FileItem {
    file: File;
    url: string;
    name: string;
    extension: string;
}
export const CONFIGURE_STORAGE_KEY = "config_tmp";

export const configureDefaults = {
    rag_config: {
        temperature: 0,
        llm: "gpt-4o-mini",
        top_k: 10,
        seed: null,
    },
    additional_context: "",
    alpha: 0,
    queryParams: {
        collection_id: "",
    },
};

function CollectionFileUpload({ collectionId }: { collectionId: string }) {
    const [files, setFiles] = useState<FileItem[]>([]);
    const [fileEnter, setFileEnter] = useState(false);
    const inputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();
    const queryClient = useQueryClient();

    const uploadMutation = useMutation({
        mutationFn: async (filesToUpload: File[]) => {
            // Store collection_id in localStorage temporarily for the upload
            const currentConfig = JSON.parse(localStorage.getItem(CONFIGURE_STORAGE_KEY) || JSON.stringify(configureDefaults));
            const previousCollectionId = currentConfig.queryParams.collection_id;

            currentConfig.queryParams.collection_id = collectionId;
            localStorage.setItem(CONFIGURE_STORAGE_KEY, JSON.stringify(currentConfig));

            try {
                await filesUpload(filesToUpload);
            } finally {
                // Restore the previous collection_id
                currentConfig.queryParams.collection_id = previousCollectionId;
                localStorage.setItem(CONFIGURE_STORAGE_KEY, JSON.stringify(currentConfig));
            }
        },
        onSuccess: () => {
            toast({
                title: t("Upload successful"),
                description: t("Documents have been uploaded to the collection"),
            });
            setFiles([]);
            queryClient.invalidateQueries({ queryKey: ["knowledgeBase"] });
        },
        onError: () => {
            toast({
                variant: "destructive",
                title: t("Upload failed"),
                description: t("Failed to upload documents. Please try again."),
            });
        },
    });

    const onDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setFileEnter(false);

        if (!e.dataTransfer.items) return;

        [...e.dataTransfer.items].forEach((item) => {
            if (item.kind !== "file") return;

            const file = item.getAsFile();
            if (!file) return;

            const blobUrl = URL.createObjectURL(file);
            const extension = file.name.split(".").pop() || "";

            setFiles((prev) => [
                ...prev,
                {
                    file,
                    url: blobUrl,
                    name: file.name,
                    extension,
                },
            ]);
        });
    }, []);

    const onChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
        const fileList = e.target.files;
        if (!fileList) return;

        const newFiles = Array.from(fileList).map((file) => ({
            file,
            url: URL.createObjectURL(file),
            name: file.name,
            extension: file.name.split(".").pop() || "",
        }));

        setFiles((prev) => [...prev, ...newFiles]);
    }, []);

    const onDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        setFileEnter(true);
    }, []);

    const onDragLeave = useCallback(() => {
        setFileEnter(false);
    }, []);

    const removeFile = useCallback((url: string) => {
        setFiles((prev) => prev.filter((file) => file.url !== url));
    }, []);

    const handleUpload = useCallback(async () => {
        uploadMutation.mutate(files.map((f) => f.file));
    }, [files, uploadMutation]);

    return (
        <div
            className={cn("relative border-2 border-dashed rounded-lg p-4 transition-colors", fileEnter ? "border-blue-500 bg-blue-50" : "border-gray-300")}
            onDrop={onDrop}
            onDragOver={onDragOver}
            onDragLeave={onDragLeave}
        >
            <input
                ref={inputRef}
                type="file"
                multiple
                className="hidden"
                onChange={onChange}
            />

            {files.length === 0 ? (
                <div className="text-center">
                    <Button
                        variant="outline"
                        onClick={() => inputRef.current?.click()}
                        className="w-full"
                    >
                        {t("Choose files or drag them here")}
                    </Button>
                </div>
            ) : (
                <div className="space-y-2">
                    {files.map((file) => (
                        <div
                            key={file.url}
                            className="flex items-center justify-between bg-gray-50 p-2 rounded"
                        >
                            <div className="flex items-center gap-2">
                                <FileIcon className="h-4 w-4 text-gray-500" />
                                <span className="text-sm">{file.name}</span>
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFile(file.url)}
                            >
                                <X className="h-4 w-4" />
                            </Button>
                        </div>
                    ))}
                    <Button
                        className="w-full"
                        onClick={handleUpload}
                        disabled={uploadMutation.isPending}
                    >
                        {uploadMutation.isPending ? t("Uploading...") : t("Upload")}
                    </Button>
                </div>
            )}
        </div>
    );
}

export default function Knowledge({ onDocumentSelect }: KnowledgeProps) {
    const queryClient = useQueryClient();
    const { toast } = useToast();
    const [documentToDelete, setDocumentToDelete] = useState<SimpleDocument | null>(null);
    const [collectionToDelete, setCollectionToDelete] = useState<{ id: string; name: string } | null>(null);
    const [openCollections, setOpenCollections] = useState<Record<string, boolean>>({});
    const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);
    const [newCollectionName, setNewCollectionName] = useState("");
    const [showNewCollectionDialog, setShowNewCollectionDialog] = useState(false);
    const [isGraphView, setIsGraphView] = useState(false);

    // Create a ref for the WASM object to pass to both GraphView and GraphProcessingComponent
    const wasmObjectRef = useRef<HTMLObjectElement>(null);

    const isValidCollectionName = (name: string) => /^[a-z0-9]*$/.test(name);
    const hasInvalidCharacters = newCollectionName.length > 0 && !isValidCollectionName(newCollectionName);

    // Move the useQuery calls before using their results
    const { data: knowledgeBase, isPending: isKnowledgeBasePending } = useQuery({
        queryKey: ["knowledgeBase"],
        queryFn: LoadKnowlageBase,
        refetchOnWindowFocus: false,
    });

    const { data: userData } = useQuery({
        queryKey: ["userData"],
        queryFn: () => {
            const data = localStorage.getItem("userData");
            return data ? JSON.parse(data) : null;
        },
    });

    const { data: embeddingCollections, isPending: isCollectionsPending } = useQuery({
        queryKey: ["embeddingCollections"],
        queryFn: listEmbeddingVectorCollections,
        refetchOnWindowFocus: false,
    });

    const [isRefreshing, setIsRefreshing] = useState(false);
    // Now isKnowledgeBasePending and isCollectionsPending are defined before being used
    const isPending = isKnowledgeBasePending || isCollectionsPending || isRefreshing;

    // Function to refresh all data - wrapped in useCallback to fix another warning
    const refreshData = useCallback(async () => {
        setIsRefreshing(true);
        try {
            // Invalidate queries to force a fresh fetch
            await Promise.all([queryClient.invalidateQueries({ queryKey: ["knowledgeBase"] }), queryClient.invalidateQueries({ queryKey: ["embeddingCollections"] })]);
        } catch (error) {
            captureError(error, {
                action: "refreshData",
                component: "Knowledge",
            });
        } finally {
            setIsRefreshing(false);
        }
    }, [queryClient]);

    // Handler for when graph processing completes
    // const handleProcessingComplete = useCallback(() => {
    //     toast({
    //         title: t("Graph processing complete"),
    //         description: t("All processing steps have been completed successfully."),
    //     });
    //     // Don't refresh the entire page data, as the WASM app is already updated after each step
    // }, [toast]);

    // Handler for when graph processing completes
    const handleProcessingComplete = useCallback(() => {
        const completionToastKey = 'graph-completion-toast-shown';
        if (!sessionStorage.getItem(completionToastKey)) {
            toast({
                title: t("Graph processing complete"),
                description: t("All processing steps have been completed successfully."),
            });
            // Record that we've shown this toast
            sessionStorage.setItem(completionToastKey, 'true');
        }

    }, [toast]);

    // Group documents by collection
    const documentsByCollection = useMemo(() => {
        if (!knowledgeBase?.documents) return {};

        return knowledgeBase.documents.reduce((acc: Record<string, SimpleDocument[]>, doc) => {
            doc.collections.forEach((collectionId) => {
                // Use exact collection ID, maintain case sensitivity
                if (!acc[collectionId]) {
                    acc[collectionId] = [];
                }
                acc[collectionId].push({
                    id: doc.id,
                    name: doc.name,
                    status: doc.status,
                    created_at: doc.created_at,
                    collections: doc.collections,
                    document_type: doc.document_type,
                    keywords: doc.keywords,
                    source_file: doc.source_file,
                });
            });
            return acc;
        }, {});
    }, [knowledgeBase]);

    // Filter collections by tenant
    const filteredCollections = useMemo(() => {
        const tenantDescription = userData?.assigned_tenant?.tenant_description;

        console.log("Filtering collections:", {
            tenantDescription,
            totalCollections: embeddingCollections?.length || 0,
            allCollectionIds: embeddingCollections?.map((c) => c.id),
        });

        const filtered = (embeddingCollections || []).filter((collection) =>
            tenantDescription
                ? // Use exact string matching, maintain case sensitivity
                  collection.name.startsWith(tenantDescription)
                : true,
        );

        console.log("Collections after tenant filtering:", {
            filteredCount: filtered.length,
            filteredCollectionIds: filtered.map((c) => c.id),
            documentsInCollections: filtered.map((c) => ({
                collectionId: c.id,
                documentCount: documentsByCollection[c.id]?.length || 0,
            })),
        });

        return filtered;
    }, [embeddingCollections, userData?.assigned_tenant?.tenant_description, documentsByCollection]);

    const protectedCollectionIds = ["Sita", "Sita_deicing", "Sita_tests"];

    const deleteMutation = useMutation({
        mutationFn: DeleteDocument,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["knowledgeBase"] });
            setDocumentToDelete(null);
        },
        onError: (error) => {
            captureError(error, {
                action: "deleteDocument",
                component: "Knowledge",
            });
            console.error("Failed to delete document:", error);
            setDocumentToDelete(null);
        },
    });

    const deleteCollectionMutation = useMutation({
        mutationFn: DeleteCollection,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["knowledgeBase"] });
            queryClient.invalidateQueries({ queryKey: ["embeddingCollections"] });
            setCollectionToDelete(null);
        },
        onError: (error) => {
            captureError(error, {
                action: "deleteCollection",
                component: "Knowledge",
            });
        },
    });

    const createCollectionMutation = useMutation({
        mutationFn: CreateCollection,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["knowledgeBase"] });
            queryClient.invalidateQueries({ queryKey: ["embeddingCollections"] });
            setShowNewCollectionDialog(false);
            setNewCollectionName("");
        },
        onError: (error) => {
            captureError(error, {
                action: "createCollection",
                component: "Knowledge",
            });
            console.error("Failed to create collection:", error);
        },
    });

    const handleDeleteDocument = async (document: SimpleDocument) => {
        setDocumentToDelete(document);
    };

    const confirmDelete = async () => {
        if (documentToDelete) {
            try {
                await deleteMutation.mutateAsync(documentToDelete.id);
            } catch (error) {
                captureError(error, {
                    action: "confirmDelete",
                    component: "Knowledge",
                });
                console.error("Error deleting document:", error);
            }
        }
    };

    const handleMenuSelect = (documentId: string, document: SimpleDocument) => {
        setOpenDropdownId(null);
        handleDeleteDocument(document);
    };

    const handleCreateCollection = async () => {
        if (newCollectionName.trim()) {
            try {
                const prefix = userData?.assigned_tenant?.tenant_description;
                const fullName = prefix ? `${prefix}_${newCollectionName.trim()}` : newCollectionName.trim();
                await createCollectionMutation.mutateAsync(fullName);
            } catch (error) {
                captureError(error, {
                    action: "createCollection",
                    collectionName: newCollectionName,
                    component: "Knowledge",
                });
                console.error("Error creating collection:", error);
            }
        }
    };

    const handleDeleteCollection = (id: string, name: string) => {
        if (protectedCollectionIds.includes(id)) {
            return;
        }
        // Close the dropdown first
        setOpenDropdownId(null);
        // Then set the collection to delete
        setCollectionToDelete({ id, name });
    };


    const handleDownloadCollection = async (id: string) => {
        try {
            // Close the dropdown first
            setOpenDropdownId(null);
            
            // Call the API function (NO toLowerCase conversion)
            const blob = await downloadCollectionDocuments(id);
            
            // Create a download link for the blob
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', `collection_${id}.zip`);
            document.body.appendChild(link);
            link.click();
            
            // Cleanup
            if (link.parentNode) {
                link.parentNode.removeChild(link);
            }
            window.URL.revokeObjectURL(url);
            
            toast({
                title: t("Download started"),
                description: t("Collection downloaded successfully")
            });
        } catch (error) {
            console.error("Download error:", error);
            toast({
                variant: "destructive",
                title: t("Download failed"),
                description: t("Failed to download collection. Please try again.")
            });
        }
    };
       

    const confirmDeleteCollection = async () => {
        if (collectionToDelete) {
            try {
                await deleteCollectionMutation.mutateAsync(collectionToDelete.id);
            } catch (error) {
                captureError(error, {
                    action: "confirmDeleteCollection",
                    component: "Knowledge",
                });
                console.error("Error deleting collection:", error);
            }
        }
    };

    const formatDate = (dateString: string) => {
        try {
            return format(new Date(dateString), "MMM d, yyyy HH:mm");
        } catch {
            return dateString;
        }
    };

    const LoadingSkeleton = () => (
        <div className="space-y-4">
            {[1, 2, 3].map((i) => (
                <div
                    key={i}
                    className="animate-pulse"
                >
                    <div className="flex items-center justify-between gap-2 mb-4">
                        <div className="flex items-center gap-2">
                            <div className="h-4 w-4 bg-gray-200 rounded" />
                            <div className="h-4 w-4 bg-gray-200 rounded" />
                            <div className="h-6 w-32 bg-gray-200 rounded" />
                            <div className="h-4 w-16 bg-gray-200 rounded ml-2" />
                        </div>
                        <div className="h-8 w-8 bg-gray-200 rounded" />
                    </div>
                    <div className="space-y-3 mt-2">
                        <div className="h-8 bg-gray-100 rounded flex items-center px-4">
                            <div className="grid grid-cols-4 gap-4 w-full">
                                <div className="h-4 bg-gray-200 rounded col-span-1" />
                                <div className="h-4 bg-gray-200 rounded col-span-1" />
                                <div className="h-4 bg-gray-200 rounded col-span-1" />
                                <div className="h-4 bg-gray-200 rounded col-span-1" />
                            </div>
                        </div>
                        {[1, 2].map((row) => (
                            <div
                                key={row}
                                className="h-10 bg-gray-50 rounded flex items-center px-4"
                            >
                                <div className="grid grid-cols-4 gap-4 w-full">
                                    <div className="h-3 bg-gray-200 rounded col-span-1 w-3/4" />
                                    <div className="h-3 bg-gray-200 rounded col-span-1 w-1/2" />
                                    <div className="h-3 bg-gray-200 rounded col-span-1 w-2/3" />
                                    <div className="h-3 bg-gray-200 rounded col-span-1 w-8" />
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            ))}
        </div>
    );

    const collectionTables = useMemo(() => {
        if (!knowledgeBase?.documents) return null;

        return (
            <div>
                {filteredCollections.map((collection) => {
                    const title = collection.name;
                    const documents = documentsByCollection[collection.id] || [];

                    return (
                        <div
                            key={collection.id}
                            className="mb-6"
                        >
                            <Collapsible
                                open={openCollections[title] ?? false}
                                onOpenChange={(open) => setOpenCollections((prev) => ({ ...prev, [title]: open }))}
                            >
                                <div className="flex items-center justify-between gap-2 mb-4">
                                    <div className="flex items-center gap-2">
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="flex items-center gap-2 p-0 h-auto hover:bg-transparent"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setOpenCollections((prev) => ({
                                                    ...prev,
                                                    [title]: !(prev[title] ?? false),
                                                }));
                                            }}
                                        >
                                            <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform ${(openCollections[title] ?? false) ? "transform rotate-180" : ""}`} />
                                            <Database className="h-4 w-4 text-gray-500" />
                                            <h2 className="text-lg font-medium text-gray-900">{title}</h2>
                                            <span className="text-sm text-gray-500">({documents.length})</span>
                                        </Button>
                                    </div>
                                    <DropdownMenu
                                        open={openDropdownId === title}
                                        onOpenChange={(open) => setOpenDropdownId(open ? title : null)}
                                    >
                                        <DropdownMenuTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                className="h-8 w-8 p-0"
                                                aria-label={`Actions for ${title} collection`}
                                            >
                                                <span className="sr-only">Open menu for {title} collection</span>
                                                <MoreVertical className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent
                                            align="end"
                                            onCloseAutoFocus={(event) => {
                                                event.preventDefault();
                                            }}
                                            onEscapeKeyDown={(event) => {
                                                event.preventDefault();
                                                setOpenDropdownId(null);
                                            }}
                                        >
                                            {protectedCollectionIds.includes(collection.id) ? (
                                                <TooltipProvider delayDuration={0}>
                                                    <Tooltip defaultOpen>
                                                        <TooltipTrigger asChild>
                                                            <DropdownMenuItem
                                                                className="text-gray-400 cursor-not-allowed hover:!bg-blue-50 hover:!text-gray-400 data-[highlighted]:!bg-blue-50 data-[highlighted]:!text-gray-400"
                                                                onSelect={(e) => e.preventDefault()}
                                                                onPointerLeave={(e) => e.preventDefault()}
                                                            >
                                                                {t("Delete")}
                                                            </DropdownMenuItem>
                                                        </TooltipTrigger>
                                                        <TooltipContent
                                                            side="right"
                                                            sideOffset={5}
                                                        >
                                                            <p>{t("This collection is protected and cannot be removed")}</p>
                                                        </TooltipContent>
                                                    </Tooltip>
                                                </TooltipProvider>
                                            ) : (
                                                <DropdownMenuItem
                                                    className="text-red-600 hover:!bg-blue-50 hover:!text-blue-600 data-[highlighted]:!bg-blue-50 data-[highlighted]:!text-blue-600"
                                                    onSelect={() => handleDeleteCollection(collection.id, title)}
                                                >
                                                    {t("Delete")}
                                                </DropdownMenuItem>
                                                
                                            )}
                                            <DropdownMenuItem
                                                                    className="text-blue-600 hover:!bg-blue-50 hover:!text-blue-600 data-[highlighted]:!bg-blue-50 data-[highlighted]:!text-blue-600"
                                                                    onSelect={() => handleDownloadCollection(collection.id)}
                                                                >
                                                                    {t("Download")}
                                                                </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                                <CollapsibleContent>
                                    <p className="text-xs text-white mt-1 font-mono">{collection.id}</p>

                                    <div className="mb-4">
                                        <CollectionFileUpload collectionId={collection.id} />
                                    </div>

                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead className="w-[300px]">{t("Name")}</TableHead>
                                                <TableHead>{t("Status")}</TableHead>
                                                <TableHead>{t("Created")}</TableHead>
                                                <TableHead>{t("Keywords")}</TableHead>
                                                <TableHead className="w-10"></TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {documents.length > 0 ? (
                                                documents.map(({ name, status, id, created_at, collections, document_type, keywords, source_file }) => (
                                                    <TableRow
                                                        key={id}
                                                        className="hover:bg-gray-50 cursor-pointer h-8"
                                                        onClick={() =>
                                                            onDocumentSelect({
                                                                id,
                                                                name,
                                                                status,
                                                                created_at,
                                                                collections,
                                                                document_type,
                                                                keywords,
                                                                source_file,
                                                            })
                                                        }
                                                    >
                                                        <TableCell className="py-0.5 text-xs max-w-[300px] whitespace-normal break-words">{name}</TableCell>
                                                        <TableCell className="py-0.5 text-xs">
                                                            {status === "processed" ? (
                                                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">Processed</span>
                                                            ) : status === "failedd" ? (
                                                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">Failed</span>
                                                            ) : status === "in_process" ? (
                                                                <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                                    <div className="w-2 h-2 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                                                                    Processing
                                                                </span>
                                                            ) : (
                                                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">{status}</span>
                                                            )}
                                                        </TableCell>
                                                        <TableCell className="py-0.5 text-xs whitespace-nowrap">{formatDate(created_at)}</TableCell>
                                                        <TableCell className="py-0.5 text-xs whitespace-nowrap">

                                                        {keywords?.length > 0 && (
                                <div className="text-xs">
                                    <div className="mt-1">
                                        {keywords.map((keyword, index) => (
                                            <span
                                                key={index}
                                                className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 mr-1 mb-1"
                                            >
                                                {keyword}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            )}

                                                        </TableCell>
                                                        <TableCell className="py-0.5 text-xs w-10">
                                                            <DropdownMenu
                                                                open={openDropdownId === id}
                                                                onOpenChange={(open) => setOpenDropdownId(open ? id : null)}
                                                            >
                                                                <DropdownMenuTrigger asChild>
                                                                    <Button
                                                                        variant="ghost"
                                                                        className="h-8 w-8 p-0"
                                                                        onClick={(e) => e.stopPropagation()}
                                                                    >
                                                                        <span className="sr-only">Open menu</span>
                                                                        <MoreVertical className="h-4 w-4" />
                                                                    </Button>
                                                                </DropdownMenuTrigger>
                                                                <DropdownMenuContent
                                                                align="end"
                                                                onCloseAutoFocus={(event) => {
                                                                    event.preventDefault();
                                                                }}
                                                                onEscapeKeyDown={(event) => {
                                                                    event.preventDefault();
                                                                    setOpenDropdownId(null);
                                                                }}
                                                            >
                                                                {/* Add Download button first */}
                                                                
                                                                {/* Keep existing Delete button with its conditional rendering */}
                                                                {protectedCollectionIds.includes(collection.id) ? (
                                                                    <TooltipProvider delayDuration={0}>
                                                                        <Tooltip defaultOpen>
                                                                            <TooltipTrigger asChild>
                                                                                <DropdownMenuItem
                                                                                    className="text-gray-400 cursor-not-allowed hover:!bg-blue-50 hover:!text-gray-400 data-[highlighted]:!bg-blue-50 data-[highlighted]:!text-gray-400"
                                                                                    onSelect={(e) => e.preventDefault()}
                                                                                    onPointerLeave={(e) => e.preventDefault()}
                                                                                >
                                                                                    {t("Delete")}
                                                                                </DropdownMenuItem>
                                                                            </TooltipTrigger>
                                                                            <TooltipContent
                                                                                side="right"
                                                                                sideOffset={5}
                                                                            >
                                                                                <p>{t("This collection is protected and cannot be removed")}</p>
                                                                            </TooltipContent>
                                                                        </Tooltip>
                                                                    </TooltipProvider>
                                                                ) : (
                                                                    <DropdownMenuItem
                                                                        className="text-red-600 hover:!bg-blue-50 hover:!text-blue-600 data-[highlighted]:!bg-blue-50 data-[highlighted]:!text-blue-600"
                                                                        onSelect={() => handleMenuSelect(id, { id, name, status, created_at, collections, document_type, keywords, source_file })}
                                                                    >
                                                                        {t("Delete")}
                                                                    </DropdownMenuItem>
                                                                )}
                                                            </DropdownMenuContent>
                                                                
                                                            </DropdownMenu>
                                                        </TableCell>
                                                    </TableRow>
                                                ))
                                            ) : (
                                                <TableRow>
                                                    <TableCell
                                                        colSpan={4}
                                                        className="text-center py-4 text-sm text-gray-500"
                                                    >
                                                        {t("No documents in this collection")}
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                        </TableBody>
                                    </Table>
                                </CollapsibleContent>
                            </Collapsible>
                        </div>
                    );
                })}
            </div>
        );
    }, [knowledgeBase?.documents, openCollections, openDropdownId, filteredCollections, onDocumentSelect, protectedCollectionIds, handleMenuSelect, handleDeleteCollection, documentsByCollection]);

    return (
        <div className="flex-1">
            <div className="flex justify-between items-center mb-4">
                <div className="flex items-center space-x-4">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowNewCollectionDialog(true)}
                        disabled={isPending}
                        className="flex items-center gap-2"
                    >
                        <Plus className="h-4 w-4" />
                        <span>{t("New Collection")}</span>
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={refreshData}
                        disabled={isPending}
                        className="flex items-center gap-2"
                    >
                        <RefreshCw className={`h-4 w-4 ${isPending ? "animate-spin" : ""}`} />
                        <span>{t("Refresh")}</span>
                    </Button>
                </div>
                <div className="flex items-center space-x-2">
                    <Label
                        htmlFor="graph-view"
                        className="flex items-center gap-1"
                    >
                        <GitBranch className="h-4 w-4" />
                        {t("Graph View")}
                    </Label>
                    <Switch
                        id="graph-view"
                        checked={isGraphView}
                        onCheckedChange={setIsGraphView}
                    />
                </div>
            </div>

            {isPending ? (
                <LoadingSkeleton />
            ) : isGraphView ? (
                <>
                    {/* Pass wasmObjectRef to GraphProcessingComponent */}
                    <GraphProcessingComponent
                        onProcessingComplete={handleProcessingComplete}
                        wasmRef={wasmObjectRef}
                    />
                    {/* Pass the same wasmObjectRef to GraphView */}
                    <GraphView objectRef={wasmObjectRef} />
                </>
            ) : (
                collectionTables
            )}

            {/* Dialog components remain unchanged */}
            <Dialog
                open={!!documentToDelete}
                onOpenChange={(open) => !open && setDocumentToDelete(null)}
            >
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>{t("Delete Document")}</DialogTitle>
                        <DialogDescription>
                            {t("Are you sure you want to delete")} <span className="font-medium">{documentToDelete?.name}</span>?{t("This action cannot be undone.")}
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setDocumentToDelete(null)}
                            disabled={deleteMutation.isPending}
                        >
                            {t("Cancel")}
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={async () => {
                                try {
                                    await confirmDelete();
                                } catch (error) {
                                    captureError(error, {
                                        action: "confirmDelete",
                                        component: "Knowledge",
                                    });
                                }
                            }}
                            disabled={deleteMutation.isPending}
                        >
                            {deleteMutation.isPending ? t("Deleting...") : t("Delete")}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <Dialog
                open={!!collectionToDelete}
                onOpenChange={(open) => {
                    if (!open) {
                        setCollectionToDelete(null);
                    }
                }}
            >
                <DialogContent
                    onEscapeKeyDown={(event) => {
                        event.preventDefault();
                        setCollectionToDelete(null);
                    }}
                    onPointerDownOutside={(event) => {
                        event.preventDefault();
                    }}
                >
                    <DialogHeader>
                        <DialogTitle>{t("Delete Collection")}</DialogTitle>
                        <DialogDescription>
                            {t("Are you sure you want to delete collection")} `{collectionToDelete?.name}`?
                            {t("This action cannot be undone.")}
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setCollectionToDelete(null)}
                            disabled={deleteCollectionMutation.isPending}
                        >
                            {t("Cancel")}
                        </Button>
                        <Button
                            variant="destructive"
                            onClick={async () => {
                                try {
                                    await confirmDeleteCollection();
                                } catch (error) {
                                    captureError(error, {
                                        action: "confirmDeleteCollection",
                                        component: "Knowledge",
                                    });
                                }
                            }}
                            disabled={deleteCollectionMutation.isPending}
                        >
                            {deleteCollectionMutation.isPending ? t("Deleting...") : t("Delete")}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <Dialog
                open={showNewCollectionDialog}
                onOpenChange={setShowNewCollectionDialog}
            >
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>{t("Create New Collection")}</DialogTitle>
                        <DialogDescription>{t("Enter a name for your new collection")}</DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                        <div className="flex flex-col gap-2">
                            <div className="flex items-center gap-2">
                                <span className="text-sm text-muted-foreground whitespace-nowrap">{userData?.assigned_tenant?.tenant_description}_</span>
                                <div className="flex-1">
                                    <Input
                                        placeholder={t("Collection name")}
                                        value={newCollectionName}
                                        onChange={(e) => {
                                            const value = e.target.value;
                                            if (isValidCollectionName(value)) {
                                                setNewCollectionName(value);
                                            }
                                        }}
                                    />
                                </div>
                            </div>
                            <p className="text-sm text-muted-foreground">{t("Only lowercase letters and numbers are allowed")}</p>
                            {hasInvalidCharacters && <p className="text-sm text-destructive">{t("Collection name can only contain lowercase letters and numbers")}</p>}
                        </div>
                    </div>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setShowNewCollectionDialog(false)}
                            disabled={createCollectionMutation.isPending}
                        >
                            {t("Cancel")}
                        </Button>
                        <Button
                            onClick={handleCreateCollection}
                            disabled={!newCollectionName.trim() || hasInvalidCharacters || createCollectionMutation.isPending}
                        >
                            {createCollectionMutation.isPending ? t("Creating...") : t("Create")}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}
