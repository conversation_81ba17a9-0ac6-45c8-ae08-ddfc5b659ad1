import React, { useEffect, useRef, useState } from 'react';
import cookies from "js-cookie";
import { ApiRequest } from "../../../../fetch/FetchRequest";
import { CookieName } from "@/enums/CookieName";
import { captureError } from "@/utils/errorHandling";
import AddNodeComponent from '../AddNodeComponent';

// Define interfaces for the graph data
interface NodeData {
  name?: string;
  entity_id?: string;
  data?: Record<string, unknown>;
  [key: string]: unknown;
}

interface GraphNode {
  id: string;
  name: string;
  type: string;
  node_data: NodeData;
}

interface GraphEdge {
  source: string;
  target: string;
  type: string;
  id?: string; // Optional ID for the edge
}

interface GraphResponse {
  nodes: GraphNode[];
  edges: GraphEdge[];
}

// Interface for node details from the get_node endpoint
interface NodeDetails {
  id: string;
  node_data?: {
    data?: Record<string, unknown>;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

// Interface for enhanced node data
interface EnhancedNodeData {
  enhanced_data: string;
  sources: string;
  status: string;
}

// Interface for new edge creation
interface NewEdgeData {
  source_id: string;
  target_id: string;
  edge_type: string;
}

// Extend Window interface to include wasm_app
declare global {
  interface Window {
    wasm_app?: {
      send_info_message: (message: string) => void;
      new_graph_data: (data: string) => void;
      get_selected_entities: () => string[];
      get_root_entity: () => string | undefined;
      add_edge: (source: string, target: string, type: string) => void;
      highlight_node: (nodeId: string, highlight: boolean, color?: string) => void;
      set_connection_mode: (active: boolean) => void;
    };
  }
}

export default function GraphView() {
    // Explicitly type the ref as HTMLObjectElement
    const objectRef = useRef<HTMLObjectElement>(null);
    
    // State for graph data
    const [graphData, setGraphData] = useState<GraphResponse | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    
    // State for selected entities
    const [selectedEntities, setSelectedEntities] = useState<string[]>([]);
    const [rootEntity, setRootEntity] = useState<string | undefined>(undefined);
    
    // State for node details
    const [selectedNodeDetails, setSelectedNodeDetails] = useState<Record<string, NodeDetails>>({});
    const [fetchingNodeDetails, setFetchingNodeDetails] = useState<boolean>(false);
    const [nodeDetailsError, setNodeDetailsError] = useState<string | null>(null);
    const [expandedNode, setExpandedNode] = useState<string | null>(null);
    
    // State for editing metadata
    const [editingNode, setEditingNode] = useState<string | null>(null);
    const [editFormData, setEditFormData] = useState<Record<string, string>>({});
    const [editFormError, setEditFormError] = useState<string | null>(null);
    const [isSaving, setIsSaving] = useState<boolean>(false);
    
    // State for node enhancement
    const [enhancedNodeData, setEnhancedNodeData] = useState<Record<string, EnhancedNodeData>>({});
    const [enhancingNode, setEnhancingNode] = useState<string | null>(null);
    const [enhancementError, setEnhancementError] = useState<Record<string, string | null>>({});
    
    // State for creating connections between nodes
    const [connectMode, setConnectMode] = useState<boolean>(false);
    const [sourceNode, setSourceNode] = useState<string | null>(null);
    const [targetNode, setTargetNode] = useState<string | null>(null);
    const [edgeType, setEdgeType] = useState<string>("related_to");
    // Define available edge types without the setter since we're not modifying it
    const [availableEdgeTypes] = useState<string[]>([
        "related_to", "depends_on", "contains", "references", "created_by", "is_a"
    ]);
    const [creatingEdge, setCreatingEdge] = useState<boolean>(false);
    const [connectionError, setConnectionError] = useState<string | null>(null);
    const [customEdgeType, setCustomEdgeType] = useState<string>("");
    const [showCustomEdgeInput, setShowCustomEdgeInput] = useState<boolean>(false);
    const [connectionStep, setConnectionStep] = useState<'source' | 'target' | 'type'>('source');
    const [showTypeSelector, setShowTypeSelector] = useState<boolean>(false);

    const [newNodeAdded, setNewNodeAdded] = useState<boolean>(false);
    const [newNodeInfo, setNewNodeInfo] = useState<NodeData | null>(null);
    
    // Load graph data on component mount
    useEffect(() => {
        const loadGraphData = async () => {
            try {
                setLoading(true);
                setError(null);
                
                const bearer = cookies.get(CookieName.API_TOKEN);
                const queryParams = new URLSearchParams();
                
                // Define node types to include
                const nodeTypes = ["artifact", "table"];
                
                // Add node_types as query parameters
                nodeTypes.forEach(type => {
                    queryParams.append('node_types', type);
                });
                
                // Add other parameters
                queryParams.append('base_relation', 'all');
                queryParams.append('metadata_only', 'false');
                
                console.log('Fetching graph data:', {
                    endpoint: `graph_query/get_graph_view`,
                    baseRelation: 'all',
                    metadataOnly: false
                });
                
                // // Use the same ApiRequest pattern as the working code
                // const { data, response } = await ApiRequest<GraphResponse>("graph_query/get_graph_view", { 
                //     bearer, 
                //     init: { method: "GET" },
                //     params: queryParams
                // });
                
                // console.log('API Response:', {
                //     status: response?.status,
                //     statusText: response?.statusText,
                //     ok: response?.ok,
                //     dataReceived: !!data,
                //     data: data
                // });
                const response = await fetch('http://127.0.0.1:8000/api/v1/graph_query/get_graph_view?node_types=artifact&node_types=object&node_types=file&node_types=table&node_types=instance&base_relation=all&metadata_only=false', {
                    method: "GET",
                    headers: {
                        "Authorization": `Bearer ${bearer}`,
                        "Accept": "application/json"
                    }
                });
                
                const data = await response.json();
                console.log("Raw Fetch Response:", data);
                
                if (objectRef.current?.contentWindow?.wasm_app) {
                    objectRef.current.contentWindow.wasm_app.new_graph_data(JSON.stringify(data));
                }
            } catch (err) {
                console.error('Failed to load graph data:', err);
                captureError(err, {
                    action: 'loadGraphData',
                    component: 'GraphView'
                });
                setError(err instanceof Error ? err.message : 'Unknown error occurred');
            } finally {
                setLoading(false);
            }
        };
        
        loadGraphData();
    }, []);
    
    // Function to send data to WASM app when it becomes available
    useEffect(() => {
        // Check if we have graph data and need to send it to WASM
        if (graphData && objectRef.current) {
            const checkAndSendData = () => {
                if (objectRef.current?.contentWindow?.wasm_app) {
                    try {
                        console.log('Sending graph data to WASM app:', {
                            appFound: true,
                            dataSize: JSON.stringify(graphData).length,
                            nodes: graphData.nodes?.length || 0,
                            edges: graphData.edges?.length || 0
                        });
                        
                        // Send the complete graph data to the WASM app
                        objectRef.current.contentWindow.wasm_app.new_graph_data(
                            JSON.stringify(graphData)
                        );
                        
                        console.log('Graph data successfully sent to WASM app');
                        return true;
                    } catch (error) {
                        console.error('Error sending graph data to WASM app:', error);
                        return false;
                    }
                }
                console.log('WASM app not ready yet, will retry...');
                return false;
            };
            
            // Try immediately
            if (!checkAndSendData()) {
                // If not ready, set up an interval to try again
                const interval = setInterval(() => {
                    if (checkAndSendData()) {
                        clearInterval(interval);
                    }
                }, 500);
                
                // Clean up interval
                return () => clearInterval(interval);
            }
        }
    }, [graphData]);

    const handleNodeAdded = (nodeData: NodeData) => {
        console.log('New node added:', nodeData);
        setNewNodeAdded(true);
        setNewNodeInfo(nodeData);
        
        // Show info message in the WASM app if it's available
        if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
            objectRef.current.contentWindow.wasm_app.send_info_message(
                `New node created: ${nodeData.name} (${nodeData.type})`
            );
        }
        
        // In a real implementation, you'd add the node to the graph data
        // and then refresh the visualization
        setTimeout(() => {
            setNewNodeAdded(false);
        }, 5000);
    };

    // Function to fetch node details from the get_node endpoint
    const fetchNodeDetails = async (nodeId: string) => {
        try {
            setFetchingNodeDetails(true);
            setNodeDetailsError(null);
            
            const bearer = cookies.get(CookieName.API_TOKEN);
            const queryParams = new URLSearchParams();
            queryParams.append('node_id', nodeId);
            
            console.log(`Fetching details for node: ${nodeId}`);
            
            const { data, response } = await ApiRequest<NodeDetails>("graph_query/get_node", {
                bearer,
                init: { method: "GET" },
                params: queryParams
            });
            
            if (response?.ok && data) {
                console.log(`Node details received for ${nodeId}:`, data);
                
                // Update the selectedNodeDetails state with the new data
                setSelectedNodeDetails(prev => ({
                    ...prev,
                    [nodeId]: data
                }));
                
                return data;
            } else {
                throw new Error(`Failed to fetch node details: ${response?.statusText || 'Unknown error'}`);
            }
        } catch (err) {
            console.error(`Error fetching node details for ${nodeId}:`, err);
            captureError(err, {
                action: 'fetchNodeDetails',
                component: 'GraphView',
                nodeId
            });
            setNodeDetailsError(err instanceof Error ? err.message : 'Unknown error occurred');
            return null;
        } finally {
            setFetchingNodeDetails(false);
        }
    };

    // Function to enhance a node using the enhance_node endpoint
    const enhanceNode = async (nodeId: string) => {
        try {
            setEnhancingNode(nodeId);
            setEnhancementError(prev => ({ ...prev, [nodeId]: null }));
            
            const bearer = cookies.get(CookieName.API_TOKEN);
            const queryParams = new URLSearchParams();
            queryParams.append('node_id', nodeId);
            
            console.log(`Enhancing node: ${nodeId}`);
            
            const { data, response } = await ApiRequest<EnhancedNodeData>("graph_query/enhance_node", {
                bearer,
                init: { method: "GET" },
                params: queryParams
            });
            
            if (response?.ok && data) {
                console.log(`Node enhancement received for ${nodeId}:`, data);
                
                // Update the enhancedNodeData state with the new data
                setEnhancedNodeData(prev => ({
                    ...prev,
                    [nodeId]: data
                }));
                
                return data;
            } else {
                throw new Error(`Failed to enhance node: ${response?.statusText || 'Unknown error'}`);
            }
        } catch (err) {
            console.error(`Error enhancing node ${nodeId}:`, err);
            captureError(err, {
                action: 'enhanceNode',
                component: 'GraphView',
                nodeId
            });
            setEnhancementError(prev => ({ 
                ...prev, 
                [nodeId]: err instanceof Error ? err.message : 'Unknown error occurred'
            }));
            return null;
        } finally {
            setEnhancingNode(null);
        }
    };

    // Function to create a new edge between nodes
    const createEdge = async () => {
        if (!sourceNode || !targetNode) {
            setConnectionError("Both source and target nodes must be selected");
            return;
        }
        
        // Use custom edge type if selected, otherwise use the selected predefined type
        const finalEdgeType = showCustomEdgeInput ? customEdgeType : edgeType;
        
        if (!finalEdgeType) {
            setConnectionError("Edge type must be specified");
            return;
        }
        
        try {
            setCreatingEdge(true);
            setConnectionError(null);
            
            const bearer = cookies.get(CookieName.API_TOKEN);
            
            // Prepare data for creating an edge
            const newEdgeData: NewEdgeData = {
                source_id: sourceNode,
                target_id: targetNode,
                edge_type: finalEdgeType
            };
            
            console.log('Creating new edge:', newEdgeData);
            
            // Send info message to the WASM app
            if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
                objectRef.current.contentWindow.wasm_app.send_info_message(
                    `Creating connection: ${getNodeDisplayName(sourceNode)} → ${finalEdgeType} → ${getNodeDisplayName(targetNode)}...`
                );
            }
            
            // Make API request to create the edge
            const { data, response } = await ApiRequest<{ edge_id: string }>("graph_query/create_edge", {
                bearer,
                init: { 
                    method: "POST",
                    body: JSON.stringify(newEdgeData)
                }
            });
            
            if (response?.ok && data) {
                console.log('Edge created successfully:', data);
                
                // Create a new edge object
                const newEdge: GraphEdge = {
                    source: sourceNode,
                    target: targetNode,
                    type: finalEdgeType,
                    id: data.edge_id
                };
                
                // Update graph data with the new edge
                if (graphData) {
                    const updatedGraphData = {
                        ...graphData,
                        edges: [...graphData.edges, newEdge]
                    };
                    
                    setGraphData(updatedGraphData);
                    
                    // Update the visualization in the WASM app
                    if (objectRef.current?.contentWindow?.wasm_app?.add_edge) {
                        // If the WASM app supports adding an edge directly
                        objectRef.current.contentWindow.wasm_app.add_edge(sourceNode, targetNode, finalEdgeType);
                    } else if (objectRef.current?.contentWindow?.wasm_app) {
                        // Otherwise, refresh the entire graph
                        objectRef.current.contentWindow.wasm_app.new_graph_data(JSON.stringify(updatedGraphData));
                    }
                    
                    // Clear highlighting from the nodes
                    if (objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
                        objectRef.current.contentWindow.wasm_app.highlight_node(sourceNode, false);
                        objectRef.current.contentWindow.wasm_app.highlight_node(targetNode, false);
                    }
                    
                    // Reset the connection state
                    resetConnectionSelection();
                    setShowTypeSelector(false);
                    setConnectionStep('source');
                    
                    if (showCustomEdgeInput) {
                        setCustomEdgeType("");
                        setShowCustomEdgeInput(false);
                    }
                    
                    // Show success message
                    if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
                        objectRef.current.contentWindow.wasm_app.send_info_message(
                            `Connection created successfully: ${getNodeDisplayName(sourceNode)} → ${finalEdgeType} → ${getNodeDisplayName(targetNode)}`
                        );
                    }
                }
                
                return data;
            } else {
                throw new Error(`Failed to create edge: ${response?.statusText || 'Unknown error'}`);
            }
        } catch (err) {
            console.error('Error creating edge:', err);
            captureError(err, {
                action: 'createEdge',
                component: 'GraphView',
                sourceNode,
                targetNode,
                edgeType: finalEdgeType
            });
            setConnectionError(err instanceof Error ? err.message : 'Failed to create connection');
            
            // Show error message in the WASM app
            if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
                objectRef.current.contentWindow.wasm_app.send_info_message(
                    `Error creating connection: ${err instanceof Error ? err.message : 'Unknown error'}`
                );
            }
            
            return null;
        } finally {
            setCreatingEdge(false);
        }
    };

    // Toggle connect mode
    const toggleConnectMode = () => {
        const newConnectMode = !connectMode;
        setConnectMode(newConnectMode);
        
        // Notify the WASM app about the connection mode change if the method exists
        if (objectRef.current?.contentWindow?.wasm_app?.set_connection_mode) {
            objectRef.current.contentWindow.wasm_app.set_connection_mode(newConnectMode);
        }
        
        if (!newConnectMode) {
            // Clear connection data when exiting connect mode
            resetConnectionSelection();
            
            // Clear any highlighting in the graph
            if (sourceNode && objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
                objectRef.current.contentWindow.wasm_app.highlight_node(sourceNode, false);
            }
            if (targetNode && objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
                objectRef.current.contentWindow.wasm_app.highlight_node(targetNode, false);
            }
        } else {
            // Reset to first step when entering connect mode
            setConnectionStep('source');
            setShowTypeSelector(false);
            
            // Send an info message to the WASM app
            if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
                objectRef.current.contentWindow.wasm_app.send_info_message(
                    "Connection Mode: Click on a node to select it as the source"
                );
            }
        }
    };

    // Helper function to set a node as source or target based on current state
    const selectNodeForConnection = (nodeId: string) => {
        if (!connectMode) return;
        
        if (connectionStep === 'source') {
            setSourceNode(nodeId);
            setConnectionStep('target');
            
            // Highlight the source node in blue
            if (objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
                objectRef.current.contentWindow.wasm_app.highlight_node(nodeId, true, "#3b82f6"); // blue-500
            }
        } else if (connectionStep === 'target') {
            // Don't allow self-connections
            if (nodeId === sourceNode) {
                setConnectionError("Cannot create self-connection. Source and target must be different nodes.");
                
                // Send an error message to the WASM app
                if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
                    objectRef.current.contentWindow.wasm_app.send_info_message(
                        "Error: Cannot connect a node to itself. Please select a different target node."
                    );
                }
                return;
            }
            
            setTargetNode(nodeId);
            setConnectionStep('type');
            setShowTypeSelector(true);
            
            // Highlight the target node in green
            if (objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
                objectRef.current.contentWindow.wasm_app.highlight_node(nodeId, true, "#10b981"); // emerald-500
            }
        }
    };

    // Function to reset connection selection
    const resetConnectionSelection = () => {
        // Clear any highlighting in the graph
        if (sourceNode && objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
            objectRef.current.contentWindow.wasm_app.highlight_node(sourceNode, false);
        }
        if (targetNode && objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
            objectRef.current.contentWindow.wasm_app.highlight_node(targetNode, false);
        }
        
        setSourceNode(null);
        setTargetNode(null);
        setConnectionError(null);
        setConnectionStep('source');
        setShowTypeSelector(false);
        
        // Send an info message to the WASM app
        if (connectMode && objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
            objectRef.current.contentWindow.wasm_app.send_info_message(
                "Selection reset. Select a node to use as the source"
            );
        }
    };

    // Toggle node expansion to show/hide details
    const toggleNodeExpansion = (nodeId: string) => {
        if (expandedNode === nodeId) {
            // If clicking on already expanded node, collapse it
            setExpandedNode(null);
        } else {
            // Expand the clicked node
            setExpandedNode(nodeId);
        }
    };
    
    // Function to start editing a node's metadata
    const startEditing = (nodeId: string) => {
        const details = selectedNodeDetails[nodeId];
        if (!details?.node_data?.data) {
            // Cannot edit if no data is available
            setEditFormError("No metadata available to edit");
            return;
        }

        // Initialize form with current metadata
        const initialFormData: Record<string, string> = {};
        Object.entries(details.node_data.data).forEach(([key, value]) => {
            initialFormData[key] = typeof value === 'string' ? value : JSON.stringify(value);
        });
        
        setEditFormData(initialFormData);
        setEditingNode(nodeId);
        setEditFormError(null);
    };

    // Function to cancel editing
    const cancelEditing = () => {
        setEditingNode(null);
        setEditFormData({});
        setEditFormError(null);
    };

    // Function to handle form field changes
    const handleFormChange = (key: string, value: string) => {
        setEditFormData(prev => ({
            ...prev,
            [key]: value
        }));
    };

    // Function to add a new metadata field
    const addMetadataField = () => {
        setEditFormData(prev => ({
            ...prev,
            [`new_field_${Object.keys(prev).length}`]: ""
        }));
    };

    // Function to remove a metadata field
    const removeMetadataField = (key: string) => {
        setEditFormData(prev => {
            const newData = { ...prev };
            delete newData[key];
            return newData;
        });
    };

    // Function to save changes
    const saveMetadataChanges = async (nodeId: string) => {
        try {
            setIsSaving(true);
            setEditFormError(null);
            
            // Prepare data for submission
            const updatedData = { ...editFormData };
            
            // Try to parse JSON values where applicable
            Object.keys(updatedData).forEach(key => {
                const value = updatedData[key];
                try {
                    // Only parse values that look like objects or arrays
                    if ((value.startsWith('{') && value.endsWith('}')) || 
                        (value.startsWith('[') && value.endsWith(']'))) {
                        updatedData[key] = JSON.parse(value);
                    }
                } catch (e) {
                    // Keep as string if parsing fails
                    console.warn(`Failed to parse JSON for key ${key}:`, e);
                }
            });
            
            const bearer = cookies.get(CookieName.API_TOKEN);
            
            console.log(`Updating metadata for node: ${nodeId}`, updatedData);
            
            // Make API request to update node
            const { response } = await ApiRequest("graph_query/update_node", {
                bearer,
                init: { 
                    method: "POST",
                    body: JSON.stringify({
                        node_id: nodeId,
                        node_data: {
                            data: updatedData
                        }
                    })
                }
            });
            
            if (response?.ok) {
                console.log(`Node metadata updated successfully for ${nodeId}`);
                
                // Refresh node details
                await fetchNodeDetails(nodeId);
                
                // Exit edit mode
                setEditingNode(null);
                setEditFormData({});
            } else {
                throw new Error(`Failed to update node metadata: ${response?.statusText || 'Unknown error'}`);
            }
        } catch (err) {
            console.error(`Error updating node metadata for ${nodeId}:`, err);
            captureError(err, {
                action: 'saveMetadataChanges',
                component: 'GraphView',
                nodeId
            });
            setEditFormError(err instanceof Error ? err.message : 'Failed to save changes');
        } finally {
            setIsSaving(false);
        }
    };

    // Add polling to check for selected entities and handle graph interactions
    useEffect(() => {
        // Only set up polling if we're not loading and have no error
        if (!loading && !error && objectRef.current) {
            // Function to check for selected entities, root entity, and handle selections for connections
            const checkGraphInteractions = async () => {
                if (objectRef.current?.contentWindow?.wasm_app) {
                    try {
                        // Get selected entities
                        const selected = objectRef.current.contentWindow.wasm_app.get_selected_entities();
                        
                        // Filter for valid UUIDs
                        const validSelected = selected.filter(isValidUUID);
                        
                        // Handle connection mode selection
                        if (connectMode) {
                            // Process node selection for connection based on the selected entities
                            if (validSelected.length === 1) {
                                const selectedNode = validSelected[0];
                                
                                // Check if this is a new node selection for our source/target process
                                if (connectionStep === 'source' && selectedNode !== sourceNode) {
                                    // If we're on source step and selected a node that's not already our source
                                    
                                    // Fetch node details if we don't have them
                                    if (!selectedNodeDetails[selectedNode]) {
                                        fetchNodeDetails(selectedNode);
                                    }
                                    
                                    // Process the node selection for connection
                                    selectNodeForConnection(selectedNode);
                                    
                                    // Send info message
                                    if (objectRef.current.contentWindow.wasm_app.send_info_message) {
                                        const displayName = getNodeDisplayName(selectedNode);
                                        objectRef.current.contentWindow.wasm_app.send_info_message(
                                            `Selected "${displayName}" as source. Now select a target node.`
                                        );
                                    }
                                } 
                                else if (connectionStep === 'target' && selectedNode !== sourceNode && selectedNode !== targetNode) {
                                    // If we're on target step and selected a node that's not our source and not already our target
                                    
                                    // Fetch node details if we don't have them
                                    if (!selectedNodeDetails[selectedNode]) {
                                        fetchNodeDetails(selectedNode);
                                    }
                                    
                                    // Process the node selection for connection
                                    selectNodeForConnection(selectedNode);
                                    
                                    // Send info message
                                    if (objectRef.current.contentWindow.wasm_app.send_info_message) {
                                        const displayName = getNodeDisplayName(selectedNode);
                                        objectRef.current.contentWindow.wasm_app.send_info_message(
                                            `Selected "${displayName}" as target. Now choose a connection type.`
                                        );
                                    }
                                }
                            }
                        }
                        
                        // Check if the selection has changed for panel display
                        const hasSelectionChanged = 
                            validSelected.length !== selectedEntities.length || 
                            validSelected.some(id => !selectedEntities.includes(id));
                        
                        if (hasSelectionChanged) {
                            setSelectedEntities(validSelected || []);
                            
                            // Fetch details for all newly selected entities
                            const newEntities = validSelected.filter(id => 
                                !selectedEntities.includes(id) && !selectedNodeDetails[id]
                            );
                            
                            // Fetch details for new entities
                            for (const nodeId of newEntities) {
                                fetchNodeDetails(nodeId);
                            }
                        }
                        
                        // Get root entity
                        const root = objectRef.current.contentWindow.wasm_app.get_root_entity();
                        if (root && root !== rootEntity && isValidUUID(root)) {
                            setRootEntity(root);
                            
                            // Fetch root entity details if it's valid and we don't have it yet
                            if (!selectedNodeDetails[root]) {
                                fetchNodeDetails(root);
                            }
                        }
                    } catch (error) {
                        console.error('Error checking graph interactions:', error);
                    }
                }
            };
            
            // Set up polling interval
            const interval = setInterval(checkGraphInteractions, 200); // Faster polling for better responsiveness
            
            // Initial check
            checkGraphInteractions();
            
            // Clean up interval on unmount
            return () => clearInterval(interval);
        }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [loading, error, selectedEntities, rootEntity, selectedNodeDetails, connectMode, sourceNode, 
        targetNode, connectionStep]);

    // Format node details for display
    const formatNodeDetail = (value: unknown): React.ReactNode => {
        if (value === null || value === undefined) {
            return <span className="text-gray-400">null</span>;
        }
        
        if (typeof value === 'object') {
            return <span>{JSON.stringify(value)}</span>;
        }
        
        return <span>{String(value)}</span>;
    };

    // Helper to get node display name
    const getNodeDisplayName = (nodeId: string): string => {
        const details = selectedNodeDetails[nodeId];
        
        if (details) {
            // Focus on node_data.data property
            const nodeData = details.node_data as Record<string, unknown> | undefined;
            
            // Check if nodeData exists and has a data property
            if (nodeData && 'data' in nodeData) {
                const nestedData = nodeData.data as Record<string, unknown> | undefined;
                
                // Try to get the name from nested data
                if (nestedData && typeof nestedData.name === 'string' && nestedData.name) {
                    return nestedData.name as string;
                }
                
                // Try to get label from nested data
                if (nestedData && typeof nestedData.label === 'string' && nestedData.label) {
                    return nestedData.label as string;
                }
                
                // Try to get Bezeichnung from nested data
                if (nestedData && typeof nestedData.Bezeichnung === 'string' && nestedData.Bezeichnung) {
                    return nestedData.Bezeichnung as string;
                }
            }
        }
        
        // Fallback to displaying a shortened ID
        return `${nodeId.slice(0, 8)}...`;
    };

    // Helper function to check if an ID is a valid UUID
    const isValidUUID = (id: string | undefined): boolean => {
        // UUID format: 8-4-4-4-12 hexadecimal digits
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        return typeof id === 'string' && uuidRegex.test(id);
    };

    // Render enhanced node data section
    const renderEnhancedNodeData = (nodeId: string) => {
        const enhancedData = enhancedNodeData[nodeId];
        const isEnhancing = enhancingNode === nodeId;
        const error = enhancementError[nodeId];
        
        if (isEnhancing) {
            return (
                <div className="mt-4 p-3 bg-blue-50 border border-blue-100 rounded">
                    <div className="flex items-center justify-center text-blue-700">
                        <div className="animate-spin rounded-full h-5 w-5 border-2 border-t-transparent border-blue-500 mr-2"></div>
                        <span>Enhancing node with AI...</span>
                    </div>
                </div>
            );
        }
        
        if (error) {
            return (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                    <div className="flex justify-between items-center mb-2">
                        <h4 className="font-medium text-red-700">Enhancement Failed</h4>
                        <button 
                            className="text-blue-500 hover:bg-blue-50 px-2 py-1 rounded text-xs flex items-center"
                            onClick={() => enhanceNode(nodeId)}
                        >
                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Retry
                        </button>
                    </div>
                    <p className="text-red-600 text-sm">{error}</p>
                </div>
            );
        }
        
        if (enhancedData) {
            return (
                <div className="mt-4 p-3 bg-indigo-50 border border-indigo-100 rounded">
                    <div className="flex justify-between items-center mb-2">
                        <h4 className="font-medium text-indigo-700">AI Enhanced Information</h4>
                        <button 
                            className="text-blue-500 hover:bg-blue-50 px-2 py-1 rounded text-xs flex items-center"
                            onClick={() => enhanceNode(nodeId)}
                        >
                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Refresh
                        </button>
                    </div>
                    <div className="bg-white rounded border border-indigo-100 p-3 mb-2">
                        <p className="text-gray-800 whitespace-pre-wrap">{enhancedData.enhanced_data}</p>
                    </div>
                    {enhancedData.sources && (
                        <div>
                            <h5 className="text-xs font-medium text-indigo-700 mb-1">Sources</h5>
                            <div className="bg-white rounded border border-indigo-100 p-2">
                                <p className="text-gray-600 text-xs">{enhancedData.sources}</p>
                            </div>
                        </div>
                    )}
                </div>
            );
        }
        
        return (
            <div className="mt-4">
                <button 
                    className="w-full py-2 text-center text-indigo-500 hover:bg-indigo-50 border border-indigo-200 rounded flex items-center justify-center"
                    onClick={() => enhanceNode(nodeId)}
                >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Enhance with AI
                </button>
            </div>
        );
    };

    // Render connection mode panel and overlay
    const renderConnectionPanel = () => {
        if (!connectMode) return null;
        
        // Show a different panel based on the connection step
        if (showTypeSelector && sourceNode && targetNode) {
            // Type selection panel - shown after both nodes are selected
            return (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20">
                    <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full m-4">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-medium text-gray-900">Create Connection</h3>
                            <button 
                                className="text-gray-400 hover:text-gray-500"
                                onClick={() => {
                                    setShowTypeSelector(false);
                                    resetConnectionSelection();
                                }}
                            >
                                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        
                        <div className="mb-6">
                            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg mb-2">
                                <div className="flex items-center">
                                    <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center mr-3">
                                        1
                                    </div>
                                    <span className="font-medium">Source:</span>
                                </div>
                                <span>{getNodeDisplayName(sourceNode)}</span>
                            </div>
                            
                            <div className="flex justify-center my-2">
                                <svg className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                </svg>
                            </div>
                            
                            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                                <div className="flex items-center">
                                    <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center mr-3">
                                        2
                                    </div>
                                    <span className="font-medium">Target:</span>
                                </div>
                                <span>{getNodeDisplayName(targetNode)}</span>
                            </div>
                        </div>
                        
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-2">Connection Type</label>
                            <div className="mb-4">
                                <select
                                    value={showCustomEdgeInput ? "custom" : edgeType}
                                    onChange={(e) => {
                                        if (e.target.value === "custom") {
                                            setShowCustomEdgeInput(true);
                                        } else {
                                            setShowCustomEdgeInput(false);
                                            setEdgeType(e.target.value);
                                        }
                                    }}
                                    className="w-full p-3 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                    disabled={creatingEdge}
                                >
                                    {availableEdgeTypes.map(type => (
                                        <option key={type} value={type}>{type}</option>
                                    ))}
                                    <option value="custom">Custom Type...</option>
                                </select>
                            </div>
                            
                            {showCustomEdgeInput && (
                                <input
                                    type="text"
                                    value={customEdgeType}
                                    onChange={(e) => setCustomEdgeType(e.target.value)}
                                    placeholder="Enter custom connection type..."
                                    className="w-full p-3 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                    disabled={creatingEdge}
                                    autoFocus
                                />
                            )}
                        </div>
                        
                        {connectionError && (
                            <div className="mb-4 p-3 bg-red-50 text-red-600 text-sm rounded-md border border-red-200">
                                {connectionError}
                            </div>
                        )}
                        
                        <div className="flex justify-between">
                            <button
                                className="px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                onClick={() => {
                                    resetConnectionSelection();
                                    setShowTypeSelector(false);
                                }}
                                disabled={creatingEdge}
                            >
                                Cancel
                            </button>
                            
                            <button
                                className={`px-4 py-2 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 flex items-center ${
                                    sourceNode && targetNode && (showCustomEdgeInput ? customEdgeType : edgeType)
                                        ? 'bg-indigo-600 hover:bg-indigo-700'
                                        : 'bg-gray-400 cursor-not-allowed'
                                }`}
                                onClick={createEdge}
                                disabled={!sourceNode || !targetNode || creatingEdge || (showCustomEdgeInput ? !customEdgeType : !edgeType)}
                            >
                                {creatingEdge ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-t-transparent border-white mr-2"></div>
                                        Creating...
                                    </>
                                ) : (
                                    <>
                                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                        </svg>
                                        Create Connection
                                    </>
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            );
        }
        
        // Display status panel during the selection process
        return (
            <div className="absolute top-4 right-4 z-10">
                <div className="bg-white rounded-lg shadow-lg p-4 border border-indigo-200 w-72">
                    <div className="flex justify-between items-center mb-3">
                        <h3 className="font-medium text-indigo-700">Connection Mode</h3>
                        <button 
                            className="text-gray-400 hover:text-gray-500 focus:outline-none"
                            onClick={toggleConnectMode}
                        >
                            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    
                    <div className="space-y-3">
                        <div className={`flex items-center p-2 rounded-md ${
                            connectionStep === 'source' ? 'bg-indigo-50 border border-indigo-100' : 
                            sourceNode ? 'bg-green-50 border border-green-100' : 'bg-gray-50 border border-gray-200'
                        }`}>
                            <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                                connectionStep === 'source' ? 'bg-indigo-500 text-white' : 
                                sourceNode ? 'bg-green-500 text-white' : 'bg-gray-300 text-white'
                            }`}>
                                1
                            </div>
                            <div className="flex-grow">
                                <span className="text-sm font-medium">
                                    {connectionStep === 'source' ? 'Select source node' : 
                                     sourceNode ? `Source: ${getNodeDisplayName(sourceNode)}` : 'Source node'}
                                </span>
                            </div>
                            {sourceNode && connectionStep !== 'source' && (
                                <button 
                                    className="text-sm text-indigo-600 hover:text-indigo-500"
                                    onClick={() => {
                                        // Clear source selection and reset to step 1
                                        if (sourceNode && objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
                                            objectRef.current.contentWindow.wasm_app.highlight_node(sourceNode, false);
                                        }
                                        setSourceNode(null);
                                        setConnectionStep('source');
                                        
                                        // Send info message
                                        if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
                                            objectRef.current.contentWindow.wasm_app.send_info_message(
                                                "Source node cleared. Select a new source node."
                                            );
                                        }
                                    }}
                                >
                                    Change
                                </button>
                            )}
                        </div>
                        
                        <div className={`flex items-center p-2 rounded-md ${
                            connectionStep === 'target' ? 'bg-indigo-50 border border-indigo-100' : 
                            targetNode ? 'bg-green-50 border border-green-100' : 'bg-gray-50 border border-gray-200'
                        }`}>
                            <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                                connectionStep === 'target' ? 'bg-indigo-500 text-white' : 
                                targetNode ? 'bg-green-500 text-white' : 'bg-gray-300 text-white'
                            }`}>
                                2
                            </div>
                            <div className="flex-grow">
                                <span className="text-sm font-medium">
                                    {connectionStep === 'target' ? 'Select target node' : 
                                     targetNode ? `Target: ${getNodeDisplayName(targetNode)}` : 'Target node'}
                                </span>
                            </div>
                            {targetNode && connectionStep !== 'target' && (
                                <button 
                                    className="text-sm text-indigo-600 hover:text-indigo-500"
                                    onClick={() => {
                                        // Clear target selection and go back to step 2
                                        if (targetNode && objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
                                            objectRef.current.contentWindow.wasm_app.highlight_node(targetNode, false);
                                        }
                                        setTargetNode(null);
                                        setConnectionStep('target');
                                        
                                        // Send info message
                                        if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
                                            objectRef.current.contentWindow.wasm_app.send_info_message(
                                                "Target node cleared. Click on a node to select a target."
                                            );
                                        }
                                    }}
                                >
                                    Change
                                </button>
                            )}
                        </div>
                        
                        <div className={`flex items-center p-2 rounded-md ${
                            connectionStep === 'type' ? 'bg-indigo-50 border border-indigo-100' : 'bg-gray-50 border border-gray-200'
                        }`}>
                            <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                                connectionStep === 'type' ? 'bg-indigo-500 text-white' : 'bg-gray-300 text-white'
                            }`}>
                                3
                            </div>
                            <span className="text-sm font-medium">Choose connection type</span>
                        </div>
                    </div>
                    
                    {connectionError && (
                        <div className="mt-3 p-2 bg-red-50 text-red-600 text-sm rounded border border-red-200">
                            {connectionError}
                        </div>
                    )}
                    
                    <div className="mt-4 flex justify-between">
                        <button
                            className="px-3 py-1.5 bg-white text-gray-600 border border-gray-300 rounded text-sm hover:bg-gray-50"
                            onClick={resetConnectionSelection}
                        >
                            Reset
                        </button>
                        
                        {sourceNode && targetNode && (
                            <button
                                className="px-3 py-1.5 bg-indigo-600 text-white rounded text-sm hover:bg-indigo-700"
                                onClick={() => setShowTypeSelector(true)}
                            >
                                Continue
                            </button>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    // Render a node in the selected entities panel
    const renderNodeItem = (nodeId: string, index: number) => {
        const details = selectedNodeDetails[nodeId];
        const isExpanded = expandedNode === nodeId;
        const isEditing = editingNode === nodeId;
        const isSourceNode = sourceNode === nodeId;
        const isTargetNode = targetNode === nodeId;
        
        // Add connect mode styling
        const nodeItemClasses = `mb-3 border ${
            isSourceNode ? 'border-blue-400 ring-2 ring-blue-300' : 
            isTargetNode ? 'border-green-400 ring-2 ring-green-300' : 
            'border-gray-200'
        } rounded-md overflow-hidden`;
        
        // Add connect mode action
        const handleNodeClick = () => {
            if (connectMode) {
                selectNodeForConnection(nodeId);
            } else {
                toggleNodeExpansion(nodeId);
            }
        };
        
        return (
            <li key={index} className={nodeItemClasses}>
                <div 
                    className={`flex items-center justify-between cursor-pointer hover:bg-gray-50 p-2 ${
                        connectMode ? 'bg-gray-50' : 'bg-gray-50'
                    }`}
                    onClick={handleNodeClick}
                >
                    <div className="flex-grow truncate">
                        <span className="text-sm font-medium">{getNodeDisplayName(nodeId)}</span>
                        {connectMode && (isSourceNode || isTargetNode) && (
                            <span className={`ml-2 px-1 py-0.5 text-xs rounded ${
                                isSourceNode ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                            }`}>
                                {isSourceNode ? 'Source' : 'Target'}
                            </span>
                        )}
                    </div>
                    <div className="flex items-center">
                        {fetchingNodeDetails && selectedNodeDetails[nodeId] === undefined && (
                            <div className="animate-spin rounded-full h-4 w-4 border border-t-transparent border-blue-500 mr-2"></div>
                        )}
                        
                        {!connectMode && (
                            <button 
                                className="text-blue-500 hover:text-blue-700 focus:outline-none w-5 h-5 flex items-center justify-center"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    toggleNodeExpansion(nodeId);
                                }}
                            >
                                {isExpanded ? '▼' : '▶'}
                            </button>
                        )}
                    </div>
                </div>
                
                {isExpanded && !connectMode && (
                    <div className="p-3 text-sm border-t border-gray-200">
                        {fetchingNodeDetails && (
                            <div className="flex items-center justify-center p-4 text-gray-500">
                                <div className="animate-spin rounded-full h-5 w-5 border-2 border-t-transparent border-blue-500 mr-2"></div>
                                <span>Loading details...</span>
                            </div>
                        )}
                        
                        {nodeDetailsError && (
                            <div className="text-red-500 p-3 bg-red-50 rounded mb-2">
                                <p className="font-semibold">Error loading node details:</p>
                                <p className="mb-2">{nodeDetailsError}</p>
                                <button 
                                    className="text-blue-500 hover:underline flex items-center"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        fetchNodeDetails(nodeId);
                                    }}
                                >
                                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Retry
                                </button>
                            </div>
                        )}
                        
                        {details && !fetchingNodeDetails && !nodeDetailsError && details.node_data?.data && !isEditing && (
                            <>
                                <div className="flex justify-between items-center mb-2">
                                    <h4 className="font-medium text-gray-700">Metadata</h4>
                                    <button 
                                        className="text-blue-500 hover:bg-blue-50 px-2 py-1 rounded text-xs flex items-center"
                                        onClick={() => startEditing(nodeId)}
                                    >
                                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                        </svg>
                                        Edit Metadata
                                    </button>
                                </div>
                                <div className="bg-white rounded border border-gray-100">
                                    <table className="w-full">
                                        <tbody>
                                            {Object.entries(details.node_data.data).map(([key, value]) => (
                                                <tr key={key} className="border-b border-gray-100 last:border-b-0">
                                                    <td className="py-2 px-3 font-medium text-gray-700 whitespace-nowrap">{key}</td>
                                                    <td className="py-2 px-3 text-gray-900">{formatNodeDetail(value)}</td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                                
                                {/* Add the enhance node section */}
                                {renderEnhancedNodeData(nodeId)}
                            </>
                        )}
                        
                        {details && !fetchingNodeDetails && !nodeDetailsError && details.node_data?.data && isEditing && (
                            <div className="bg-white rounded border border-gray-200 p-3">
                                <div className="flex justify-between items-center mb-3">
                                    <h4 className="font-medium text-gray-700">Edit Metadata</h4>
                                    <div className="flex space-x-2">
                                        <button 
                                            className="text-blue-500 hover:bg-blue-50 px-2 py-1 rounded text-xs flex items-center"
                                            onClick={addMetadataField}
                                        >
                                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                            Add Field
                                        </button>
                                    </div>
                                </div>
                                
                                {editFormError && (
                                    <div className="text-red-500 p-2 bg-red-50 rounded mb-3 text-xs">
                                        {editFormError}
                                    </div>
                                )}
                                
                                <div className="max-h-64 overflow-y-auto border border-gray-100 rounded mb-3">
                                    <table className="w-full">
                                        <tbody>
                                            {Object.entries(editFormData).map(([key, value]) => (
                                                <tr key={key} className="border-b border-gray-100 last:border-b-0">
                                                    <td className="py-2 px-2 text-gray-700 w-1/3">
                                                        <input
                                                            type="text"
                                                            value={key}
                                                            onChange={(e) => {
                                                                const newKey = e.target.value;
                                                                const newValue = editFormData[key];
                                                                removeMetadataField(key);
                                                                handleFormChange(newKey, newValue);
                                                            }}
                                                            className="w-full p-1 text-xs border border-gray-200 rounded"
                                                        />
                                                    </td>
                                                    <td className="py-2 px-2 text-gray-900">
                                                        <div className="flex items-center">
                                                            <textarea
                                                                value={value}
                                                                onChange={(e) => handleFormChange(key, e.target.value)}
                                                                className="w-full p-1 text-xs border border-gray-200 rounded resize-y"
                                                                rows={value.includes('\n') ? 3 : 1}
                                                            />
                                                            <button 
                                                                className="ml-1 text-red-400 hover:text-red-600"
                                                                onClick={() => removeMetadataField(key)}
                                                            >
                                                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                                </svg>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div className="flex justify-end space-x-2">
                                    <button 
                                        className="px-3 py-1 text-gray-600 hover:bg-gray-100 border border-gray-200 rounded text-xs"
                                        onClick={cancelEditing}
                                        disabled={isSaving}
                                    >
                                        Cancel
                                    </button>
                                    <button 
                                        className="px-3 py-1 text-white bg-blue-500 hover:bg-blue-600 rounded text-xs flex items-center"
                                        onClick={() => saveMetadataChanges(nodeId)}
                                        disabled={isSaving}
                                    >
                                        {isSaving ? (
                                            <>
                                                <div className="animate-spin rounded-full h-3 w-3 border-2 border-t-transparent border-white mr-1"></div>
                                                <span>Saving...</span>
                                            </>
                                        ) : (
                                            <>
                                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                <span>Save Changes</span>
                                            </>
                                        )}
                                    </button>
                                </div>
                            </div>
                        )}
                        
                        {details && !fetchingNodeDetails && !nodeDetailsError && !details.node_data?.data && (
                            <p className="text-gray-500 italic">No node_data.data available.</p>
                        )}
                        
                        {!details && !fetchingNodeDetails && !nodeDetailsError && (
                            <button 
                                className="w-full py-2 text-center text-blue-500 hover:bg-blue-50 border border-blue-200 rounded flex items-center justify-center"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    fetchNodeDetails(nodeId);
                                }}
                            >
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Fetch node details
                            </button>
                        )}
                    </div>
                )}
            </li>
        );
    };
    
    // Check if the WASM path needs to be updated based on your project structure
    // This path should point to where your WebAssembly app is hosted
    const wasmPath = '/wasm/index.html';
    
    // Debug the path
    console.log('Using WebAssembly path:', wasmPath);
    
    return (
        <div className="flex flex-col h-full">
            {/* Action Buttons */}
            <div className="mb-4 flex justify-between">
                <div className="flex space-x-4">
                    {/* Add the new Add Node button */}
                    <AddNodeComponent onNodeAdded={handleNodeAdded} />
                    
                    <button
                        className={`px-4 py-2 rounded flex items-center ${
                            connectMode 
                                ? 'bg-indigo-600 text-white hover:bg-indigo-700' 
                                : 'bg-blue-600 text-white hover:bg-blue-700'
                        }`}
                        onClick={toggleConnectMode}
                    >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                        {connectMode ? 'Exit Connection Mode' : 'Create Connection'}
                    </button>
                </div>
                
                {/* Add Node Success Notification */}
                {newNodeAdded && newNodeInfo && (
                    <div className="px-4 py-2 bg-green-100 text-green-800 rounded-md flex items-center">
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Node created
                    </div>
                )}
            </div>
            
            {/* Graph visualization with relative positioning for overlay elements */}
            <div className="flex-grow w-full bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden relative">
                {/* Connection Panel Overlay (only shown in connect mode) */}
                {renderConnectionPanel()}
                
                {/* Loading spinner */}
                {loading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                )}
                
                {/* Error message */}
                {error && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
                        <div className="text-red-500 p-4 bg-red-100 rounded-md">
                            Error loading graph data: {error}
                        </div>
                    </div>
                )}
                
                {/* Connection mode instructions overlay */}
                {connectMode && !showTypeSelector && (
                    <div className="absolute bottom-4 left-4 right-4 bg-indigo-900 bg-opacity-90 text-white p-3 rounded-lg z-10 text-center">
                        <p className="text-sm">
                            {connectionStep === 'source' && "👆 Select a node in the graph to use as the source"}
                            {connectionStep === 'target' && "👆 Now select a different node to use as the target"}
                            {connectionStep === 'type' && "Almost done! Click &apos;Continue&apos; to choose a connection type"}
                        </p>
                    </div>
                )}
                
                {/* The WebAssembly graph visualization */}
                <object
                    type="text/html"
                    data={wasmPath}
                    className="w-full h-full"
                    id="app"
                    ref={objectRef}
                />
            </div>
            
            {/* Selected Entities Panel with Node Details */}
            <div className="mt-4 p-4 bg-gray-100 rounded-lg shadow-sm border border-gray-200 max-h-96 overflow-auto">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">
                        {connectMode && !showTypeSelector
                            ? (connectionStep === 'source' 
                                ? 'Click on a node in the graph to select source' 
                                : connectionStep === 'target' 
                                    ? 'Click on a node in the graph to select target'
                                    : 'Selected Nodes')
                            : 'Selected Entities'
                        }
                    </h3>
                    {selectedEntities.length > 0 && (
                        <div className="flex space-x-2">
                            <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
                                {selectedEntities.length} total
                            </span>
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                {selectedEntities.filter(isValidUUID).length} valid
                            </span>
                        </div>
                    )}
                </div>
                
                {rootEntity && isValidUUID(rootEntity) && !connectMode && (
                    <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
                        <div className="flex justify-between items-center">
                            <div className="flex-grow">
                                <span className="font-medium">{getNodeDisplayName(rootEntity)}</span>
                            </div>
                            <div className="flex items-center">
                                {expandedNode === rootEntity && selectedNodeDetails[rootEntity]?.node_data?.data && editingNode !== rootEntity && (
                                    <button 
                                        className="text-blue-500 hover:bg-blue-100 px-2 py-1 mr-2 rounded text-xs flex items-center"
                                        onClick={() => startEditing(rootEntity)}
                                    >
                                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                        </svg>
                                        Edit
                                    </button>
                                )}
                                <button 
                                    className="text-blue-500 hover:text-blue-700 focus:outline-none w-5 h-5 flex items-center justify-center"
                                    onClick={() => toggleNodeExpansion(rootEntity)}
                                >
                                    {expandedNode === rootEntity ? '▼' : '▶'}
                                </button>
                            </div>
                        </div>
                        
                        {expandedNode === rootEntity && (
                            <div className="mt-3 border-t border-blue-200 pt-3">
                                {fetchingNodeDetails && (
                                    <div className="flex items-center justify-center p-4 text-gray-500">
                                        <div className="animate-spin rounded-full h-5 w-5 border-2 border-t-transparent border-blue-500 mr-2"></div>
                                        <span>Loading details...</span>
                                    </div>
                                )}
                                
                                {selectedNodeDetails[rootEntity]?.node_data?.data && !editingNode && (
                                    <div className="bg-white rounded border border-gray-100">
                                        <table className="w-full">
                                            <tbody>
                                                {Object.entries(selectedNodeDetails[rootEntity].node_data.data).map(([key, value]) => (
                                                    <tr key={key} className="border-b border-gray-100 last:border-b-0">
                                                        <td className="py-2 px-3 font-medium text-gray-700 whitespace-nowrap">{key}</td>
                                                        <td className="py-2 px-3 text-gray-900">{formatNodeDetail(value)}</td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                )}
                                
                                {/* Add the enhance node section for root entity */}
                                {selectedNodeDetails[rootEntity]?.node_data?.data && !editingNode && (
                                    renderEnhancedNodeData(rootEntity)
                                )}
                                
                                {selectedNodeDetails[rootEntity]?.node_data?.data && editingNode === rootEntity && (
                                    <div className="bg-white rounded border border-gray-200 p-3">
                                        <div className="flex justify-between items-center mb-3">
                                            <h4 className="font-medium text-gray-700">Edit Metadata</h4>
                                            <div className="flex space-x-2">
                                                <button 
                                                    className="text-blue-500 hover:bg-blue-50 px-2 py-1 rounded text-xs flex items-center"
                                                    onClick={addMetadataField}
                                                >
                                                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                    </svg>
                                                    Add Field
                                                </button>
                                            </div>
                                        </div>
                                        
                                        {editFormError && (
                                            <div className="text-red-500 p-2 bg-red-50 rounded mb-3 text-xs">
                                                {editFormError}
                                            </div>
                                        )}
                                        
                                        <div className="max-h-64 overflow-y-auto border border-gray-100 rounded mb-3">
                                            <table className="w-full">
                                                <tbody>
                                                    {Object.entries(editFormData).map(([key, value]) => (
                                                        <tr key={key} className="border-b border-gray-100 last:border-b-0">
                                                            <td className="py-2 px-2 text-gray-700 w-1/3">
                                                                <input
                                                                    type="text"
                                                                    value={key}
                                                                    onChange={(e) => {
                                                                        const newKey = e.target.value;
                                                                        const newValue = editFormData[key];
                                                                        removeMetadataField(key);
                                                                        handleFormChange(newKey, newValue);
                                                                    }}
                                                                    className="w-full p-1 text-xs border border-gray-200 rounded"
                                                                />
                                                            </td>
                                                            <td className="py-2 px-2 text-gray-900">
                                                                <div className="flex items-center">
                                                                    <textarea
                                                                        value={value}
                                                                        onChange={(e) => handleFormChange(key, e.target.value)}
                                                                        className="w-full p-1 text-xs border border-gray-200 rounded resize-y"
                                                                        rows={value.includes('\n') ? 3 : 1}
                                                                    />
                                                                    <button 
                                                                        className="ml-1 text-red-400 hover:text-red-600"
                                                                        onClick={() => removeMetadataField(key)}
                                                                    >
                                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                                        </svg>
                                                                    </button>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </div>
                                        
                                        <div className="flex justify-end space-x-2">
                                            <button 
                                                className="px-3 py-1 text-gray-600 hover:bg-gray-100 border border-gray-200 rounded text-xs"
                                                onClick={cancelEditing}
                                                disabled={isSaving}
                                            >
                                                Cancel
                                            </button>
                                            <button 
                                                className="px-3 py-1 text-white bg-blue-500 hover:bg-blue-600 rounded text-xs flex items-center"
                                                onClick={() => saveMetadataChanges(rootEntity)}
                                                disabled={isSaving}
                                            >
                                                {isSaving ? (
                                                    <>
                                                        <div className="animate-spin rounded-full h-3 w-3 border-2 border-t-transparent border-white mr-1"></div>
                                                        <span>Saving...</span>
                                                    </>
                                                ) : (
                                                    <>
                                                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                                                        </svg>
                                                        <span>Save Changes</span>
                                                    </>
                                                )}
                                            </button>
                                        </div>
                                    </div>
                                )}
                                
                                {selectedNodeDetails[rootEntity] && !selectedNodeDetails[rootEntity]?.node_data?.data && (
                                    <p className="text-gray-500 italic p-3">No node_data.data available.</p>
                                )}
                                
                                {!selectedNodeDetails[rootEntity] && !fetchingNodeDetails && (
                                    <button 
                                        className="w-full py-2 text-center text-blue-500 hover:bg-blue-50 border border-blue-200 rounded flex items-center justify-center"
                                        onClick={() => fetchNodeDetails(rootEntity)}
                                    >
                                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        Fetch root entity details
                                    </button>
                                )}
                            </div>
                        )}
                    </div>
                )}
                
                {selectedEntities.length === 0 ? (
                    <div className="text-center py-10 text-gray-500">
                        <svg className="w-12 h-12 mx-auto mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p>No entities selected</p>
                        <p className="text-sm mt-1">
                            {connectMode ? 'Select entities in the graph to create connections' : 'Select entities in the graph to view details'}
                        </p>
                    </div>
                ) : selectedEntities.filter(isValidUUID).length === 0 ? (
                    <div className="text-center py-10 text-gray-500">
                        <svg className="w-12 h-12 mx-auto mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p>No valid entities selected</p>
                        <p className="text-sm mt-1">Selected entities don&apos;t have a UUID format</p>
                        <p className="text-xs mt-2 max-w-md mx-auto">Only entities with IDs in the format &quot;xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx&quot; can be queried</p>
                    </div>
                ) : (
                    <div>
                        <ul className="list-none pl-0 space-y-3">
                            {selectedEntities
                                .filter(isValidUUID)
                                .map((entity, index) => renderNodeItem(entity, index))}
                        </ul>
                    </div>
                )}
            </div>
        </div>
    );
}