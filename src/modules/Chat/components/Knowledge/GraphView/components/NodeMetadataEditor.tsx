import React from 'react';

interface NodeMetadataEditorProps {
    editFormData: Record<string, string>;
    editFormError: string | null;
    isSaving: boolean;
    onSave: () => void;
    onCancel: () => void;
    onAddField: () => void;
    onRemoveField: (key: string) => void;
    onFieldChange: (key: string, value: string) => void;
    onKeyChange: (oldKey: string, newKey: string) => void;
}

const NodeMetadataEditor: React.FC<NodeMetadataEditorProps> = ({
    editFormData,
    editFormError,
    isSaving,
    onSave,
    onCancel,
    onAddField,
    onRemoveField,
    onFieldChange,
    onKeyChange
}) => {
    return (
        <div className="bg-white rounded border border-gray-200 p-3">
            <div className="flex justify-between items-center mb-3">
                <h4 className="font-medium text-gray-700">Edit Metadata</h4>
                <div className="flex space-x-2">
                    <button 
                        className="text-blue-500 hover:bg-blue-50 px-2 py-1 rounded text-xs flex items-center"
                        onClick={onAddField}
                    >
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Field
                    </button>
                </div>
            </div>
            
            {editFormError && (
                <div className="text-red-500 p-2 bg-red-50 rounded mb-3 text-xs">
                    {editFormError}
                </div>
            )}
            
            <div className="max-h-64 overflow-y-auto border border-gray-100 rounded mb-3">
                <table className="w-full">
                    <tbody>
                        {Object.entries(editFormData).map(([key, value]) => (
                            <tr key={key} className="border-b border-gray-100 last:border-b-0">
                                <td className="py-2 px-2 text-gray-700 w-1/3">
                                    <input
                                        type="text"
                                        value={key}
                                        onChange={(e) => onKeyChange(key, e.target.value)}
                                        className="w-full p-1 text-xs border border-gray-200 rounded"
                                    />
                                </td>
                                <td className="py-2 px-2 text-gray-900">
                                    <div className="flex items-center">
                                        <textarea
                                            value={value}
                                            onChange={(e) => onFieldChange(key, e.target.value)}
                                            className="w-full p-1 text-xs border border-gray-200 rounded resize-y"
                                            rows={value.includes('\n') ? 3 : 1}
                                        />
                                        <button 
                                            className="ml-1 text-red-400 hover:text-red-600"
                                            onClick={() => onRemoveField(key)}
                                        >
                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            
            <div className="flex justify-end space-x-2">
                <button 
                    className="px-3 py-1 text-gray-600 hover:bg-gray-100 border border-gray-200 rounded text-xs"
                    onClick={onCancel}
                    disabled={isSaving}
                >
                    Cancel
                </button>
                <button 
                    className="px-3 py-1 text-white bg-blue-500 hover:bg-blue-600 rounded text-xs flex items-center"
                    onClick={onSave}
                    disabled={isSaving}
                >
                    {isSaving ? (
                        <>
                            <div className="animate-spin rounded-full h-3 w-3 border-2 border-t-transparent border-white mr-1"></div>
                            <span>Saving...</span>
                        </>
                    ) : (
                        <>
                            <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Save Changes</span>
                        </>
                    )}
                </button>
            </div>
        </div>
    );
};

export default NodeMetadataEditor;