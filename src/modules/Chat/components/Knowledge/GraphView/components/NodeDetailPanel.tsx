import React, { useState } from 'react';
import { NodeDetails } from '../types';
import { formatNodeDetail } from '../utils';
import NodeMetadataEditor from './NodeMetadataEditor';
import NodeEnhancementPanel from './NodeEnhancementPanel';
import { EnhancedNodeData } from '../types';

// Define types for complex node data structures
type NodeDataValue = string | number | boolean | null | NodeDataArray | NodeDataObject | undefined | unknown;
type NodeDataArray = NodeDataValue[];
type NodeDataObject = { [key: string]: NodeDataValue };
type NodeDataRecord = Record<string, NodeDataValue>;

interface NodeDetailPanelProps {
    nodeId: string;
    details?: NodeDetails;
    isLoading: boolean;
    error: string | null;
    isEditing: boolean;
    editFormData: Record<string, string>;
    editFormError: string | null;
    isSaving: boolean;
    enhancedData?: EnhancedNodeData;
    isEnhancing: boolean;
    enhancementError: string | null;
    onStartEditing: () => void;
    onSaveChanges: () => void;
    onCancelEditing: () => void;
    onAddMetadataField: () => void;
    onRemoveMetadataField: (key: string) => void;
    onFieldChange: (key: string, value: string) => void;
    onKeyChange: (oldKey: string, newKey: string) => void;
    onFetchDetails: () => void;
    onEnhanceNode: () => void;
}

// Helper function to ensure string values
const ensureString = (value: unknown): string => {
    if (value === null || value === undefined) return '';
    return String(value);
};

// Component to render a collapsible section
const CollapsibleSection = ({ title, children }: { title: string, children: React.ReactNode }) => {
    const [isOpen, setIsOpen] = useState(true);
    
    return (
        <div className="border border-gray-100 rounded mb-4">
            <button 
                className="w-full flex justify-between items-center p-3 text-left font-medium text-gray-700 hover:bg-gray-50"
                onClick={() => setIsOpen(!isOpen)}
            >
                {title}
                <svg 
                    className={`w-4 h-4 ${isOpen ? 'transform rotate-180' : ''}`} 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24" 
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </button>
            {isOpen && <div className="p-3 border-t border-gray-100">{children}</div>}
        </div>
    );
};

// Component to render data as a table
const DataTable = ({ data }: { data: NodeDataRecord | Record<string, unknown> }) => {
    return (
        <table className="w-full">
            <tbody>
                {Object.entries(data).map(([key, value]) => (
                    <tr key={key} className="border-b border-gray-100 last:border-b-0">
                        <td className="py-2 px-3 font-medium text-gray-700 whitespace-nowrap align-top">{key}</td>
                        <td className="py-2 px-3 text-gray-900">
                            {Array.isArray(value) ? (
                                <div className="space-y-2">
                                    {value.length === 0 ? (
                                        <span className="text-gray-500 italic">Empty array</span>
                                    ) : (
                                        value.map((item, index) => (
                                            <div key={index} className="p-2 border border-gray-100 rounded">
                                                {Array.isArray(item) ? (
                                                    <div className="flex flex-wrap gap-2">
                                                        {item.map((subItem, subIndex) => (
                                                            <span key={subIndex} className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">
                                                                {formatNodeDetail(subItem)}
                                                            </span>
                                                        ))}
                                                    </div>
                                                ) : typeof item === 'object' && item !== null ? (
                                                    <pre className="text-xs overflow-auto max-h-40">
                                                        {JSON.stringify(item, null, 2)}
                                                    </pre>
                                                ) : (
                                                    formatNodeDetail(item)
                                                )}
                                            </div>
                                        ))
                                    )}
                                </div>
                            ) : typeof value === 'object' && value !== null ? (
                                <pre className="text-xs overflow-auto max-h-60">
                                    {JSON.stringify(value, null, 2)}
                                </pre>
                            ) : (
                                formatNodeDetail(value)
                            )}
                        </td>
                    </tr>
                ))}
            </tbody>
        </table>
    );
};

const NodeDetailPanel: React.FC<NodeDetailPanelProps> = ({
    nodeId,
    details,
    isLoading,
    error,
    isEditing,
    editFormData,
    editFormError,
    isSaving,
    enhancedData,
    isEnhancing,
    enhancementError,
    onStartEditing,
    onSaveChanges,
    onCancelEditing,
    onAddMetadataField,
    onRemoveMetadataField,
    onFieldChange,
    onKeyChange,
    onFetchDetails,
    onEnhanceNode
}) => {
    if (isLoading) {
        return (
            <div className="flex items-center justify-center p-4 text-gray-500">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-t-transparent border-blue-500 mr-2"></div>
                <span>Loading details...</span>
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-red-500 p-3 bg-red-50 rounded mb-2">
                <p className="font-semibold">Error loading node details:</p>
                <p className="mb-2">{error}</p>
                <button 
                    className="text-blue-500 hover:underline flex items-center"
                    onClick={onFetchDetails}
                >
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Retry
                </button>
            </div>
        );
    }

    if (details) {
        // Extract node name and information with proper type handling
        const nodeType = ensureString(details.node_type) || 'Unknown';
        const collection = ensureString(details.collection) || 'Unknown';
        
        if (!isEditing) {
            return (
                <>
                    {/* Node Header */}
                    <div className="bg-white p-4 rounded-lg border border-gray-200 mb-4">
                        {/* <h3 className="text-lg font-semibold text-gray-800">{nodeName}</h3> */}
                        <div className="mt-2 flex flex-wrap gap-2">
                            <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                Type: {nodeType}
                            </span>
                            <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                                Collection: {collection}
                            </span>
                            <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full">
                                ID: {nodeId}
                            </span>
                        </div>
                    </div>
                    
                    {/* Metadata action (editable data) */}
                    {details.node_data?.data && (
                        <CollapsibleSection title="Metadata">
                            <div className="flex justify-between items-center mb-2">
                                <span className="text-sm text-gray-500">Node metadata fields</span>
                                <button 
                                    className="text-blue-500 hover:bg-blue-50 px-2 py-1 rounded text-xs flex items-center"
                                    onClick={onStartEditing}
                                >
                                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                    </svg>
                                    Edit Metadata
                                </button>
                            </div>
                            <DataTable data={details.node_data.data as NodeDataRecord} />
                        </CollapsibleSection>
                    )}
                    
                    {/* Node Data section (all non-data properties) */}
                    <CollapsibleSection title="Node Data Properties">
                        <DataTable 
                            data={Object.fromEntries(
                                Object.entries(details.node_data || {})
                                    .filter(([key]) => key !== 'data')
                            ) as NodeDataRecord} 
                        />
                    </CollapsibleSection>
                    
                    {/* Additional node properties */}
                    {Object.entries(details)
                        .filter(([key]) => !['node_data', 'node_type', 'collection', 'id'].includes(key))
                        .map(([key, value]) => (
                            <CollapsibleSection key={key} title={key.charAt(0).toUpperCase() + key.slice(1)}>
                                {typeof value === 'object' && value !== null ? (
                                    <DataTable data={value as NodeDataRecord} />
                                ) : (
                                    <p>{formatNodeDetail(value)}</p>
                                )}
                            </CollapsibleSection>
                        ))
                    }
                    
                    {/* Node enhancement panel */}
                    <NodeEnhancementPanel 
                        nodeId={nodeId}
                        enhancedData={enhancedData}
                        isEnhancing={isEnhancing}
                        error={enhancementError}
                        onEnhance={onEnhanceNode}
                    />
                </>
            );
        } else {
            // When editing, use the existing NodeMetadataEditor
            return (
                <NodeMetadataEditor 
                    editFormData={editFormData}
                    editFormError={editFormError}
                    isSaving={isSaving}
                    onSave={onSaveChanges}
                    onCancel={onCancelEditing}
                    onAddField={onAddMetadataField}
                    onRemoveField={onRemoveMetadataField}
                    onFieldChange={onFieldChange}
                    onKeyChange={onKeyChange}
                />
            );
        }
    }

    return (
        <button 
            className="w-full py-2 text-center text-blue-500 hover:bg-blue-50 border border-blue-200 rounded flex items-center justify-center"
            onClick={onFetchDetails}
        >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Fetch node details
        </button>
    );
};

export default NodeDetailPanel;