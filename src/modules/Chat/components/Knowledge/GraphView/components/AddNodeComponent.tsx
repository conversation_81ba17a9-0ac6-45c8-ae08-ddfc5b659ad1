import React, { useState } from 'react';
import cookies from 'js-cookie';
import { ApiRequest } from '@/fetch/FetchRequest';
import { CookieName } from '@/enums/CookieName';
import { captureError } from '@/utils/errorHandling';
import { GraphNodeType, NodeData } from '../types';

interface AddNodeComponentProps {
  onNodeAdded: (nodeData: NodeData) => void;
}

interface MetadataField {
  key: string;
  value: string;
}

const AddNodeComponent: React.FC<AddNodeComponentProps> = ({ onNodeAdded }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [nodeName, setNodeName] = useState('');
  const [nodeType, setNodeType] = useState<GraphNodeType>(GraphNodeType.ENTITY);
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Metadata fields state
  const [metadataFields, setMetadataFields] = useState<MetadataField[]>([
    { key: "name", value: "" },
    { key: "description", value: "" }
  ]);

  const availableNodeTypes = [
    GraphNodeType.ENTITY,
    GraphNodeType.DOCUMENT,
    GraphNodeType.TABLE,
    GraphNodeType.OBJECT,
    GraphNodeType.FILE,
    GraphNodeType.INSTANCE
  ];

  // Add a new empty metadata field
  const addMetadataField = () => {
    setMetadataFields([...metadataFields, { key: "", value: "" }]);
  };

  // Remove a metadata field
  const removeMetadataField = (index: number) => {
    const newFields = [...metadataFields];
    newFields.splice(index, 1);
    setMetadataFields(newFields);
  };

  // Update a metadata field
  const updateMetadataField = (index: number, keyOrValue: 'key' | 'value', newValue: string) => {
    const newFields = [...metadataFields];
    newFields[index][keyOrValue] = newValue;
    
    // If we're updating the name field in metadata, also update the node name
    if (keyOrValue === 'value' && newFields[index].key === 'name') {
      setNodeName(newValue);
    }
    
    setMetadataFields(newFields);
  };

  // Sync node name with the name field in metadata
  const updateNodeName = (name: string) => {
    setNodeName(name);
    
    // Find and update the 'name' field in metadata
    const nameFieldIndex = metadataFields.findIndex(field => field.key === 'name');
    if (nameFieldIndex >= 0) {
      const newFields = [...metadataFields];
      newFields[nameFieldIndex].value = name;
      setMetadataFields(newFields);
    } else {
      // If no name field exists, add one
      setMetadataFields([...metadataFields, { key: 'name', value: name }]);
    }
  };

  const handleAddNode = async () => {
    if (!nodeName.trim()) {
      setError('Node name is required');
      return;
    }
  
    try {
      setIsCreating(true);
      setError(null);
      
      const bearer = cookies.get(CookieName.API_TOKEN);
      
      // Prepare attributes from fields
      const attributes: Record<string, string | number | boolean | object> = {};
      const data: Record<string, string | number | boolean | object> = {};
      const extraction_metadata: Record<string, string | number | boolean | object> = {};
      
      metadataFields.forEach(field => {
        if (field.key && field.value) {
          // Try to parse JSON values
          try {
            if (field.value.startsWith('{') && field.value.endsWith('}') || 
                field.value.startsWith('[') && field.value.endsWith(']')) {
              attributes[field.key] = JSON.parse(field.value);
            } else {
              attributes[field.key] = field.value;
            }
          } catch {
            attributes[field.key] = field.value;
          }
        }
      });
      
      // Always ensure the name is added to metadata
      attributes.name = nodeName.trim();
      attributes.created_at = new Date().toISOString();
      
      // Prepare the request payload to match the API structure
      const requestPayload = {
        node_type: nodeType,
        attributes,
        data,
        extraction_metadata
      };
       
      console.log('Creating new node:', requestPayload);
      
      // Add name as a query parameter instead of in the body
      const endpoint = `graph_query/create_node?name=${encodeURIComponent(nodeName.trim())}`;
      
      const { data: responseData, response } = await ApiRequest(endpoint, {
        bearer,
        init: { 
          method: "POST",
          body: JSON.stringify(requestPayload)
        }
      });
      
      if (response?.ok && responseData) {
        console.log('Node created successfully:', responseData);
        
        // Reset form
        setNodeName('');
        setNodeType(GraphNodeType.ENTITY);
        setMetadataFields([
          { key: "name", value: "" },
          { key: "description", value: "" }
        ]);
        
        // Close modal
        setIsModalOpen(false);
        
        // Notify parent component
        onNodeAdded(responseData as NodeData);
        
        return responseData;
      } else {
        throw new Error(`Failed to create node: ${response?.statusText || 'Unknown error'}`);
      }
    } catch (err) {
      console.error('Error creating node:', err);
      captureError(err, {
        action: 'createNode',
        component: 'AddNodeComponent'
      });
      setError(err instanceof Error ? err.message : 'Failed to create node');
      return null;
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <>
      <button
        className="px-4 py-2 bg-green-600 text-white rounded flex items-center hover:bg-green-700"
        onClick={() => setIsModalOpen(true)}
      >
        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add Node
      </button>

      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30">
          <div className="bg-white rounded-lg shadow-xl p-6 max-w-lg w-full m-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Create New Node</h3>
              <button 
                className="text-gray-400 hover:text-gray-500"
                onClick={() => setIsModalOpen(false)}
                disabled={isCreating}
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Node Name
              </label>
              <input
                type="text"
                value={nodeName}
                onChange={(e) => updateNodeName(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                placeholder="Enter node name"
                disabled={isCreating}
                autoFocus
              />
            </div>
            
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Node Type
              </label>
              <select
                value={nodeType}
                onChange={(e) => setNodeType(e.target.value as GraphNodeType)}
                className="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                disabled={isCreating}
              >
                {availableNodeTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
            
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  Metadata Fields
                </label>
                <button
                  type="button"
                  onClick={addMetadataField}
                  className="text-sm text-indigo-600 hover:text-indigo-900 flex items-center"
                  disabled={isCreating}
                >
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Add Field
                </button>
              </div>
              
              <div className="space-y-3 max-h-64 overflow-y-auto p-2 border border-gray-200 rounded-md">
                {metadataFields.map((field, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={field.key}
                      onChange={(e) => updateMetadataField(index, 'key', e.target.value)}
                      placeholder="Key"
                      className="w-1/3 p-2 text-sm border border-gray-300 rounded-md"
                      disabled={isCreating || field.key === 'name'} // Prevent editing the name key
                    />
                    <input
                      type="text"
                      value={field.value}
                      onChange={(e) => updateMetadataField(index, 'value', e.target.value)}
                      placeholder="Value"
                      className="flex-1 p-2 text-sm border border-gray-300 rounded-md"
                      disabled={isCreating}
                    />
                    {field.key !== 'name' && ( // Prevent removing the name field
                      <button
                        type="button"
                        onClick={() => removeMetadataField(index)}
                        className="text-red-500 hover:text-red-700"
                        disabled={isCreating}
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                      </button>
                    )}
                  </div>
                ))}
                {metadataFields.length === 0 && (
                  <p className="text-sm text-gray-500 p-2">No metadata fields added yet</p>
                )}
              </div>
            </div>
            
            {error && (
              <div className="mb-4 p-3 bg-red-50 text-red-600 text-sm rounded-md border border-red-200">
                {error}
              </div>
            )}
            
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                onClick={() => setIsModalOpen(false)}
                disabled={isCreating}
              >
                Cancel
              </button>
              
              <button
                className="px-4 py-2 bg-green-600 text-white rounded-md shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 flex items-center"
                onClick={handleAddNode}
                disabled={isCreating || !nodeName.trim()}
              >
                {isCreating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-t-transparent border-white mr-2"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Node
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AddNodeComponent;