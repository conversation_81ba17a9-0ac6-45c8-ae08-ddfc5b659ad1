import React, { useState, useEffect } from 'react';

// Define available node types
export const AVAILABLE_NODE_TYPES = [
  { id: 'artifact', label: 'Artifact' },
  { id: 'entity', label: 'Entity' },
  { id: 'identifier', label: 'Identifier' },
  { id: 'object', label: 'Object' },
  { id: 'file', label: 'File' },
  { id: 'table', label: 'Table' },
  { id: 'instance', label: 'Instance' }
];

// Default to only having "object" selected
export const DEFAULT_SELECTED_NODE_TYPES = ['object'];

interface NodeTypeSelectorProps {
  selectedNodeTypes?: string[];
  onChange: (selectedTypes: string[]) => void;
  isLoading?: boolean;
  isExpanded?: boolean;
  onToggleExpand?: () => void;
}

const NodeTypeSelector: React.FC<NodeTypeSelectorProps> = ({ 
  selectedNodeTypes = DEFAULT_SELECTED_NODE_TYPES, 
  onChange,
  isLoading = false,
  isExpanded = false, // Default to collapsed
  onToggleExpand = () => {}
}) => {
  // Create internal state to track selected types before applying
  const [pendingSelection, setPendingSelection] = useState<string[]>(selectedNodeTypes);

  // Update internal state when prop changes (for initialization)
  useEffect(() => {
    setPendingSelection(selectedNodeTypes);
  }, [selectedNodeTypes]);

  const handleTypeChange = (type: string) => {
    // Toggle selection in the pending state only, don't apply yet
    const newSelection = pendingSelection.includes(type)
      ? pendingSelection.filter(t => t !== type)
      : [...pendingSelection, type];
    
    setPendingSelection(newSelection);
  };

  // Handle "Select All" functionality
  const handleSelectAll = () => {
    setPendingSelection(AVAILABLE_NODE_TYPES.map(type => type.id));
  };

  // Handle "Clear All" functionality
  const handleClearAll = () => {
    setPendingSelection([]);
  };
  
  // Handle applying the current selection
  const handleApplyFilters = () => {
    if (pendingSelection.length === 0) return;
    onChange(pendingSelection);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-4 overflow-hidden">
      <div 
        className="flex justify-between items-center p-4 cursor-pointer bg-gray-50" 
        onClick={onToggleExpand}
      >
        <div className="flex items-center">
          <svg 
            className={`w-5 h-5 mr-2 transition-transform ${isExpanded ? 'transform rotate-90' : ''}`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
          </svg>
          <h3 className="text-lg font-medium">Filter Node Types</h3>
        </div>
        <div className="flex items-center space-x-2">
          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
            {selectedNodeTypes.length} selected
          </span>
          <span className="text-sm text-gray-500">
            {isExpanded ? 'Click to collapse' : 'Click to expand'}
          </span>
        </div>
      </div>
      
      {isExpanded && (
        <div className="p-4 border-t border-gray-200">
          <div className="flex justify-between items-center mb-3">
            <div className="flex space-x-2">
              <button 
                onClick={handleSelectAll}
                disabled={isLoading}
                className="text-xs px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded border border-gray-300"
              >
                Select All
              </button>
              <button 
                onClick={handleClearAll}
                disabled={isLoading}
                className="text-xs px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded border border-gray-300"
              >
                Clear All
              </button>
            </div>
            
            {/* Show a badge if pending selection differs from applied selection */}
            {JSON.stringify(pendingSelection.sort()) !== JSON.stringify(selectedNodeTypes.sort()) && (
              <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                Unapplied changes
              </span>
            )}
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 mb-4">
            {AVAILABLE_NODE_TYPES.map(type => (
              <div key={type.id} className="flex items-center">
                <input
                  type="checkbox"
                  id={`node-type-${type.id}`}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  checked={pendingSelection.includes(type.id)}
                  onChange={() => handleTypeChange(type.id)}
                  disabled={isLoading}
                />
                <label htmlFor={`node-type-${type.id}`} className="ml-2 text-sm text-gray-700">
                  {type.label}
                </label>
              </div>
            ))}
          </div>
          
          {pendingSelection.length === 0 && (
            <p className="text-sm text-red-500 mb-4">
              Please select at least one node type
            </p>
          )}
          
          <div className="flex justify-end">
            <button
              onClick={handleApplyFilters}
              disabled={pendingSelection.length === 0 || isLoading}
              className={`px-4 py-2 rounded text-white ${
                pendingSelection.length === 0 || isLoading
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700'
              }`}
            >
              {isLoading ? 'Loading...' : 'Apply Filters'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NodeTypeSelector;