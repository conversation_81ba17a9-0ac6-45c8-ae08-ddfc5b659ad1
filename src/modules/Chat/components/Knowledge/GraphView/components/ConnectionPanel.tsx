import React from 'react';

interface ConnectionPanelProps {
    connectMode: boolean;
    showTypeSelector: boolean;
    sourceNode: string | null;
    targetNode: string | null;
    edgeType: string;
    availableEdgeTypes: string[];
    showCustomEdgeInput: boolean;
    customEdgeType: string;
    connectionStep: 'source' | 'target' | 'type';
    connectionError: string | null;
    creatingEdge: boolean;
    getNodeDisplayName: (nodeId: string) => string;
    onToggleConnectMode: () => void;
    onCreateEdge: () => void;
    onReset: () => void;
    onShowTypeSelector: () => void;
    onEdgeTypeChange: (type: string) => void;
    onShowCustomEdgeInput: (show: boolean) => void;
    onCustomEdgeTypeChange: (value: string) => void;
    onChangeSource: () => void;
    onChangeTarget: () => void;
}

const ConnectionPanel: React.FC<ConnectionPanelProps> = ({
    connectMode,
    showTypeSelector,
    sourceNode,
    targetNode,
    edgeType,
    availableEdgeTypes,
    showCustomEdgeInput,
    customEdgeType,
    connectionStep,
    connectionError,
    creatingEdge,
    getNodeDisplayName,
    onToggleConnectMode,
    onCreateEdge,
    onReset,
    onShowTypeSelector,
    onEdgeTypeChange,
    onShowCustomEdgeInput,
    onCustomEdgeTypeChange,
    onChangeSource,
    onChangeTarget
}) => {
    if (!connectMode) return null;
    
    // Show a different panel based on the connection step
    if (showTypeSelector && sourceNode && targetNode) {
        // Type selection panel - shown after both nodes are selected
        return (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-20">
                <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full m-4">
                    <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-medium text-gray-900">Create Connection</h3>
                        <button 
                            className="text-gray-400 hover:text-gray-500"
                            onClick={onReset}
                        >
                            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    
                    <div className="mb-6">
                        <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg mb-2">
                            <div className="flex items-center">
                                <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center mr-3">
                                    1
                                </div>
                                <span className="font-medium">Source:</span>
                            </div>
                            <span>{sourceNode ? getNodeDisplayName(sourceNode) : ''}</span>
                        </div>
                        
                        <div className="flex justify-center my-2">
                            <svg className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                        </div>
                        
                        <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <div className="flex items-center">
                                <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center mr-3">
                                    2
                                </div>
                                <span className="font-medium">Target:</span>
                            </div>
                            <span>{targetNode ? getNodeDisplayName(targetNode) : ''}</span>
                        </div>
                    </div>
                    
                    <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Connection Type</label>
                        <div className="mb-4">
                            <select
                                value={showCustomEdgeInput ? "custom" : edgeType}
                                onChange={(e) => {
                                    if (e.target.value === "custom") {
                                        onShowCustomEdgeInput(true);
                                    } else {
                                        onShowCustomEdgeInput(false);
                                        onEdgeTypeChange(e.target.value);
                                    }
                                }}
                                className="w-full p-3 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                disabled={creatingEdge}
                            >
                                {availableEdgeTypes.map(type => (
                                    <option key={type} value={type}>{type}</option>
                                ))}
                                <option value="custom">Custom Type...</option>
                            </select>
                        </div>
                        
                        {showCustomEdgeInput && (
                            <input
                                type="text"
                                value={customEdgeType}
                                onChange={(e) => onCustomEdgeTypeChange(e.target.value)}
                                placeholder="Enter custom connection type..."
                                className="w-full p-3 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                                disabled={creatingEdge}
                                autoFocus
                            />
                        )}
                    </div>
                    
                    {connectionError && (
                        <div className="mb-4 p-3 bg-red-50 text-red-600 text-sm rounded-md border border-red-200">
                            {connectionError}
                        </div>
                    )}
                    
                    <div className="flex justify-between">
                        <button
                            className="px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            onClick={onReset}
                            disabled={creatingEdge}
                        >
                            Cancel
                        </button>
                        
                        <button
                            className={`px-4 py-2 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 flex items-center ${
                                sourceNode && targetNode && (showCustomEdgeInput ? customEdgeType : edgeType)
                                    ? 'bg-indigo-600 hover:bg-indigo-700'
                                    : 'bg-gray-400 cursor-not-allowed'
                            }`}
                            onClick={onCreateEdge}
                            disabled={!sourceNode || !targetNode || creatingEdge || (showCustomEdgeInput ? !customEdgeType : !edgeType)}
                        >
                            {creatingEdge ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-t-transparent border-white mr-2"></div>
                                    Creating...
                                </>
                            ) : (
                                <>
                                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                    </svg>
                                    Create Connection
                                </>
                            )}
                        </button>
                    </div>
                </div>
            </div>
        );
    }
    
    // Display status panel during the selection process
    return (
        <div className="absolute top-4 right-4 z-10">
            <div className="bg-white rounded-lg shadow-lg p-4 border border-indigo-200 w-72">
                <div className="flex justify-between items-center mb-3">
                    <h3 className="font-medium text-indigo-700">Connection Mode</h3>
                    <button 
                        className="text-gray-400 hover:text-gray-500 focus:outline-none"
                        onClick={onToggleConnectMode}
                    >
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div className="space-y-3">
                    <div className={`flex items-center p-2 rounded-md ${
                        connectionStep === 'source' ? 'bg-indigo-50 border border-indigo-100' : 
                        sourceNode ? 'bg-green-50 border border-green-100' : 'bg-gray-50 border border-gray-200'
                    }`}>
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                            connectionStep === 'source' ? 'bg-indigo-500 text-white' : 
                            sourceNode ? 'bg-green-500 text-white' : 'bg-gray-300 text-white'
                        }`}>
                            1
                        </div>
                        <div className="flex-grow">
                            <span className="text-sm font-medium">
                                {connectionStep === 'source' ? 'Select source node' : 
                                 sourceNode ? `Source: ${getNodeDisplayName(sourceNode)}` : 'Source node'}
                            </span>
                        </div>
                        {sourceNode && connectionStep !== 'source' && (
                            <button 
                                className="text-sm text-indigo-600 hover:text-indigo-500"
                                onClick={onChangeSource}
                            >
                                Change
                            </button>
                        )}
                    </div>
                    
                    <div className={`flex items-center p-2 rounded-md ${
                        connectionStep === 'target' ? 'bg-indigo-50 border border-indigo-100' : 
                        targetNode ? 'bg-green-50 border border-green-100' : 'bg-gray-50 border border-gray-200'
                    }`}>
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                            connectionStep === 'target' ? 'bg-indigo-500 text-white' : 
                            targetNode ? 'bg-green-500 text-white' : 'bg-gray-300 text-white'
                        }`}>
                            2
                        </div>
                        <div className="flex-grow">
                            <span className="text-sm font-medium">
                                {connectionStep === 'target' ? 'Select target node' : 
                                 targetNode ? `Target: ${getNodeDisplayName(targetNode)}` : 'Target node'}
                            </span>
                        </div>
                        {targetNode && connectionStep !== 'target' && (
                            <button 
                                className="text-sm text-indigo-600 hover:text-indigo-500"
                                onClick={onChangeTarget}
                            >
                                Change
                            </button>
                        )}
                    </div>
                    
                    <div className={`flex items-center p-2 rounded-md ${
                        connectionStep === 'type' ? 'bg-indigo-50 border border-indigo-100' : 'bg-gray-50 border border-gray-200'
                    }`}>
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${
                            connectionStep === 'type' ? 'bg-indigo-500 text-white' : 'bg-gray-300 text-white'
                        }`}>
                            3
                        </div>
                        <span className="text-sm font-medium">Choose connection type</span>
                    </div>
                </div>
                
                {connectionError && (
                    <div className="mt-3 p-2 bg-red-50 text-red-600 text-sm rounded border border-red-200">
                        {connectionError}
                    </div>
                )}
                
                <div className="mt-4 flex justify-between">
                    <button
                        className="px-3 py-1.5 bg-white text-gray-600 border border-gray-300 rounded text-sm hover:bg-gray-50"
                        onClick={onReset}
                    >
                        Reset
                    </button>
                    
                    {sourceNode && targetNode && (
                        <button
                            className="px-3 py-1.5 bg-indigo-600 text-white rounded text-sm hover:bg-indigo-700"
                            onClick={onShowTypeSelector}
                        >
                            Continue
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ConnectionPanel;