import React from 'react';
import { EnhancedNodeData } from '../types';

interface NodeEnhancementPanelProps {
    nodeId: string;
    enhancedData?: EnhancedNodeData;
    isEnhancing: boolean;
    error?: string | null;
    onEnhance: (nodeId: string) => void;
}

const NodeEnhancementPanel: React.FC<NodeEnhancementPanelProps> = ({ 
    nodeId, 
    enhancedData, 
    isEnhancing, 
    error, 
    onEnhance 
}) => {
    if (isEnhancing) {
        return (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-100 rounded">
                <div className="flex items-center justify-center text-blue-700">
                    <div className="animate-spin rounded-full h-5 w-5 border-2 border-t-transparent border-blue-500 mr-2"></div>
                    <span>Enhancing node with AI...</span>
                </div>
            </div>
        );
    }
    
    if (error) {
        return (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded">
                <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium text-red-700">Enhancement Failed</h4>
                    <button 
                        className="text-blue-500 hover:bg-blue-50 px-2 py-1 rounded text-xs flex items-center"
                        onClick={() => onEnhance(nodeId)}
                    >
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Retry
                    </button>
                </div>
                <p className="text-red-600 text-sm">{error}</p>
            </div>
        );
    }
    
    if (enhancedData) {
        return (
            <div className="mt-4 p-3 bg-indigo-50 border border-indigo-100 rounded">
                <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium text-indigo-700">AI Enhanced Information</h4>
                    <button 
                        className="text-blue-500 hover:bg-blue-50 px-2 py-1 rounded text-xs flex items-center"
                        onClick={() => onEnhance(nodeId)}
                    >
                        <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        Refresh
                    </button>
                </div>
                <div className="bg-white rounded border border-indigo-100 p-3 mb-2">
                    <p className="text-gray-800 whitespace-pre-wrap">{enhancedData.enhanced_data}</p>
                </div>
                {enhancedData.sources && (
                    <div>
                        <h5 className="text-xs font-medium text-indigo-700 mb-1">Sources</h5>
                        <div className="bg-white rounded border border-indigo-100 p-2">
                            <p className="text-gray-600 text-xs">{enhancedData.sources}</p>
                        </div>
                    </div>
                )}
            </div>
        );
    }
    
    return (
        <div className="mt-4">
            <button 
                className="w-full py-2 text-center text-indigo-500 hover:bg-indigo-50 border border-indigo-200 rounded flex items-center justify-center"
                onClick={() => onEnhance(nodeId)}
            >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Enhance with AI
            </button>
        </div>
    );
};

export default NodeEnhancementPanel;