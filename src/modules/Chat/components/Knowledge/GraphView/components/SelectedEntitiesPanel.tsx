import React from 'react';
import { NodeDetails } from '../types';
import NodeDetailPanel from './NodeDetailPanel';
import { EnhancedNodeData } from '../types';

interface SelectedEntitiesPanelProps {
    selectedEntities: string[];
    rootEntity?: string;
    isValidUUID: (id: string | undefined) => boolean;
    connectMode: boolean;
    sourceNode: string | null;
    targetNode: string | null;
    expandedNode: string | null;
    selectedNodeDetails: Record<string, NodeDetails>;
    fetchingNodeDetails: boolean;
    nodeDetailsError: string | null;
    editingNode: string | null;
    editFormData: Record<string, string>;
    editFormError: string | null;
    isSaving: boolean;
    enhancedNodeData: Record<string, EnhancedNodeData>;
    enhancingNode: string | null;
    enhancementError: Record<string, string | null>;
    getNodeDisplayName: (nodeId: string, details?: NodeDetails) => string;
    onSelectNode: (nodeId: string) => void;
    onToggleNodeExpansion: (nodeId: string) => void;
    onFetchNodeDetails: (nodeId: string) => void;
    onStartEditing: (nodeId: string) => void;
    onSaveMetadataChanges: (nodeId: string) => void;
    onCancelEditing: () => void;
    onHandleFormChange: (key: string, value: string) => void;
    onAddMetadataField: () => void;
    onRemoveMetadataField: (key: string) => void;
    onEnhanceNode: (nodeId: string) => void;
}

const SelectedEntitiesPanel: React.FC<SelectedEntitiesPanelProps> = ({
    selectedEntities,
    rootEntity,
    isValidUUID,
    connectMode,
    sourceNode,
    targetNode,
    expandedNode,
    selectedNodeDetails,
    fetchingNodeDetails,
    nodeDetailsError,
    editingNode,
    editFormData,
    editFormError,
    isSaving,
    enhancedNodeData,
    enhancingNode,
    enhancementError,
    getNodeDisplayName,
    onSelectNode,
    onToggleNodeExpansion,
    onFetchNodeDetails,
    onStartEditing,
    onSaveMetadataChanges,
    onCancelEditing,
    onHandleFormChange,
    onAddMetadataField,
    onRemoveMetadataField,
    onEnhanceNode
}) => {
    // Helper to safely convert any value to string for display
    // const safeString = (value: unknown): string => {
    //     if (value === null || value === undefined) return '';
    //     return String(value);
    // };
    
    // Helper to safely get a value from the node_data.data object
    // const getDataValue = (details: NodeDetails | undefined, key: string): string | undefined => {
    //     if (!details?.node_data?.data) return undefined;
    //     if (typeof details.node_data.data !== 'object') return undefined;
        
    //     const dataObj = details.node_data.data as Record<string, unknown>;
    //     return typeof dataObj[key] === 'string' ? dataObj[key] as string : 
    //            dataObj[key] !== undefined ? String(dataObj[key]) : undefined;
    // };
    
    // Render a node in the selected entities panel
    const renderNodeItem = (nodeId: string, index: number) => {
        const details = selectedNodeDetails[nodeId];
        const isExpanded = expandedNode === nodeId;
        const isEditing = editingNode === nodeId;
        const isSourceNode = sourceNode === nodeId;
        const isTargetNode = targetNode === nodeId;
        
        // Fix the type issue by explicitly converting to string
        const nodeType = typeof details?.node_type === 'string' 
            ? details.node_type 
            : typeof details?.entity_type === 'string'
                ? details.entity_type
                : 'unknown';
        
        // Get display name with priority on meaningful names
        const nodeName = getNodeDisplayName(nodeId, details);
        
        // Add connect mode styling
        const nodeItemClasses = `mb-3 border ${
            isSourceNode ? 'border-blue-400 ring-2 ring-blue-300' : 
            isTargetNode ? 'border-green-400 ring-2 ring-green-300' : 
            'border-gray-200'
        } rounded-md overflow-hidden`;
        
        // Add connect mode action
        const handleNodeClick = () => {
            if (connectMode) {
                onSelectNode(nodeId);
            } else {
                onToggleNodeExpansion(nodeId);
            }
        };
        
        // Get node type icon
        const getNodeTypeIcon = () => {
            const typeString = String(nodeType).toLowerCase();
            
            switch (typeString) {
                case 'file':
                    return (
                        <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    );
                case 'object':
                    return (
                        <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    );
                case 'artifact':
                    return (
                        <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"></path>
                        </svg>
                    );
                default:
                    return (
                        <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    );
            }
        };
        
        // Get the technical ID to display (BMK, id, or similar)
        // const getTechnicalId = (): string | undefined => {
        //     // First check node_data.BMK
        //     if (details?.node_data?.BMK !== undefined) {
        //         return safeString(details.node_data.BMK);
        //     }
            
        //     // Then check nested data properties
        //     if (details?.node_data?.data && typeof details.node_data.data === 'object') {
        //         const dataObj = details.node_data.data as Record<string, unknown>;
                
        //         if (dataObj.BMK !== undefined) {
        //             return safeString(dataObj.BMK);
        //         }
                
        //         if (dataObj.id !== undefined) {
        //             return safeString(dataObj.id);
        //         }
        //     }
            
        //     return undefined;
        // };
        
        return (
            <li key={index} className={nodeItemClasses}>
                <div 
                    className={`flex items-center justify-between cursor-pointer hover:bg-gray-50 p-3 ${
                        isExpanded ? 'bg-gray-100' : 'bg-gray-50'
                    }`}
                    onClick={handleNodeClick}
                >
                    <div className="flex items-center flex-grow">
                        <div className="mr-2 flex-shrink-0">
                            {getNodeTypeIcon()}
                        </div>
                        <div className="flex flex-col flex-grow min-w-0">
                            {/* Enhanced name presentation */}
                            <div className="font-medium text-gray-800 truncate">{nodeName}</div>
                            {connectMode && (isSourceNode || isTargetNode) && (
                                <span className={`mt-1 px-2 py-0.5 text-xs rounded-full font-medium inline-block w-fit ${
                                    isSourceNode ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'
                                }`}>
                                    {isSourceNode ? 'Source' : 'Target'}
                                </span>
                            )}
                            <div className="text-xs text-gray-500 mt-0.5">
                                <span className="capitalize">{nodeType}</span>
                            </div>
                        </div>
                    </div>
                    <div className="flex items-center ml-2">
                        {fetchingNodeDetails && selectedNodeDetails[nodeId] === undefined && (
                            <div className="animate-spin rounded-full h-4 w-4 border border-t-transparent border-blue-500 mr-2"></div>
                        )}
                        
                        {!connectMode && (
                            <button 
                                className="text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded-full w-6 h-6 flex items-center justify-center focus:outline-none"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onToggleNodeExpansion(nodeId);
                                }}
                            >
                                <svg 
                                    className={`w-4 h-4 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
                                    fill="none" 
                                    stroke="currentColor" 
                                    viewBox="0 0 24 24" 
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                        )}
                    </div>
                </div>
                
                {isExpanded && !connectMode && (
                    <div className="p-3 text-sm border-t border-gray-200">
                     
                        
                        <NodeDetailPanel 
                            nodeId={nodeId}
                            details={details}
                            isLoading={fetchingNodeDetails && selectedNodeDetails[nodeId] === undefined}
                            error={nodeDetailsError}
                            isEditing={isEditing}
                            editFormData={editFormData}
                            editFormError={editFormError}
                            isSaving={isSaving}
                            enhancedData={enhancedNodeData[nodeId]}
                            isEnhancing={enhancingNode === nodeId}
                            enhancementError={enhancementError[nodeId] || null}
                            onStartEditing={() => onStartEditing(nodeId)}
                            onSaveChanges={() => onSaveMetadataChanges(nodeId)}
                            onCancelEditing={onCancelEditing}
                            onAddMetadataField={onAddMetadataField}
                            onRemoveMetadataField={onRemoveMetadataField}
                            onFieldChange={onHandleFormChange}
                            onKeyChange={(oldKey, newKey) => {
                                const value = editFormData[oldKey];
                                onRemoveMetadataField(oldKey);
                                onHandleFormChange(newKey, value);
                            }}
                            onFetchDetails={() => onFetchNodeDetails(nodeId)}
                            onEnhanceNode={() => onEnhanceNode(nodeId)}
                        />
                    </div>
                )}
            </li>
        );
    };

    // Render root entity section if available
    const renderRootEntity = () => {
        if (!rootEntity || !isValidUUID(rootEntity) || connectMode) return null;
        
        // Fix the type issue by explicitly converting to string
        const rootNodeName = getNodeDisplayName(rootEntity, selectedNodeDetails[rootEntity]);
        const rootNodeType = typeof selectedNodeDetails[rootEntity]?.node_type === 'string' 
            ? selectedNodeDetails[rootEntity].node_type 
            : typeof selectedNodeDetails[rootEntity]?.entity_type === 'string'
                ? selectedNodeDetails[rootEntity].entity_type
                : 'unknown';
        
        // Helper to safely check if node has data property and it's an object
        const hasDataObject = (): boolean => {
            return !!(
                selectedNodeDetails[rootEntity]?.node_data?.data && 
                typeof selectedNodeDetails[rootEntity].node_data.data === 'object'
            );
        };
        
        return (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
                <div className="flex justify-between items-center">
                    <div className="flex items-center flex-grow">
                        <div className="mr-2">
                            <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                            </svg>
                        </div>
                        <div className="flex flex-col">
                            {/* Prominently display name for root entity */}
                            <span className="font-medium text-blue-800">{rootNodeName}</span>
                            <div className="text-xs text-blue-600 flex items-center mt-0.5">
                                <span className="capitalize">{rootNodeType}</span>
                                <span className="ml-2">(Root)</span>
                            </div>
                        </div>
                    </div>
                    <div className="flex items-center">
                        {expandedNode === rootEntity && hasDataObject() && editingNode !== rootEntity && (
                            <button 
                                className="text-blue-500 hover:bg-blue-100 px-2 py-1 mr-2 rounded text-xs flex items-center"
                                onClick={() => onStartEditing(rootEntity)}
                            >
                                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                                </svg>
                                Edit
                            </button>
                        )}
                        <button 
                            className="text-blue-500 hover:bg-blue-200 rounded-full w-6 h-6 flex items-center justify-center focus:outline-none"
                            onClick={() => onToggleNodeExpansion(rootEntity)}
                        >
                            <svg 
                                className={`w-4 h-4 transform transition-transform ${expandedNode === rootEntity ? 'rotate-180' : ''}`} 
                                fill="none" 
                                stroke="currentColor" 
                                viewBox="0 0 24 24" 
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                
                {expandedNode === rootEntity && (
                    <div className="mt-3 border-t border-blue-200 pt-3">
                        {/* Show ID only in expanded details section for root entity */}
                        <div className="mb-3 p-2 bg-blue-100 rounded text-xs font-mono">
                            Root ID: {rootEntity}
                        </div>
                        
                        <NodeDetailPanel 
                            nodeId={rootEntity}
                            details={selectedNodeDetails[rootEntity]}
                            isLoading={fetchingNodeDetails && selectedNodeDetails[rootEntity] === undefined}
                            error={nodeDetailsError}
                            isEditing={editingNode === rootEntity}
                            editFormData={editFormData}
                            editFormError={editFormError}
                            isSaving={isSaving}
                            enhancedData={enhancedNodeData[rootEntity]}
                            isEnhancing={enhancingNode === rootEntity}
                            enhancementError={enhancementError[rootEntity] || null}
                            onStartEditing={() => onStartEditing(rootEntity)}
                            onSaveChanges={() => onSaveMetadataChanges(rootEntity)}
                            onCancelEditing={onCancelEditing}
                            onAddMetadataField={onAddMetadataField}
                            onRemoveMetadataField={onRemoveMetadataField}
                            onFieldChange={onHandleFormChange}
                            onKeyChange={(oldKey, newKey) => {
                                const value = editFormData[oldKey];
                                onRemoveMetadataField(oldKey);
                                onHandleFormChange(newKey, value);
                            }}
                            onFetchDetails={() => onFetchNodeDetails(rootEntity)}
                            onEnhanceNode={() => onEnhanceNode(rootEntity)}
                        />
                    </div>
                )}
            </div>
        );
    };

    // Empty state when no entities are selected
    if (selectedEntities.length === 0) {
        return (
            <div className="text-center py-10 text-gray-500">
                <svg className="w-12 h-12 mx-auto mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p>No entities selected</p>
                <p className="text-sm mt-1">
                    {connectMode ? 'Select entities in the graph to create connections' : 'Select entities in the graph to view details'}
                </p>
            </div>
        );
    }

    // Invalid entities state
    if (selectedEntities.filter(isValidUUID).length === 0) {
        return (
            <div className="text-center py-10 text-gray-500">
                <svg className="w-12 h-12 mx-auto mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p>No valid entities selected</p>
                <p className="text-sm mt-1">Selected entities don&apos;t have a UUID format</p>
                <p className="text-xs mt-2 max-w-md mx-auto">Only entities with IDs in the format &quot;xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx&quot; can be queried</p>
            </div>
        );
    }

    // Render list of selected entities
    return (
        <div>
            {renderRootEntity()}
            <ul className="list-none pl-0 space-y-3">
                {selectedEntities
                    .filter(isValidUUID)
                    .map((entity, index) => renderNodeItem(entity, index))}
            </ul>
        </div>
    );
};

export default SelectedEntitiesPanel;