import React from 'react';
import { NodeDetails } from './types';

// Helper to get node display name
export const getNodeDisplayName = (nodeId: string, nodeDetails?: NodeDetails): string => {
    if (!nodeDetails) {
        return `${nodeId.slice(0, 8)}...`;
    }
    
    // Get the node_data object
    const nodeData = nodeDetails.node_data || {};
    
    // First try to get name from node_data.name (top level name)
    if (typeof nodeData.name === 'string' && nodeData.name) {
        return nodeData.name;
    }
    
    // Then try to get name from node_data root (if it's directly in the object)
    if (typeof nodeDetails.name === 'string' && nodeDetails.name) {
        return nodeDetails.name;
    }
    
    // Check for data property (nested data)
    const nestedData = nodeData.data || {};
    
    // Priority order for fields in nested data:
    // 1. Bezeichnung - German descriptive name
    if (typeof nestedData.Bezeichnung === 'string' && nestedData.Bezeichnung) {
        return nestedData.Bezeichnung;
    }
    
    // 2. Artikelbezeichnung - German article description
    if (typeof nestedData.Artikelbezeichnung === 'string' && nestedData.Artikelbezeichnung) {
        return nestedData.Artikelbezeichnung;
    }
    
    // 3. name - General name field
    if (typeof nestedData.name === 'string' && nestedData.name) {
        return nestedData.name;
    }
    
    // 4. label - Common field for identification
    if (typeof nestedData.label === 'string' && nestedData.label) {
        return nestedData.label;
    }
    
    // 5. Hersteller - Manufacturer might be useful
    if (typeof nestedData.Hersteller === 'string' && nestedData.Hersteller) {
        return `${nestedData.Hersteller}${nestedData.Typnummer ? ` - ${nestedData.Typnummer}` : ''}`;
    }
    
    // 6. Typnummer - Model number
    if (typeof nestedData.Typnummer === 'string' && nestedData.Typnummer) {
        return nestedData.Typnummer;
    }
    
    // 7. id or BMK - Try ID fields which might have some meaning
    if (typeof nestedData.id === 'string' && nestedData.id) {
        // Extract meaningful part from id (after last dash or plus)
        const idMatch = nestedData.id.match(/[-+]([^-+]+)$/);
        return idMatch ? idMatch[1] : nestedData.id;
    }
    
    if (typeof nestedData.BMK === 'string' && nestedData.BMK) {
        // Extract meaningful part from BMK (after last dash)
        const bmkMatch = nestedData.BMK.match(/[-]([^-]+)$/);
        return bmkMatch ? bmkMatch[1] : nestedData.BMK;
    }
    
    // If we have entity_type, include it with ID for better context
    const entityType = nodeDetails.entity_type || nodeDetails.node_type;
    if (entityType) {
        const typeStr = String(entityType).charAt(0).toUpperCase() + String(entityType).slice(1);
        return `${typeStr}: ${nodeId.slice(0, 8)}...`;
    }
    
    // Fallback to displaying a shortened ID
    return `${nodeId.slice(0, 8)}...`;
};

// Format node details for display
export const formatNodeDetail = (value: unknown): React.ReactNode => {
    if (value === null || value === undefined) {
        return <span className="text-gray-400">null</span>;
    }
    
    if (typeof value === 'object') {
        return <span>{JSON.stringify(value)}</span>;
    }
    
    return <span>{String(value)}</span>;
};

// Helper function to check if an ID is a valid UUID
export const isValidUUID = (id: string | undefined): boolean => {
    // UUID format: 8-4-4-4-12 hexadecimal digits
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return typeof id === 'string' && uuidRegex.test(id);
};