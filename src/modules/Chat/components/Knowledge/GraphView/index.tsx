import React, { useState, useEffect, useCallback } from 'react';
import { isValidUUID, getNodeDisplayName } from './utils';
import ConnectionPanel from './components/ConnectionPanel';
import SelectedEntitiesPanel from './components/SelectedEntitiesPanel';
import AddNodeComponent from './components/AddNodeComponent';
import NodeTypeSelector from './components/NodeTypeSelector';
import { useGraphData } from './hooks/useGraphData';
import { useNodeDetails } from './hooks/useNodeDetails';
import { useNodeEnhancement } from './hooks/useNodeEnhancement';
import { useConnectionManager } from './hooks/useConnectionManager';
import { NodeData } from './types';

interface GraphViewProps {
    // Accept objectRef as a prop instead of creating internally
    objectRef: React.RefObject<HTMLObjectElement>;
}

const GraphView: React.FC<GraphViewProps> = ({ objectRef }) => {
    // Ensure the layout works with fixed-size components
    useEffect(() => {
        // Apply initial sizing and maintain WASM container size
        const resizeObserver = new ResizeObserver(() => {
            // Force the WASM app to maintain its size regardless of panel expansion
            if (objectRef.current) {
                objectRef.current.style.height = '40rem';
            }
        });
        
        // Start observing the document body for size changes
        if (document.body) {
            resizeObserver.observe(document.body);
        }
        
        return () => {
            resizeObserver.disconnect();
        };
    }, [objectRef]);
    // State for selected node types (initialize with only OBJECT type)
    const [selectedNodeTypes, setSelectedNodeTypes] = useState<string[]>(['object']);
    
    // State for node type selector panel - collapsed by default
    const [nodeTypeSelectorExpanded, setNodeTypeSelectorExpanded] = useState<boolean>(false);
    
    // State for handling new nodes
    const [newNodeAdded, setNewNodeAdded] = useState<boolean>(false);
    const [newNodeInfo, setNewNodeInfo] = useState<NodeData | null>(null);
    
    // State for selected entities
    const [selectedEntities, setSelectedEntities] = useState<string[]>([]);
    const [rootEntity, setRootEntity] = useState<string | undefined>(undefined);
    
    // State for current view type - default is dependency graph
    const [currentView, setCurrentView] = useState<'position_graph' | 'dependency_graph'>('dependency_graph');

    // Custom hooks
    const { 
        loading, 
        error, 
        updateGraphWithNewEdge 
    } = useGraphData(objectRef, selectedNodeTypes);
    
    const {
        selectedNodeDetails,
        fetchingNodeDetails,
        nodeDetailsError,
        expandedNode,
        editingNode,
        editFormData,
        editFormError,
        isSaving,
        fetchNodeDetails,
        toggleNodeExpansion,
        startEditing,
        cancelEditing,
        handleFormChange,
        addMetadataField,
        removeMetadataField,
        saveMetadataChanges
    } = useNodeDetails();
    
    const {
        enhancedNodeData,
        enhancingNode,
        enhancementError,
        enhanceNode
    } = useNodeEnhancement();
    
    const {
        connectMode,
        sourceNode,
        targetNode,
        edgeType,
        availableEdgeTypes,
        creatingEdge,
        connectionError,
        customEdgeType,
        showCustomEdgeInput,
        connectionStep,
        showTypeSelector,
        createEdge,
        toggleConnectMode,
        selectNodeForConnection,
        resetConnectionSelection,
        setEdgeType,
        setCustomEdgeType,
        setShowCustomEdgeInput,
        setShowTypeSelector
    } = useConnectionManager(objectRef, updateGraphWithNewEdge);

    // Function to change graph view
    const changeGraphView = useCallback((viewType: 'position_graph' | 'dependency_graph') => {
        if (objectRef.current?.contentWindow?.wasm_app) {
            const wasmApp = objectRef.current.contentWindow.wasm_app;
            
            // Use bracket notation to access the method without TypeScript complaining
            if ('change_view' in wasmApp && typeof wasmApp['change_view'] === 'function') {
                wasmApp['change_view'](viewType);
                setCurrentView(viewType);
                
                // Show info message about view change
                if (typeof wasmApp.send_info_message === 'function') {
                    wasmApp.send_info_message(
                        `Switched to ${viewType.replace('_', ' ')} view`
                    );
                }
            }
        }
    }, [objectRef]);

    const handleNodeAdded = (nodeData: NodeData) => {
        console.log('New node added:', nodeData);
        setNewNodeAdded(true);
        setNewNodeInfo(nodeData);
        
        // Show info message in the WASM app if it's available
        if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
            objectRef.current.contentWindow.wasm_app.send_info_message(
                `New node created: ${nodeData.name || 'Unnamed'} (${nodeData.type || 'unknown'})`
            );
        }
        
        // In a real implementation, you'd add the node to the graph data
        // and then refresh the visualization
        setTimeout(() => {
            setNewNodeAdded(false);
        }, 5000);
    };

    // Handle node type selection change
    const handleNodeTypeChange = (types: string[]) => {
        setSelectedNodeTypes(types);
    };

    // Add polling to check for selected entities and handle graph interactions
    useEffect(() => {
        // Only set up polling if we're not loading and have no error
        if (!loading && !error && objectRef.current) {
            // Function to check for selected entities, root entity, and handle selections for connections
            const checkGraphInteractions = async () => {
                if (objectRef.current?.contentWindow?.wasm_app) {
                    try {
                        // Get selected entities
                        const selected = objectRef.current.contentWindow.wasm_app.get_selected_entities();
                        
                        // Filter for valid UUIDs
                        const validSelected = selected.filter(isValidUUID);
                        
                        // Handle connection mode selection
                        if (connectMode) {
                            // Process node selection for connection based on the selected entities
                            if (validSelected.length === 1) {
                                const selectedNode = validSelected[0];
                                
                                // Check if this is a new node selection for our source/target process
                                if (connectionStep === 'source' && selectedNode !== sourceNode) {
                                    // If we're on source step and selected a node that's not already our source
                                    
                                    // Fetch node details if we don't have them
                                    if (!selectedNodeDetails[selectedNode]) {
                                        fetchNodeDetails(selectedNode);
                                    }
                                    
                                    // Process the node selection for connection
                                    selectNodeForConnection(selectedNode);
                                    
                                    // Send info message
                                    if (objectRef.current.contentWindow.wasm_app.send_info_message) {
                                        const displayName = getNodeDisplayName(selectedNode, selectedNodeDetails[selectedNode]);
                                        objectRef.current.contentWindow.wasm_app.send_info_message(
                                            `Selected "${displayName}" as source. Now select a target node.`
                                        );
                                    }
                                } 
                                else if (connectionStep === 'target' && selectedNode !== sourceNode && selectedNode !== targetNode) {
                                    // If we're on target step and selected a node that's not our source and not already our target
                                    
                                    // Fetch node details if we don't have them
                                    if (!selectedNodeDetails[selectedNode]) {
                                        fetchNodeDetails(selectedNode);
                                    }
                                    
                                    // Process the node selection for connection
                                    selectNodeForConnection(selectedNode);
                                    
                                    // Send info message
                                    if (objectRef.current.contentWindow.wasm_app.send_info_message) {
                                        const displayName = getNodeDisplayName(selectedNode, selectedNodeDetails[selectedNode]);
                                        objectRef.current.contentWindow.wasm_app.send_info_message(
                                            `Selected "${displayName}" as target. Now choose a connection type.`
                                        );
                                    }
                                }
                            }
                        }
                        
                        // Check if the selection has changed for panel display
                        const hasSelectionChanged = 
                            validSelected.length !== selectedEntities.length || 
                            validSelected.some(id => !selectedEntities.includes(id));
                        
                        if (hasSelectionChanged) {
                            setSelectedEntities(validSelected || []);
                            
                            // Fetch details for all newly selected entities
                            const newEntities = validSelected.filter(id => 
                                !selectedEntities.includes(id) && !selectedNodeDetails[id]
                            );
                            
                            // Fetch details for new entities
                            for (const nodeId of newEntities) {
                                fetchNodeDetails(nodeId);
                            }
                        }
                        
                        // Get root entity
                        const root = objectRef.current.contentWindow.wasm_app.get_root_entity();
                        if (root && root !== rootEntity && isValidUUID(root)) {
                            setRootEntity(root);
                            
                            // Fetch root entity details if it's valid and we don't have it yet
                            if (!selectedNodeDetails[root]) {
                                fetchNodeDetails(root);
                            }
                        }
                    } catch (error) {
                        console.error('Error checking graph interactions:', error);
                    }
                }
            };
            
            // Set up polling interval
            const interval = setInterval(checkGraphInteractions, 200); // Faster polling for better responsiveness
            
            // Initial check
            checkGraphInteractions();
            
            // Clean up interval on unmount
            return () => clearInterval(interval);
        }
    }, [
        loading, 
        error, 
        selectedEntities, 
        rootEntity, 
        selectedNodeDetails, 
        connectMode, 
        sourceNode, 
        targetNode, 
        connectionStep, 
        fetchNodeDetails, 
        selectNodeForConnection,
        objectRef // Added missing dependency
    ]);

    // Check if the WASM path needs to be updated based on your project structure
    const wasmPath = '/wasm/index.html';
    
    // Helper function for handling node selection in connection mode
    const handleNodeSelection = useCallback((nodeId: string) => {
        selectNodeForConnection(nodeId);
    }, [selectNodeForConnection]);

    // Handle source node change
    const handleChangeSource = useCallback(() => {
        // Clear source selection and reset to step 1
        if (sourceNode && objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
            objectRef.current.contentWindow.wasm_app.highlight_node(sourceNode, false);
        }
        resetConnectionSelection();
        
        // Send info message
        if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
            objectRef.current.contentWindow.wasm_app.send_info_message(
                "Source node cleared. Select a new source node."
            );
        }
    }, [sourceNode, objectRef, resetConnectionSelection]);

    // Handle target node change
    const handleChangeTarget = useCallback(() => {
        // Clear target selection and go back to step 2
        if (targetNode && objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
            objectRef.current.contentWindow.wasm_app.highlight_node(targetNode, false);
        }
        
        if (sourceNode && objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
            // Keep source node highlighted
            objectRef.current.contentWindow.wasm_app.highlight_node(sourceNode, true, "#3b82f6");
        }
        
        // Reset only target, preserve source
        const currentSource = sourceNode;
        resetConnectionSelection();
        selectNodeForConnection(currentSource!);
        
        // Send info message
        if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
            objectRef.current.contentWindow.wasm_app.send_info_message(
                "Target node cleared. Click on a node to select a target."
            );
        }
    }, [targetNode, sourceNode, objectRef, resetConnectionSelection, selectNodeForConnection]);

    // Wrapper function to get node display name with access to node details
    const getNodeDisplayNameWithDetails = useCallback((nodeId: string) => {
        return getNodeDisplayName(nodeId, selectedNodeDetails[nodeId]);
    }, [selectedNodeDetails]);

    return (
        <div className="flex flex-col h-full gap-4">
            {/* Node Type Selector */}
            <NodeTypeSelector 
                selectedNodeTypes={selectedNodeTypes}
                onChange={handleNodeTypeChange}
                isLoading={loading}
                isExpanded={nodeTypeSelectorExpanded}
                onToggleExpand={() => setNodeTypeSelectorExpanded(!nodeTypeSelectorExpanded)}
            />
            
            {/* Action Buttons */}
            <div className="mb-4 flex justify-between">
                <div className="flex space-x-4">
                    {/* Include the AddNodeComponent */}
                    <AddNodeComponent onNodeAdded={handleNodeAdded} />
                    
                    <button
                        className={`px-4 py-2 rounded flex items-center ${
                            connectMode 
                                ? 'bg-indigo-600 text-white hover:bg-indigo-700' 
                                : 'bg-blue-600 text-white hover:bg-blue-700'
                        }`}
                        onClick={toggleConnectMode}
                    >
                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                        </svg>
                        {connectMode ? 'Exit Connection Mode' : 'Create Connection'}
                    </button>
                </div>
                
                <div className="flex items-center">
                    {/* View Toggle Button - on the right side */}
                    <button
                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
                        onClick={() => changeGraphView(currentView === 'dependency_graph' ? 'position_graph' : 'dependency_graph')}
                    >
                        {currentView === 'dependency_graph' ? (
                            <>
                                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zm0 8a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zm12 0a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                                </svg>
                                Change to Position View
                            </>
                        ) : (
                            <>
                                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                Change to Dependency View
                            </>
                        )}
                    </button>
                    
                    {/* Add Node Success Notification */}
                    {newNodeAdded && newNodeInfo && (
                        <div className="ml-4 px-4 py-2 bg-green-100 text-green-800 rounded-md flex items-center">
                            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Node created
                        </div>
                    )}
                </div>
            </div>
            
            {/* Graph visualization with relative positioning for overlay elements */}
            {/* Increased height by 1/3 and using fixed height to prevent shrinking */}
            <div className="w-full h-[40rem] flex-shrink-0 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden relative">
                {/* Connection Panel Overlay (only shown in connect mode) */}
                <ConnectionPanel 
                    connectMode={connectMode}
                    showTypeSelector={showTypeSelector}
                    sourceNode={sourceNode || ""}
                    targetNode={targetNode || ""}
                    edgeType={edgeType}
                    availableEdgeTypes={availableEdgeTypes}
                    showCustomEdgeInput={showCustomEdgeInput}
                    customEdgeType={customEdgeType}
                    connectionStep={connectionStep}
                    connectionError={connectionError || ""}
                    creatingEdge={creatingEdge}
                    getNodeDisplayName={getNodeDisplayNameWithDetails}
                    onToggleConnectMode={toggleConnectMode}
                    onCreateEdge={createEdge}
                    onReset={resetConnectionSelection}
                    onShowTypeSelector={() => setShowTypeSelector(true)}
                    onEdgeTypeChange={setEdgeType}
                    onShowCustomEdgeInput={setShowCustomEdgeInput}
                    onCustomEdgeTypeChange={setCustomEdgeType}
                    onChangeSource={handleChangeSource}
                    onChangeTarget={handleChangeTarget}
                />
                
                {/* Loading spinner */}
                {loading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                )}
                
                {/* Error message */}
                {error && (
                    <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
                        <div className="text-red-500 p-4 bg-red-100 rounded-md">
                            Error loading graph data: {error}
                        </div>
                    </div>
                )}
                
                {/* Connection mode instructions overlay */}
                {connectMode && !showTypeSelector && (
                    <div className="absolute bottom-4 left-4 right-4 bg-indigo-900 bg-opacity-90 text-white p-3 rounded-lg z-10 text-center">
                        <p className="text-sm">
                            {connectionStep === 'source' && "👆 Select a node in the graph to use as the source"}
                            {connectionStep === 'target' && "👆 Now select a different node to use as the target"}
                            {connectionStep === 'type' && "Almost done! Click 'Continue' to choose a connection type"}
                        </p>
                    </div>
                )}
                
                {/* The WebAssembly graph visualization */}
                <object
                    type="text/html"
                    data={wasmPath}
                    className="w-full h-full"
                    id="app"
                    ref={objectRef}
                />
            </div>
            
            {/* Selected Entities Panel with Node Details */}
            {/* Doubled the height with fixed height to ensure proper sizing */}
            <div className="mt-4 p-4 bg-gray-100 rounded-lg shadow-sm border border-gray-200 h-[72rem] overflow-auto">
                <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">
                        {connectMode && !showTypeSelector
                            ? (connectionStep === 'source' 
                                ? 'Click on a node in the graph to select source' 
                                : connectionStep === 'target' 
                                    ? 'Click on a node in the graph to select target'
                                    : 'Selected Nodes')
                            : 'Selected Entities'
                        }
                    </h3>
                    {selectedEntities.length > 0 && (
                        <div className="flex space-x-2">
                            <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
                                {selectedEntities.length} total
                            </span>
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                {selectedEntities.filter(isValidUUID).length} valid
                            </span>
                        </div>
                    )}
                </div>
                
                <SelectedEntitiesPanel 
                    selectedEntities={selectedEntities}
                    rootEntity={rootEntity}
                    isValidUUID={isValidUUID}
                    connectMode={connectMode}
                    sourceNode={sourceNode}
                    targetNode={targetNode}
                    expandedNode={expandedNode}
                    selectedNodeDetails={selectedNodeDetails}
                    fetchingNodeDetails={fetchingNodeDetails}
                    nodeDetailsError={nodeDetailsError}
                    editingNode={editingNode}
                    editFormData={editFormData}
                    editFormError={editFormError}
                    isSaving={isSaving}
                    enhancedNodeData={enhancedNodeData}
                    enhancingNode={enhancingNode}
                    enhancementError={enhancementError}
                    getNodeDisplayName={getNodeDisplayNameWithDetails}
                    onSelectNode={handleNodeSelection}
                    onToggleNodeExpansion={toggleNodeExpansion}
                    onFetchNodeDetails={fetchNodeDetails}
                    onStartEditing={startEditing}
                    onSaveMetadataChanges={saveMetadataChanges}
                    onCancelEditing={cancelEditing}
                    onHandleFormChange={handleFormChange}
                    onAddMetadataField={addMetadataField}
                    onRemoveMetadataField={removeMetadataField}
                    onEnhanceNode={enhanceNode}
                />
            </div>
        </div>
    );
};

export default GraphView;