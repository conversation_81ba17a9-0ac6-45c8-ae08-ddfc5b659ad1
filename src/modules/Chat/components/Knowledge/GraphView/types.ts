// Define interfaces for the graph data
export interface NodeData {
    name?: string;
    entity_id?: string;
    data?: Record<string, unknown>;
    [key: string]: unknown;
  }
  
  export interface GraphNode {
    id: string;
    name: string;
    type: string;
    node_data: NodeData;
  }
  
  export interface GraphEdge {
    source: string;
    target: string;
    type: string;
    id?: string; // Optional ID for the edge
  }
  
  export interface GraphResponse {
    nodes: GraphNode[];
    edges: GraphEdge[];
  }
  
  export enum GraphNodeType {
    ENTITY = 'ENTITY',
    DOCUMENT = 'DOCUMENT',
    TABLE = 'TABLE',
    OBJECT = 'OBJECT',
    FILE = 'FILE',
    INSTANCE = 'INSTANCE'
  }
  
  // Interface for node details from the get_node endpoint
  export interface NodeDetails {
    id: string;
    node_data?: {
      data?: Record<string, unknown>;
      [key: string]: unknown;
    };
    [key: string]: unknown;
  }
  
  // Interface for enhanced node data
  export interface EnhancedNodeData {
    enhanced_data: string;
    sources: string;
    status: string;
  }
  
  // Interface for new edge creation
  export interface NewEdgeData {
    source_id: string;
    target_id: string;
    edge_type: string;
  }
  
  // Extend Window interface to include wasm_app
  declare global {
    interface Window {
      wasm_app?: {
        send_info_message: (message: string) => void;
        new_graph_data: (data: string) => void;
        get_selected_entities: () => string[];
        get_root_entity: () => string | undefined;
        add_edge: (source: string, target: string, type: string) => void;
        highlight_node: (nodeId: string, highlight: boolean, color?: string) => void;
        set_connection_mode: (active: boolean) => void;
      };
    }
  }