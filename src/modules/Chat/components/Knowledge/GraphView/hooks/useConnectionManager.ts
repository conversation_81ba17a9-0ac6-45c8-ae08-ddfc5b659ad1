import { useState, useCallback, RefObject } from 'react';
import cookies from 'js-cookie';
import { ApiRequest } from '@/fetch/FetchRequest';
import { <PERSON>ieName } from '@/enums/CookieName';
import { captureError } from '@/utils/errorHandling';
import { GraphEdge } from '../types';

// Interface for create relation request
// Interface for create relation query parameters
interface CreateRelationParams {
    source_id: string;
    target_id: string;
    relation_type: string;
    is_bidirectional?: boolean;
}

// Interface for create relation response
interface CreateRelationResponse {
    status: string;
    message: string;
    relation_type: string;
    is_bidirectional: boolean;
}

export const useConnectionManager = (objectRef: RefObject<HTMLObjectElement>, updateGraphWithNewEdge: (edge: GraphEdge) => void) => {
    // State for creating connections between nodes
    const [connectMode, setConnectMode] = useState<boolean>(false);
    const [sourceNode, setSourceNode] = useState<string | null>(null);
    const [targetNode, setTargetNode] = useState<string | null>(null);
    const [edgeType, setEdgeType] = useState<string>("related_to");
    const [availableEdgeTypes] = useState<string[]>([
        "related_to", "depends_on", "contains", "references", "created_by", "is_a", "parent_of"
    ]);
    const [creatingEdge, setCreatingEdge] = useState<boolean>(false);
    const [connectionError, setConnectionError] = useState<string | null>(null);
    const [customEdgeType, setCustomEdgeType] = useState<string>("");
    const [showCustomEdgeInput, setShowCustomEdgeInput] = useState<boolean>(false);
    const [connectionStep, setConnectionStep] = useState<'source' | 'target' | 'type'>('source');
    const [showTypeSelector, setShowTypeSelector] = useState<boolean>(false);

    // Helper function to get node display name (placeholder - should be implemented or passed as a prop)
    const getNodeDisplayName = useCallback((nodeId: string): string => {
        // This is a placeholder - the actual implementation will depend on your app structure
        return `${nodeId.slice(0, 8)}...`;
    }, []);

    // Function to reset connection selection
    const resetConnectionSelection = useCallback(() => {
        // Clear any highlighting in the graph
        if (sourceNode && objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
            objectRef.current.contentWindow.wasm_app.highlight_node(sourceNode, false);
        }
        if (targetNode && objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
            objectRef.current.contentWindow.wasm_app.highlight_node(targetNode, false);
        }
        
        setSourceNode(null);
        setTargetNode(null);
        setConnectionError(null);
        setConnectionStep('source');
        setShowTypeSelector(false);
        
        // Send an info message to the WASM app
        if (connectMode && objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
            objectRef.current.contentWindow.wasm_app.send_info_message(
                "Selection reset. Select a node to use as the source"
            );
        }
    }, [sourceNode, targetNode, objectRef, connectMode]);
    
    // Function to create a new edge between nodes
    const createEdge = useCallback(async () => {
        if (!sourceNode || !targetNode) {
            setConnectionError("Both source and target nodes must be selected");
            return;
        }
        
        // Use custom edge type if selected, otherwise use the selected predefined type
        const finalEdgeType = showCustomEdgeInput ? customEdgeType : edgeType;
        
        if (!finalEdgeType) {
            setConnectionError("Edge type must be specified");
            return;
        }
        
        try {
            setCreatingEdge(true);
            setConnectionError(null);
            
            const bearer = cookies.get(CookieName.API_TOKEN);
            
            // Prepare the parameters for creating a relation
            const params: CreateRelationParams = {
                source_id: sourceNode,
                target_id: targetNode,
                relation_type: finalEdgeType,
                is_bidirectional: true // Default to bidirectional relations
            };
            
            console.log('Creating new relation:', params);
            
            // Send info message to the WASM app
            if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
                objectRef.current.contentWindow.wasm_app.send_info_message(
                    `Creating connection: ${getNodeDisplayName(sourceNode)} → ${finalEdgeType} → ${getNodeDisplayName(targetNode)}...`
                );
            }
            
            // Build query parameters for the source_id and target_id
            const queryParams = new URLSearchParams();
            queryParams.append('source_id', sourceNode);
            queryParams.append('target_id', targetNode);
            queryParams.append('relation_type', finalEdgeType);
            queryParams.append('is_bidirectional', 'true');
            
            // Make API request to create the relation using query parameters
            const { data, response } = await ApiRequest<CreateRelationResponse>(
                `graph_query/create_relation?${queryParams.toString()}`, 
                {
                    bearer,
                    init: { 
                        method: "POST"
                        // No body needed as we're using query parameters
                    }
                }
            );
            
            if (response?.ok && data) {
                console.log('Relation created successfully:', data);
                
                // Create a new edge object to update the graph visualization
                const newEdge: GraphEdge = {
                    source: sourceNode,
                    target: targetNode,
                    type: finalEdgeType,
                    // We don't have an ID from the response, but you could generate one if needed
                    id: `${sourceNode}_${finalEdgeType}_${targetNode}`
                };
                
                // Update graph data with the new edge
                try {
                    updateGraphWithNewEdge(newEdge);
                } catch (error) {
                    console.error('Error updating graph with new edge:', error);
                    
                    // Try direct WASM app update as fallback
                    try {
                        if (objectRef.current?.contentWindow?.wasm_app?.add_edge) {
                            objectRef.current.contentWindow.wasm_app.add_edge(sourceNode, targetNode, finalEdgeType);
                        }
                    } catch (edgeError) {
                        console.error('Error adding edge to WASM app:', edgeError);
                        
                        // Show a message to the user about refreshing
                        if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
                            objectRef.current.contentWindow.wasm_app.send_info_message(
                                "Connection created in database. Please refresh the page to see it in the graph."
                            );
                        }
                    }
                }
                
                // Clear highlighting from the nodes
                if (objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
                    objectRef.current.contentWindow.wasm_app.highlight_node(sourceNode, false);
                    objectRef.current.contentWindow.wasm_app.highlight_node(targetNode, false);
                }
                
                // Reset the connection state
                resetConnectionSelection();
                setShowTypeSelector(false);
                setConnectionStep('source');
                
                if (showCustomEdgeInput) {
                    setCustomEdgeType("");
                    setShowCustomEdgeInput(false);
                }
                
                // Show success message
                if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
                    objectRef.current.contentWindow.wasm_app.send_info_message(
                        `Connection created successfully: ${getNodeDisplayName(sourceNode)} → ${finalEdgeType} → ${getNodeDisplayName(targetNode)}`
                    );
                }
                
                return data;
            } else {
                throw new Error(`Failed to create relation: ${response?.statusText || 'Unknown error'}`);
            }
        } catch (err) {
            console.error('Error creating relation:', err);
            captureError(err, {
                action: 'createRelation',
                component: 'GraphView',
                sourceNode,
                targetNode,
                relationType: finalEdgeType
            });
            setConnectionError(err instanceof Error ? err.message : 'Failed to create connection');
            
            // Show error message in the WASM app
            if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
                objectRef.current.contentWindow.wasm_app.send_info_message(
                    `Error creating connection: ${err instanceof Error ? err.message : 'Unknown error'}`
                );
            }
            
            return null;
        } finally {
            setCreatingEdge(false);
        }
    }, [
        sourceNode, 
        targetNode, 
        edgeType, 
        showCustomEdgeInput, 
        customEdgeType, 
        objectRef, 
        getNodeDisplayName, 
        updateGraphWithNewEdge,
        resetConnectionSelection
    ]);

    // Toggle connect mode
    const toggleConnectMode = useCallback(() => {
        const newConnectMode = !connectMode;
        setConnectMode(newConnectMode);
        
        // Notify the WASM app about the connection mode change if the method exists
        if (objectRef.current?.contentWindow?.wasm_app?.set_connection_mode) {
            objectRef.current.contentWindow.wasm_app.set_connection_mode(newConnectMode);
        }
        
        if (!newConnectMode) {
            // Clear connection data when exiting connect mode
            resetConnectionSelection();
            
            // Clear any highlighting in the graph
            if (sourceNode && objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
                objectRef.current.contentWindow.wasm_app.highlight_node(sourceNode, false);
            }
            if (targetNode && objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
                objectRef.current.contentWindow.wasm_app.highlight_node(targetNode, false);
            }
        } else {
            // Reset to first step when entering connect mode
            setConnectionStep('source');
            setShowTypeSelector(false);
            
            // Send an info message to the WASM app
            if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
                objectRef.current.contentWindow.wasm_app.send_info_message(
                    "Connection Mode: Click on a node to select it as the source"
                );
            }
        }
    }, [connectMode, objectRef, sourceNode, targetNode, resetConnectionSelection]);

    // Helper function to set a node as source or target based on current state
    const selectNodeForConnection = useCallback((nodeId: string) => {
        if (!connectMode) return;
        
        if (connectionStep === 'source') {
            setSourceNode(nodeId);
            setConnectionStep('target');
            
            // Highlight the source node in blue
            if (objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
                objectRef.current.contentWindow.wasm_app.highlight_node(nodeId, true, "#3b82f6"); // blue-500
            }
        } else if (connectionStep === 'target') {
            // Don't allow self-connections
            if (nodeId === sourceNode) {
                setConnectionError("Cannot create self-connection. Source and target must be different nodes.");
                
                // Send an error message to the WASM app
                if (objectRef.current?.contentWindow?.wasm_app?.send_info_message) {
                    objectRef.current.contentWindow.wasm_app.send_info_message(
                        "Error: Cannot connect a node to itself. Please select a different target node."
                    );
                }
                return;
            }
            
            setTargetNode(nodeId);
            setConnectionStep('type');
            setShowTypeSelector(true);
            
            // Highlight the target node in green
            if (objectRef.current?.contentWindow?.wasm_app?.highlight_node) {
                objectRef.current.contentWindow.wasm_app.highlight_node(nodeId, true, "#10b981"); // emerald-500
            }
        }
    }, [connectMode, connectionStep, sourceNode, objectRef]);


    return {
        connectMode,
        sourceNode,
        targetNode,
        edgeType,
        availableEdgeTypes,
        creatingEdge,
        connectionError,
        customEdgeType,
        showCustomEdgeInput,
        connectionStep,
        showTypeSelector,
        createEdge,
        toggleConnectMode,
        selectNodeForConnection,
        resetConnectionSelection,
        setEdgeType,
        setCustomEdgeType,
        setShowCustomEdgeInput,
        setShowTypeSelector
    };
};