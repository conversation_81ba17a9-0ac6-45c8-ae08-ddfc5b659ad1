import { useState, useCallback } from 'react';
import cookies from 'js-cookie';
import { ApiRequest } from '@/fetch/FetchRequest';
import { CookieName } from '@/enums/CookieName';
import { captureError } from '@/utils/errorHandling';
import { NodeDetails } from '../types';

export const useNodeDetails = () => {
    // State for node details
    const [selectedNodeDetails, setSelectedNodeDetails] = useState<Record<string, NodeDetails>>({});
    const [fetchingNodeDetails, setFetchingNodeDetails] = useState<boolean>(false);
    const [nodeDetailsError, setNodeDetailsError] = useState<string | null>(null);
    const [expandedNode, setExpandedNode] = useState<string | null>(null);
    
    // State for editing metadata
    const [editingNode, setEditingNode] = useState<string | null>(null);
    const [editFormData, setEditFormData] = useState<Record<string, string>>({});
    const [editFormError, setEditFormError] = useState<string | null>(null);
    const [isSaving, setIsSaving] = useState<boolean>(false);

    // Function to fetch node details from the get_node endpoint
    const fetchNodeDetails = useCallback(async (nodeId: string) => {
        try {
            setFetchingNodeDetails(true);
            setNodeDetailsError(null);
            
            const bearer = cookies.get(CookieName.API_TOKEN);
            const queryParams = new URLSearchParams();
            queryParams.append('node_id', nodeId);
            
            console.log(`Fetching details for node: ${nodeId}`);
            
            const { data, response } = await ApiRequest<NodeDetails>("graph_query/get_node", {
                bearer,
                init: { method: "GET" },
                params: queryParams
            });
            
            if (response?.ok && data) {
                console.log(`Node details received for ${nodeId}:`, data);
                
                // Update the selectedNodeDetails state with the new data
                setSelectedNodeDetails(prev => ({
                    ...prev,
                    [nodeId]: data
                }));
                
                return data;
            } else {
                throw new Error(`Failed to fetch node details: ${response?.statusText || 'Unknown error'}`);
            }
        } catch (err) {
            console.error(`Error fetching node details for ${nodeId}:`, err);
            captureError(err, {
                action: 'fetchNodeDetails',
                component: 'GraphView',
                nodeId
            });
            setNodeDetailsError(err instanceof Error ? err.message : 'Unknown error occurred');
            return null;
        } finally {
            setFetchingNodeDetails(false);
        }
    }, []);

    // Toggle node expansion to show/hide details
    const toggleNodeExpansion = useCallback((nodeId: string) => {
        setExpandedNode(prev => prev === nodeId ? null : nodeId);
    }, []);
    
    // Function to start editing a node's metadata
    const startEditing = useCallback((nodeId: string) => {
        const details = selectedNodeDetails[nodeId];
        if (!details?.node_data?.data) {
            // Cannot edit if no data is available
            setEditFormError("No metadata available to edit");
            return;
        }

        // Initialize form with current metadata
        const initialFormData: Record<string, string> = {};
        Object.entries(details.node_data.data).forEach(([key, value]) => {
            initialFormData[key] = typeof value === 'string' ? value : JSON.stringify(value);
        });
        
        setEditFormData(initialFormData);
        setEditingNode(nodeId);
        setEditFormError(null);
    }, [selectedNodeDetails]);

    // Function to cancel editing
    const cancelEditing = useCallback(() => {
        setEditingNode(null);
        setEditFormData({});
        setEditFormError(null);
    }, []);

    // Function to handle form field changes
    const handleFormChange = useCallback((key: string, value: string) => {
        setEditFormData(prev => ({
            ...prev,
            [key]: value
        }));
    }, []);

    // Function to add a new metadata field
    const addMetadataField = useCallback(() => {
        setEditFormData(prev => ({
            ...prev,
            [`new_field_${Object.keys(prev).length}`]: ""
        }));
    }, []);

    // Function to remove a metadata field
    const removeMetadataField = useCallback((key: string) => {
        setEditFormData(prev => {
            const newData = { ...prev };
            delete newData[key];
            return newData;
        });
    }, []);

    // Function to save changes
    const saveMetadataChanges = useCallback(async (nodeId: string) => {
        try {
            setIsSaving(true);
            setEditFormError(null);
            
            // Prepare data for submission
            const updatedData = { ...editFormData };
            
            // Try to parse JSON values where applicable
            Object.keys(updatedData).forEach(key => {
                const value = updatedData[key];
                try {
                    // Only parse values that look like objects or arrays
                    if ((value.startsWith('{') && value.endsWith('}')) || 
                        (value.startsWith('[') && value.endsWith(']'))) {
                        updatedData[key] = JSON.parse(value);
                    }
                } catch (e) {
                    // Keep as string if parsing fails
                    console.warn(`Failed to parse JSON for key ${key}:`, e);
                }
            });
            
            const bearer = cookies.get(CookieName.API_TOKEN);
            
            console.log(`Updating metadata for node: ${nodeId}`, updatedData);
            
            // Make API request to update node
            const { response } = await ApiRequest("graph_query/update_node", {
                bearer,
                init: { 
                    method: "POST",
                    body: JSON.stringify({
                        node_id: nodeId,
                        node_data: {
                            data: updatedData
                        }
                    })
                }
            });
            
            if (response?.ok) {
                console.log(`Node metadata updated successfully for ${nodeId}`);
                
                // Refresh node details
                await fetchNodeDetails(nodeId);
                
                // Exit edit mode
                setEditingNode(null);
                setEditFormData({});
            } else {
                throw new Error(`Failed to update node metadata: ${response?.statusText || 'Unknown error'}`);
            }
        } catch (err) {
            console.error(`Error updating node metadata for ${nodeId}:`, err);
            captureError(err, {
                action: 'saveMetadataChanges',
                component: 'GraphView',
                nodeId
            });
            setEditFormError(err instanceof Error ? err.message : 'Failed to save changes');
        } finally {
            setIsSaving(false);
        }
    }, [editFormData, fetchNodeDetails]);

    return {
        selectedNodeDetails,
        fetchingNodeDetails,
        nodeDetailsError,
        expandedNode,
        editingNode,
        editFormData,
        editFormError,
        isSaving,
        fetchNodeDetails,
        toggleNodeExpansion,
        startEditing,
        cancelEditing,
        handleFormChange,
        addMetadataField,
        removeMetadataField,
        saveMetadataChanges
    };
};