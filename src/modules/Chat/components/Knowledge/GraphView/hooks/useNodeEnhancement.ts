import { useState, useCallback } from 'react';
import cookies from 'js-cookie';
import { ApiRequest } from '@/fetch/FetchRequest';
import { CookieName } from '@/enums/CookieName';
import { captureError } from '@/utils/errorHandling';
import { EnhancedNodeData } from '../types';

export const useNodeEnhancement = () => {
    // State for node enhancement
    const [enhancedNodeData, setEnhancedNodeData] = useState<Record<string, EnhancedNodeData>>({});
    const [enhancingNode, setEnhancingNode] = useState<string | null>(null);
    const [enhancementError, setEnhancementError] = useState<Record<string, string | null>>({});

    // Function to enhance a node using the enhance_node endpoint
    const enhanceNode = useCallback(async (nodeId: string) => {
        try {
            setEnhancingNode(nodeId);
            setEnhancementError(prev => ({ ...prev, [nodeId]: null }));
            
            const bearer = cookies.get(CookieName.API_TOKEN);
            const queryParams = new URLSearchParams();
            queryParams.append('node_id', nodeId);
            
            console.log(`Enhancing node: ${nodeId}`);
            
            const { data, response } = await ApiRequest<EnhancedNodeData>("graph_query/enhance_node", {
                bearer,
                init: { method: "GET" },
                params: queryParams
            });
            
            if (response?.ok && data) {
                console.log(`Node enhancement received for ${nodeId}:`, data);
                
                // Update the enhancedNodeData state with the new data
                setEnhancedNodeData(prev => ({
                    ...prev,
                    [nodeId]: data
                }));
                
                return data;
            } else {
                throw new Error(`Failed to enhance node: ${response?.statusText || 'Unknown error'}`);
            }
        } catch (err) {
            console.error(`Error enhancing node ${nodeId}:`, err);
            captureError(err, {
                action: 'enhanceNode',
                component: 'GraphView',
                nodeId
            });
            setEnhancementError(prev => ({ 
                ...prev, 
                [nodeId]: err instanceof Error ? err.message : 'Unknown error occurred'
            }));
            return null;
        } finally {
            setEnhancingNode(null);
        }
    }, []);

    return {
        enhancedNodeData,
        enhancingNode,
        enhancementError,
        enhanceNode
    };
};