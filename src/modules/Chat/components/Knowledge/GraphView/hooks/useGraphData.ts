import { useState, useEffect, RefObject, useCallback } from 'react';
import cookies from 'js-cookie';
import { ApiRequest } from '@/fetch/FetchRequest';
import { CookieName } from '@/enums/CookieName';
import { captureError } from '@/utils/errorHandling';
import { GraphResponse } from '../types';

export const useGraphData = (
  objectRef: RefObject<HTMLObjectElement>, 
  nodeTypes: string[] = ['artifact', 'entity', 'identifier', 'object', 'file', 'table', 'instance']
) => {
    const [graphData, setGraphData] = useState<GraphResponse | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const loadGraphData = async () => {
            if (nodeTypes.length === 0) {
                setError("Please select at least one node type");
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                setError(null);
                
                const bearer = cookies.get(CookieName.API_TOKEN);
                
                // Create URL with properly formatted query parameters
                // We need to manually create the query string to ensure multiple node_types are sent correctly
                const nodeTypesQueryString = nodeTypes.map(type => `node_types=${encodeURIComponent(type)}`).join('&');
                const url = `graph_query/get_graph_view?${nodeTypesQueryString}&base_relation=all&metadata_only=false`;
                
                console.log('Fetching graph data:', {
                    endpoint: url,
                    nodeTypes: nodeTypes
                });
                
                // Use ApiRequest with the manually constructed URL
                const { data, response } = await ApiRequest<GraphResponse>(
                    url, 
                    {
                        bearer,
                        init: {
                            method: "GET",
                            headers: {
                                "Accept": "application/json"
                            }
                        }
                    }
                );
                
                // Check if the request was successful
                if (response?.ok && data) {
                    console.log("Raw Fetch Response:", data);
                    setGraphData(data);
                    
                    // Send data to WASM app if available
                    if (objectRef.current?.contentWindow?.wasm_app) {
                        objectRef.current.contentWindow.wasm_app.new_graph_data(JSON.stringify(data));
                    }
                } else {
                    throw new Error(`Failed to fetch graph data: ${response?.statusText || 'Unknown error'}`);
                }
            } catch (err) {
                console.error('Failed to load graph data:', err);
                captureError(err, {
                    action: 'loadGraphData',
                    component: 'GraphView'
                });
                setError(err instanceof Error ? err.message : 'Unknown error occurred');
            } finally {
                setLoading(false);
            }
        };
        
        loadGraphData();
    }, [objectRef, nodeTypes]); // Add nodeTypes as a dependency

    // Function to send data to WASM app when it becomes available
    useEffect(() => {
        // Check if we have graph data and need to send it to WASM
        if (graphData && objectRef.current) {
            const checkAndSendData = () => {
                if (objectRef.current?.contentWindow?.wasm_app) {
                    try {
                        console.log('Sending graph data to WASM app:', {
                            appFound: true,
                            dataSize: JSON.stringify(graphData).length,
                            nodes: graphData.nodes?.length || 0,
                            edges: graphData.edges?.length || 0
                        });
                        
                        // Send the complete graph data to the WASM app
                        objectRef.current.contentWindow.wasm_app.new_graph_data(
                            JSON.stringify(graphData)
                        );
                        
                        console.log('Graph data successfully sent to WASM app');
                        return true;
                    } catch (error) {
                        console.error('Error sending graph data to WASM app:', error);
                        return false;
                    }
                }
                console.log('WASM app not ready yet, will retry...');
                return false;
            };
            
            if (!checkAndSendData()) {
                const interval = setInterval(() => {
                    if (checkAndSendData()) {
                        clearInterval(interval);
                    }
                }, 500);
                
                return () => clearInterval(interval);
            }
        }
    }, [graphData, objectRef]);

    const updateGraphWithNewEdge = useCallback((newEdge: { source: string; target: string; type: string; id?: string }) => {
        if (graphData) {
            const updatedGraphData = {
                ...graphData,
                edges: [...graphData.edges, newEdge]
            };
            
            setGraphData(updatedGraphData);
            
            if (objectRef.current?.contentWindow?.wasm_app) {
                if (objectRef.current.contentWindow.wasm_app.add_edge) {
                    objectRef.current.contentWindow.wasm_app.add_edge(
                        newEdge.source, 
                        newEdge.target, 
                        newEdge.type
                    );
                } else {
                    objectRef.current.contentWindow.wasm_app.new_graph_data(
                        JSON.stringify(updatedGraphData)
                    );
                }
            }
        }
    }, [graphData, objectRef]);

    return {
        graphData,
        loading,
        error,
        updateGraphWithNewEdge
    };
};