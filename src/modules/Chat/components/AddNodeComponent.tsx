import React, { useState } from 'react';
import { Plus, X, Save } from 'lucide-react';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    Di<PERSON>Footer,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { t } from "@/i18n";
import { useToast } from "@/hooks/use-toast";

// Define the node type options
const NODE_TYPES = [
    "artifact",
    "document",
    "entity",
    "concept",
    "person",
    "organization"
];

// Define node data interface
interface NodeData {
    name?: string;
    entity_id?: string;
    data?: Record<string, unknown>;
    [key: string]: unknown;
  }

interface AddNodeProps {
    onNodeAdded?: (nodeData: NodeData) => void;
}

export default function AddNodeComponent({ onNodeAdded }: AddNodeProps) {
    const [showDialog, setShowDialog] = useState(false);
    const [nodeName, setNodeName] = useState("");
    const [nodeType, setNodeType] = useState("artifact");
    const [properties, setProperties] = useState<{ key: string; value: string }[]>([
        { key: "name", value: "" }
    ]);
    const [isSaving, setIsSaving] = useState(false);
    const { toast } = useToast();

    // Reset the form state
    const resetForm = () => {
        setNodeName("");
        setNodeType("artifact");
        setProperties([{ key: "name", value: "" }]);
    };

    // Handle opening the dialog
    const handleOpenDialog = () => {
        resetForm();
        setShowDialog(true);
    };

    // Handle adding a new property
    const addProperty = () => {
        setProperties([...properties, { key: "", value: "" }]);
    };

    // Handle removing a property
    const removeProperty = (index: number) => {
        const updatedProperties = [...properties];
        updatedProperties.splice(index, 1);
        setProperties(updatedProperties);
    };

    // Handle property key change
    const handlePropertyKeyChange = (index: number, value: string) => {
        const updatedProperties = [...properties];
        updatedProperties[index].key = value;
        setProperties(updatedProperties);
    };

    // Handle property value change
    const handlePropertyValueChange = (index: number, value: string) => {
        const updatedProperties = [...properties];
        updatedProperties[index].value = value;
        setProperties(updatedProperties);
    };

    // Mock creating a new node
    const handleCreateNode = () => {
        try {
            setIsSaving(true);

            // Create properties object from array
            const propertiesObject: Record<string, string> = {};
            properties.forEach(prop => {
                if (prop.key.trim()) {
                    propertiesObject[prop.key.trim()] = prop.value;
                }
            });

            // Update the name property if it exists
            const nameProperty = properties.find(p => p.key === 'name');
            if (nameProperty) {
                nameProperty.value = nodeName;
            }

            // Create mock node data
            const newNodeData: NodeData = {
                id: `node-${Date.now()}`,  // Generate a temporary ID
                type: nodeType,
                name: nodeName,
                node_data: {
                    data: propertiesObject
                }
            };

            console.log("Creating new node:", newNodeData);

            // Show success message
            toast({
                title: t("Node created"),
                description: t("New node has been created successfully."),
            });

            // Close dialog
            setShowDialog(false);

            // Call the onNodeAdded callback if provided
            if (onNodeAdded) {
                onNodeAdded(newNodeData);
            }
        } catch (error) {
            console.error("Error creating node:", error);
            toast({
                variant: "destructive",
                title: t("Failed to create node"),
                description: error instanceof Error ? error.message : t("Unknown error occurred"),
            });
        } finally {
            setIsSaving(false);
        }
    };

    return (
        <>
            <Button
                className="px-4 py-2 bg-green-600 text-white hover:bg-green-700 flex items-center"
                onClick={handleOpenDialog}
            >
                <Plus className="w-5 h-5 mr-2" />
                {t("Add Node")}
            </Button>

            <Dialog open={showDialog} onOpenChange={setShowDialog}>
                <DialogContent className="sm:max-w-[500px]">
                    <DialogHeader>
                        <DialogTitle>{t("Create New Node")}</DialogTitle>
                        <DialogDescription>
                            {t("Add a new node to the knowledge graph")}
                        </DialogDescription>
                    </DialogHeader>

                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="nodeName" className="text-right">
                                {t("Node Name")}
                            </Label>
                            <Input
                                id="nodeName"
                                value={nodeName}
                                onChange={(e) => setNodeName(e.target.value)}
                                className="col-span-3"
                                placeholder={t("Enter a name for the node")}
                            />
                        </div>

                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="nodeType" className="text-right">
                                {t("Node Type")}
                            </Label>
                            <Select
                                value={nodeType}
                                onValueChange={setNodeType}
                            >
                                <SelectTrigger className="col-span-3">
                                    <SelectValue placeholder={t("Select a node type")} />
                                </SelectTrigger>
                                <SelectContent>
                                    {NODE_TYPES.map((type) => (
                                        <SelectItem key={type} value={type}>
                                            {type}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="border rounded-md p-3">
                            <div className="flex justify-between items-center mb-4">
                                <Label className="font-medium">
                                    {t("Node Properties")}
                                </Label>
                                <Button
                                    type="button"
                                    size="sm"
                                    variant="outline"
                                    onClick={addProperty}
                                >
                                    <Plus className="h-4 w-4 mr-1" />
                                    {t("Add Property")}
                                </Button>
                            </div>

                            <div className="space-y-2 max-h-60 overflow-y-auto pr-2">
                                {properties.map((property, index) => (
                                    <div key={index} className="flex gap-2 items-center">
                                        <Input
                                            placeholder={t("Key")}
                                            value={property.key}
                                            onChange={(e) => handlePropertyKeyChange(index, e.target.value)}
                                            className="flex-1"
                                            disabled={property.key === 'name'} // Disable editing the name key
                                        />
                                        <Input
                                            placeholder={t("Value")}
                                            value={property.value}
                                            onChange={(e) => handlePropertyValueChange(index, e.target.value)}
                                            className="flex-1"
                                            disabled={property.key === 'name'} // Disable direct editing of name value, use nodeName instead
                                        />
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="p-0 h-9 w-9"
                                            onClick={() => removeProperty(index)}
                                            disabled={property.key === 'name'} // Prevent removing the name property
                                        >
                                            <X className="h-4 w-4" />
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => setShowDialog(false)}
                            disabled={isSaving}
                        >
                            {t("Cancel")}
                        </Button>
                        <Button
                            onClick={handleCreateNode}
                            disabled={!nodeName.trim() || isSaving}
                            className="flex items-center"
                        >
                            {isSaving ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                                    {t("Creating...")}
                                </>
                            ) : (
                                <>
                                    <Save className="h-4 w-4 mr-2" />
                                    {t("Create Node")}
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
}