"use client";

import { FiSearch, FiCode, FiEdit3, FiPlusCircle } from "react-icons/fi";
import { CONFIGURE_STORAGE_KEY, configureDefaults } from "@/modules/Chat/components/Knowledge";
import { useCallback } from "react";
import { useMessageInput } from "../../hooks/MessageInput";

const features = [
    {
        title: "Code Analysis",
        description: "Get information about your codebase",
        icon: <FiCode className="w-5 h-5 text-[#2563EB]" />,
        prompt: "I have a bug in deicing process, where should I start looking for it?",
        collectionName: "Sita_acdm",
    },
    {
        title: "Search",
        description: "Search for definitions, examples, and more",
        icon: <FiSearch className="w-5 h-5 text-[#2563EB]" />,
        prompt: "Find all deicing parameters and list it in a table",
        collectionName: "Sita_acdm",
    },
    {
        title: "Documentation",
        description: "Describe the logic behind the deicing process",
        icon: <FiEdit3 className="w-5 h-5 text-[#2563EB]" />,
        prompt: "Write documentation of CoreParameter",
        collectionName: "Sita_acdm",
    },

    {
        title: "Rewrite",
        description: "Rewrite code to other languages or frameworks",
        icon: <FiPlusCircle className="w-5 h-5 text-[#2563EB]" />,
        prompt: "Rewrite identifier mapping to java",
        collectionName: "Sita_acdm",
    },
];

export default function ProductivitySection() {
    const { setMessage } = useMessageInput();

    const handleFeatureClick = useCallback(
        (prompt: string, collectionName: string) => {
            // Update message using the context
            setMessage(prompt);

            // Update collection in localStorage
            const currentConfig = JSON.parse(localStorage.getItem(CONFIGURE_STORAGE_KEY) || JSON.stringify(configureDefaults));
            if (currentConfig.queryParams) {
                currentConfig.queryParams.collection_id = collectionName;
                localStorage.setItem(CONFIGURE_STORAGE_KEY, JSON.stringify(currentConfig));
            }

            // Update collection selector button text
            const spans = document.querySelectorAll(".messages-input-container button span");
            spans.forEach((span) => {
                if (span.textContent?.includes("Collection:")) {
                    const collectionSpan = span.nextElementSibling as HTMLElement;
                    if (collectionSpan) {
                        collectionSpan.textContent = collectionName;
                    }
                }
            });
        },
        [setMessage],
    );

    return (
        <div className="flex-auto basis-8/12">
            <div className="grid grid-cols-2 gap-6 full-w">
                {features.map((feature) => (
                    <div
                        key={feature.title}
                        className="p-6 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer flex items-start gap-4"
                        onClick={() => handleFeatureClick(feature.prompt, feature.collectionName)}
                    >
                        <div className="p-2.5 rounded-lg bg-blue-50">{feature.icon}</div>
                        <div>
                            <h3 className="text-base font-medium text-gray-900 mb-1">{feature.title}</h3>
                            <p className="text-sm text-gray-500">{feature.description}</p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
}
