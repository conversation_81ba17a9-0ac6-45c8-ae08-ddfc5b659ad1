import "client-only";
import { Chat, ChatContext, ChatMessage } from "@/modules/Chat/contexts/ChatContextProvider";
import { useContext, useEffect, useState } from "react";

export function useChats() {
    return useContext(ChatContext);
}

export function useCurrentChat(): Chat | null {
    const [currentChat, setCurrentChat] = useState<Chat | null>(null);
    const {
        state: { currentChatId, chats },
    } = useChats();

    useEffect(() => {
        if (currentChatId === null || chats[currentChatId] === undefined) {
            setCurrentChat(null);
        } else {
            setCurrentChat(chats[currentChatId]);
        }
    }, [chats, currentChatId]);

    return currentChat;
}

export function useCurrentMessage(): ChatMessage | null {
    const {
        state: { currentMessageId },
    } = useChats();
    const currentChat = useCurrentChat();
    const [currentMessage, setCurrentMessage] = useState<ChatMessage | null>(null);

    useEffect(() => {
        if (currentMessageId === null || currentChat === null || currentChat.messages[currentMessageId] === undefined) {
            setCurrentMessage(null);
        } else {
            setCurrentMessage(currentChat.messages[currentMessageId]);
        }
    }, [currentMessageId, currentChat]);

    return currentMessage;
}
