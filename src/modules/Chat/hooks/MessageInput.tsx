"use client";

import React, { createContext, useContext, useState, useCallback } from 'react';

type MessageInputContextType = {
    message: string;
    setMessage: (message: string) => void;
    isActive: boolean;
};

const MessageInputContext = createContext<MessageInputContextType | undefined>(undefined);

export function MessageInputProvider({ children }: { children: React.ReactNode }) {
    const [message, setMessage] = useState('');
    const [isActive, setIsActive] = useState(false);

    const handleSetMessage = useCallback((newMessage: string) => {
        setMessage(newMessage);
        setIsActive(newMessage.trim().length > 0);
    }, []);

    return (
        <MessageInputContext.Provider value={{ 
            message, 
            setMessage: handleSetMessage,
            isActive 
        }}>
            {children}
        </MessageInputContext.Provider>
    );
}

export function useMessageInput() {
    const context = useContext(MessageInputContext);
    if (context === undefined) {
        throw new Error('useMessageInput must be used within a MessageInputProvider');
    }
    return context;
}
