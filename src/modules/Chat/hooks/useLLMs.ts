import { useQuery } from '@tanstack/react-query';
import { listEmbeddingsAndLlms } from '@/fetch/api/Chat';
import { AppComboboxOption } from '@/components/AppCombobox';

export const useLLMs = () => {
    const { data: llms, isLoading } = useQuery({
        queryKey: ['llms'],
        async queryFn() {
            try {
                const { llms } = await listEmbeddingsAndLlms();
                return llms.map(
                    (llm) => ({
                        label: llm,
                        value: llm,
                        disabled: llm !== 'gpt-4o-mini'
                    }) as AppComboboxOption,
                );
            } catch (error) {
                console.error('Failed to load LLMs:', error);
                return [];
            }
        }
    });

    return {
        llms: llms || [],
        isLoading
    };
};
