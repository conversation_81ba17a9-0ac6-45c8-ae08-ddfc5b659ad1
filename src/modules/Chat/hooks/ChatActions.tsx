import "client-only";
import { useCallback } from "react";
import { useChats, useCurrentChat } from "./ChatState";
import { ChatAction, ChatMessage } from "../contexts/ChatContextProvider";
import { useChatApi } from "./ChatApi";
import { v4 as uuid } from "uuid";
import { ChatRoles, MessageKind } from "@/fetch/api/Chat";

function convertToChatEntry(message: ChatMessage) {
    const entries = [];

    // Add user's query message if it exists
    if (message.query) {
        entries.push({
            role: ChatRoles.USER,
            message: {
                kind: MessageKind.TEXT,
                content: message.query
            }
        });
    }

    // Add chatbot's response if it exists
    if (message.result) {
        entries.push({
            role: ChatRoles.CHATBOT,
            message: {
                kind: MessageKind.TEXT,
                content: message.result.result
            }
        });
    }

    return entries;
}

export function useChatActions() {
    const currentChat = useCurrentChat();
    const { dispatch } = useChats();
    const { askMessageMutation } = useChatApi();

    const processAsk = useCallback(
        async (params: { chatId: string; messageId: string; query: string }) => {
            const { chatId, messageId, query } = params;

            // Get all messages from the current chat
            const chatMessages = {
                messages: currentChat?.messages 
                    ? Object.values(currentChat.messages)
                        .filter((msg): msg is ChatMessage => msg !== undefined)
                        .sort((a, b) => a.createdAt - b.createdAt) // Sort by creation time
                        .flatMap(convertToChatEntry) // Use flatMap to handle array of entries
                    : []
            };
            chatMessages.messages = chatMessages.messages.slice(-4);



            const response = await askMessageMutation.mutateAsync({
                query,
                chat_messages: chatMessages
            });

            if (response === null) {
                dispatch({ type: ChatAction.SET_MESSAGE_ERROR_STATE, payload: { chatId, messageId, error: true } });
                return false;
            }

            dispatch({ type: ChatAction.SET_MESSAGE_RESPONSE, payload: { chatId, messageId, response } });
            return true;
        },
        [askMessageMutation, currentChat?.messages, dispatch],
    );

    const sendAskMessage = useCallback(
        async (query: string) => {
            if (!query) {
                return;
            }

            if (!currentChat) {
                return;
            }

            const { chatId } = currentChat;
            const messageId = uuid();

            dispatch({ type: ChatAction.CREATE_MESSAGE, payload: { chatId, messageId, query } });

            if (!(await processAsk({ chatId, messageId, query }))) {
                return;
            }
        },
        [currentChat, dispatch, processAsk],
    );

    const retry = useCallback(
        async (messageId: string, query: string) => {
            if (!query) {
                return;
            }

            if (!currentChat) {
                return;
            }

            dispatch({
                type: ChatAction.SET_MESSAGE_ERROR_STATE,
                payload: {
                    chatId: currentChat.chatId,
                    messageId,
                    error: false,
                },
            });

            await processAsk({ chatId: currentChat.chatId, messageId, query });
        },
        [currentChat, dispatch, processAsk],
    );

    return {
        sendAskMessage,
        retry,
    };
}
