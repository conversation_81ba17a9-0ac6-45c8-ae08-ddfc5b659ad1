"use client";

import { useSession } from 'next-auth/react';
import { useCallback, useEffect, useState } from 'react';
import { AskConfigParams } from '@/fetch/api/Chat';

type ConfigPath =
    | ['rag_config', 'temperature' | 'llm' | 'top_k' | 'seed']
    | ['queryParams', 'collection_id']
    | ['alpha']
    | ['additional_context']
    | ['useExperimentalApi']; // Add new path for API version toggle

export const CONFIGURE_STORAGE_KEY = "config";

export const configureDefaults: AskConfigParams & { useExperimentalApi?: boolean } = {
    rag_config: {
        temperature: 0,
        llm: "gpt-4o-mini",
        top_k: 10,
        seed: null,
    },
    additional_context: "",
    alpha: 0,
    queryParams: {
        collection_id: "",
    },
    useExperimentalApi: false, // Add default value for API version toggle
};

export function useConfiguration() {
    const { data: session, status } = useSession();
    const [config, setConfig] = useState<AskConfigParams & { useExperimentalApi?: boolean }>(configureDefaults);

    // Get user-specific storage key
    const storageKey = session?.user?.email
        ? `${CONFIGURE_STORAGE_KEY}_${session.user.email}`
        : CONFIGURE_STORAGE_KEY;

    // Load configuration on mount and when session changes
    useEffect(() => {
        console.log('Configuration effect triggered:', {
            status,
            email: session?.user?.email,
            storageKey
        });

        if (status === 'loading') {
            console.log('Session still loading, waiting...');
            return;
        }

        if (status === 'unauthenticated') {
            console.log('User unauthenticated, clearing configuration');
            setConfig(configureDefaults);
            localStorage.removeItem(CONFIGURE_STORAGE_KEY);
            return;
        }

        try {
            console.log('Loading configuration for authenticated user');
            const savedConfig = localStorage.getItem(storageKey);
            if (savedConfig) {
                console.log('Found saved configuration:', JSON.parse(savedConfig));
                setConfig(JSON.parse(savedConfig));
            } else {
                console.log('No saved configuration found, using defaults');
                setConfig(configureDefaults);
            }
        } catch (error) {
            console.error('Error loading configuration:', error);
            setConfig(configureDefaults);
        }
    }, [status, storageKey]);

    // Update configuration
    const updateConfigValue = useCallback((newConfig: AskConfigParams & { useExperimentalApi?: boolean }) => {
        console.log('Updating configuration:', {
            status,
            path,
            value,
            storageKey
        });
        setConfig(prev => {
            const newConfig = { ...prev };
            if (path.length === 1) {
                (newConfig as any)[path[0]] = value;
            } else {
                const [parent, child] = path;
                (newConfig as any)[parent] = {
                    ...(newConfig as any)[parent],
                    [child]: value
                };
            }
            if (status === 'authenticated') {
                localStorage.setItem(storageKey, JSON.stringify(newConfig));
            }
            console.log('Updated configuration:', newConfig);
            return newConfig;
        });
    }, [status, storageKey]);

    // Update a specific path in the configuration
    const updateConfigPath = useCallback(<T>(path: ConfigPath, value: T) => {
        console.log('Updating config path:', { path, value });
        setConfig(prevConfig => {
            const newConfig = { ...prevConfig };
            let current: any = newConfig;

            // Navigate to the nested property
            for (let i = 0; i < path.length - 1; i++) {
                current = current[path[i]];
            }

            // Set the value
            current[path[path.length - 1]] = value;

            // Save to localStorage if authenticated
            if (status === 'authenticated') {
                localStorage.setItem(storageKey, JSON.stringify(newConfig));
            }

            return newConfig;
        });
    }, [status, storageKey]);

    // Update just the collection ID
    const updateCollectionId = useCallback((collectionId: string | null) => {
        console.log('Updating collection ID:', {
            status,
            currentId: config.queryParams.collection_id,
            newId: collectionId,
            storageKey
        });
        const newConfig = {
            ...config,
            queryParams: {
                ...config.queryParams,
                collection_id: collectionId || ""
            }
        };
        updateConfigValue(newConfig);
    }, [config, updateConfigValue]);

    return {
        config,
        updateConfig: updateConfigValue,
        updateConfigPath,
        updateCollectionId,
        isAuthenticated: status === 'authenticated'
    };
}
