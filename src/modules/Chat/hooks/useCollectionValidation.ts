import { useQuery } from '@tanstack/react-query';
import { listEmbeddingVectorCollections } from '@/fetch/api/Chat';
import { useEffect } from 'react';
import { useConfiguration } from './useConfiguration';

export const useCollectionValidation = () => {
    const { config, updateConfig, isAuthenticated } = useConfiguration();

    // Fetch collections
    const { data: collections } = useQuery({
        queryKey: ['embeddingCollections'],
        queryFn: listEmbeddingVectorCollections,
        enabled: isAuthenticated,
        refetchOnWindowFocus: true,
    });

    useEffect(() => {
        console.log('Collection validation effect:', {
            isAuthenticated,
            hasCollections: !!collections,
            currentCollectionId: config.queryParams?.collection_id,
            collectionsCount: collections?.length
        });

        if (!isAuthenticated || !collections) return;

        const currentCollectionId = config.queryParams?.collection_id;
        const collectionExists = currentCollectionId && collections.some(c => c.id === currentCollectionId);
        const hasCollections = collections.length > 0;

        // Only update if we need to:
        // 1. When there's no current collection but collections exist
        // 2. When the current collection doesn't exist in available collections
        if ((!currentCollectionId && hasCollections) || (currentCollectionId && !collectionExists)) {
            console.log('Updating collection ID due to validation:', {
                currentCollectionId,
                availableCollections: collections.map(c => c.id),
                newCollectionId: collections[0]?.id
            });
            updateConfig(['queryParams', 'collection_id'] as const, collections[0]?.id || '');
        }
    }, [collections, config.queryParams?.collection_id, isAuthenticated, updateConfig]);

    return { collections };
};
