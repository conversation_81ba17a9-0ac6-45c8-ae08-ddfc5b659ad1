import "client-only";
import { AskConfigParams, SendMessagePayload, askMessage, filesUpload } from "@/fetch/api/Chat";

import { useMutation } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { CONFIGURE_STORAGE_KEY, configureDefaults, useConfiguration } from "./useConfiguration";
import { useAskQuestion } from "@/api/qa/qa"; // Import Orval-generated hook
import type {
    BodyAskQuestion,
    AskQuestionParams,
    AvailableModels,
    SearchConfig,
    LLMInferenceConfig,
    RagConfig
} from "@/api/fastAPI.schemas"; // Import types
import { useApiVersion } from '../contexts/ApiVersionContext';
import { useChatToast } from "./ChatToast";

export type AskMessageParams = Pick<SendMessagePayload, "query" | "chat_messages"> & { collection_id?: string };

export function useChatApi() {
    const { data: session } = useSession();
    const { config } = useConfiguration();
    const { useExperimentalApi } = useApiVersion(); // Use the context for immediate updates
    const { unableToUploadFilesToast, fileUploadSuccess } = useChatToast();

    // Use the Orval-generated hook for experimental API
    const askQuestionOrval = useAskQuestion();

    // Traditional mutation for the stable API
    const askMessageMutation = useMutation({
        async mutationFn(params: AskMessageParams) {
            // If experimental API is enabled, use Orval-generated hook
            if (useExperimentalApi) { // Use the context value instead of config
                console.log("Using experimental API via Orval");
                // Create the llm_config object with the correct structure
                const llm_config: LLMInferenceConfig = {
                    temperature_scale: config.rag_config.temperature,
                    llm: config.rag_config.llm as AvailableModels,
                    seed: config.rag_config.seed
                };

                /////
                // Create the search_config object with the correct structure
                const search_config: SearchConfig = {
                    top_k: config.rag_config.top_k
                };

                // Create the rag_config object with the correct structure
                const rag_config: RagConfig = {
                    llm_config,
                    search_config
                };
                try {
                    // Create properly typed payload for Orval
                    const orvalData: BodyAskQuestion = {
                        query: params.query,
                        rag_config,
                        // Handle chat_messages correctly
                        ...(params.chat_messages ? {
                            context: { chat_messages: params.chat_messages }
                        } : {})
                    };

                    // Get collection_id from config, ensure it's not empty
                    const collection_id = config.queryParams.collection_id || "default";

                    console.log("Using collection_id:", collection_id);

                    // Create properly typed params for Orval
                    const orvalParams: AskQuestionParams = {
                        collection_id: collection_id
                    };

                    console.log("Sending request with data:", JSON.stringify(orvalData, null, 2));
                    console.log("Using params:", JSON.stringify(orvalParams, null, 2));

                    const response = await askQuestionOrval.mutateAsync({
                        data: orvalData,
                        params: orvalParams
                    });

                    return response.data;
                } catch (error) {
                    console.error("Error using experimental API:", error);
                    throw error;
                }
            } else {
                // Use the stable API
                console.log("Using stable API");
                try {
                    // Get user-specific storage key
                    const storageKey = session?.user?.email ? `config_${session.user.email}` : CONFIGURE_STORAGE_KEY;
                    console.log(params);
                    console.log("storage key", storageKey);
                    const config = JSON.parse(localStorage.getItem(storageKey) || JSON.stringify(configureDefaults)) as AskConfigParams;
                    console.log(config);
                    const collection_id = config.queryParams.collection_id;

                    // @TODO - add control to proper handle seed value
                    config.rag_config.seed = null;

                    const result = await askMessage({
                        ...config,
                        ...params,
                        collection_id, // add this line
                    });

                    return result;
                } catch (error) {
                    console.error("Error using stable API:", error);
                    throw error;
                }
            }
        },
    });

    // File upload mutation
    const fileUploadMutation = useMutation({
        async mutationFn(files: File[]) {
            try {
                const result = await filesUpload(files);
                fileUploadSuccess();
                return result;
            } catch (error) {
                unableToUploadFilesToast();
                throw error;
            }
        },
    });

    return {
        askMessageMutation,
        fileUploadMutation
    };
}
