import "client-only";
import { useToast } from "@/hooks/use-toast";
import { t } from "@/i18n";
import { useCallback } from "react";

export function useChatToast() {
    const { toast } = useToast();

    const fileUploadSuccess = useCallback(
        () =>
            toast({
                variant: "default",
                title: t("File upload"),
                description: t("File uploaded successfully"),
            }),
        [toast],
    );

    const unableToSendMessageToast = useCallback(
        () =>
            toast({
                variant: "destructive",
                description: t("Unable to send ask message. Try again later."),
            }),
        [toast],
    );

    const unableToUploadFilesToast = useCallback(
        () =>
            toast({
                variant: "destructive",
                description: t("Unable to upload files. Try again later."),
            }),
        [toast],
    );

    const failedToLoadConfigureData = useCallback(
        () =>
            toast({
                variant: "destructive",
                description: t("Unable to load configuration data. Try again later."),
            }),
        [toast],
    );

    return {
        unableToSendMessageToast,
        failedToLoadConfigureData,
        unableToUploadFilesToast,
        fileUploadSuccess,
    };
}
